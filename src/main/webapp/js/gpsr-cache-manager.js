/**
 * GPSR打印标签缓存管理器
 *
 * 功能特性：
 * 1. 智能预加载GPSR标签，提升打印速度
 * 2. 内存缓存管理，避免内存溢出
 * 3. 缓存过期机制，确保数据新鲜度
 * 4. 降级保护，兼容原有打印方式
 * 5. 性能监控和错误处理
 * 6. 支持多平台GPSR标签（SMT、Amazon、Fruugo等）
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-06-30
 */

(function(window) {
    'use strict';

    // 全局配置
    var CONFIG = {
        CACHE_MAX_SIZE: 10,           // 最大缓存数量
        CACHE_EXPIRE_TIME: 5 * 60 * 1000,  // 缓存过期时间（5分钟）
        RETRY_MAX_COUNT: 2,           // 最大重试次数
        RETRY_DELAY: 1000,            // 重试延迟（毫秒）
        PERFORMANCE_LOG: true,        // 是否启用性能日志
        DEBUG_MODE: false             // 调试模式
    };

    // 缓存存储
    var gpsrCache = {
        data: {},

        /**
         * 设置缓存
         * @param {string} apvNo - 发货单号
         * @param {object} cacheData - 缓存数据
         */
        set: function(apvNo, cacheData) {
            // 检查缓存大小，清理最旧的缓存
            var keys = Object.keys(this.data);
            if (keys.length >= CONFIG.CACHE_MAX_SIZE) {
                var oldestKey = keys.reduce(function(oldest, key) {
                    return !oldest || this.data[key].timestamp < this.data[oldest].timestamp ? key : oldest;
                }.bind(this));

                if (oldestKey) {
                    delete this.data[oldestKey];
                    this._log('缓存已满，清理最旧缓存：' + oldestKey);
                }
            }

            this.data[apvNo] = {
                content: cacheData.content,
                pdfUrls: cacheData.pdfUrls || [],
                timestamp: Date.now(),
                platform: cacheData.platform
            };

            this._log('缓存已设置：' + apvNo);
        },

        /**
         * 获取缓存
         * @param {string} apvNo - 发货单号
         * @returns {object|null} 缓存数据或null
         */
        get: function(apvNo) {
            var cached = this.data[apvNo];
            if (!cached) {
                return null;
            }

            // 检查是否过期
            if (Date.now() - cached.timestamp > CONFIG.CACHE_EXPIRE_TIME) {
                delete this.data[apvNo];
                this._log('缓存已过期，已清理：' + apvNo);
                return null;
            }

            return cached;
        },

        /**
         * 清理指定缓存
         * @param {string} apvNo - 发货单号
         */
        remove: function(apvNo) {
            if (this.data[apvNo]) {
                delete this.data[apvNo];
                this._log('缓存已清理：' + apvNo);
            }
        },

        /**
         * 清理所有缓存
         */
        clear: function() {
            this.data = {};
            this._log('所有缓存已清理');
        },

        /**
         * 清理过期缓存
         */
        cleanExpired: function() {
            var now = Date.now();
            var expiredKeys = [];

            for (var key in this.data) {
                if (now - this.data[key].timestamp > CONFIG.CACHE_EXPIRE_TIME) {
                    expiredKeys.push(key);
                }
            }

            expiredKeys.forEach(function(key) {
                delete this.data[key];
            });

            if (expiredKeys.length > 0) {
                this._log('清理过期缓存：' + expiredKeys.join(', '));
            }
        },

        /**
         * 获取缓存统计信息
         * @returns {object} 统计信息
         */
        getStats: function() {
            return {
                totalCount: Object.keys(this.data).length,
                maxSize: CONFIG.CACHE_MAX_SIZE,
                expireTime: CONFIG.CACHE_EXPIRE_TIME,
                cacheKeys: Object.keys(this.data)
            };
        },

        /**
         * 日志输出
         * @param {string} message - 日志消息
         */
        _log: function(message) {
            if (CONFIG.DEBUG_MODE) {
                console.log('[GPSR缓存] ' + message);
            }
        }
    };

    // 性能监控
    var performanceMonitor = {
        metrics: {},

        /**
         * 开始计时
         * @param {string} key - 计时键
         */
        start: function(key) {
            this.metrics[key] = {
                startTime: performance.now(),
                endTime: null,
                duration: null
            };
        },

        /**
         * 结束计时
         * @param {string} key - 计时键
         * @returns {number} 耗时（毫秒）
         */
        end: function(key) {
            if (this.metrics[key]) {
                this.metrics[key].endTime = performance.now();
                this.metrics[key].duration = this.metrics[key].endTime - this.metrics[key].startTime;

                if (CONFIG.PERFORMANCE_LOG) {
                    console.log('[GPSR性能] ' + key + '：' + this.metrics[key].duration.toFixed(2) + 'ms');
                }

                return this.metrics[key].duration;
            }
            return 0;
        },

        /**
         * 获取性能统计
         * @returns {object} 性能统计数据
         */
        getStats: function() {
            return this.metrics;
        }
    };

    // PDF预加载管理器
    var pdfPreloader = {
        loadedUrls: new Set(),

        /**
         * 预加载PDF文件
         * @param {string} pdfUrl - PDF文件URL
         * @returns {Promise} 预加载Promise
         */
        preload: function(pdfUrl) {
            if (this.loadedUrls.has(pdfUrl)) {
                return Promise.resolve();
            }

            return new Promise(function(resolve, reject) {
                var xhr = new XMLHttpRequest();
                xhr.open('HEAD', pdfUrl, true); // 使用HEAD请求检查文件存在性
                xhr.timeout = 5000; // 5秒超时

                xhr.onload = function() {
                    if (xhr.status === 200) {
                        // 文件存在，进行实际预加载
                        var fullXhr = new XMLHttpRequest();
                        fullXhr.open('GET', pdfUrl, true);
                        fullXhr.responseType = 'blob';
                        fullXhr.timeout = 10000; // 10秒超时

                        fullXhr.onload = function() {
                            if (fullXhr.status === 200) {
                                this.loadedUrls.add(pdfUrl);
                                console.log('[GPSR] PDF预加载完成：' + pdfUrl);
                                resolve();
                            } else {
                                reject(new Error('PDF加载失败：' + fullXhr.status));
                            }
                        }.bind(this);

                        fullXhr.onerror = function() {
                            reject(new Error('PDF加载网络错误'));
                        };

                        fullXhr.send();
                    } else {
                        reject(new Error('PDF文件不存在：' + xhr.status));
                    }
                }.bind(this);

                xhr.onerror = function() {
                    reject(new Error('PDF检查网络错误'));
                };

                xhr.send();
            }.bind(this));
        }
    };

    // 主要的GPSR缓存管理器
    var GpsrCacheManager = {

        /**
         * 预加载GPSR标签
         * @param {string} apvNo - 发货单号
         * @param {object} options - 选项配置
         * @returns {Promise} 预加载Promise
         */
        preloadGpsrTag: function(apvNo, options) {
            options = options || {};
            var isLocal = options.local !== false; // 默认为true
            var retryCount = options.retryCount || 0;

            return new Promise(function(resolve, reject) {
                // 检查是否已缓存
                var cached = gpsrCache.get(apvNo);
                if (cached) {
                    console.log('[GPSR] 使用已缓存数据：' + apvNo);
                    resolve(cached);
                    return;
                }

                // 开始性能监控
                var perfKey = 'preload_' + apvNo;
                performanceMonitor.start(perfKey);

                var url = window.CONTEXT_PATH + (isLocal ? "apv/packs/printGpsrTag" : "fba/packs/printGpsrTag") + "?apvNo=" + apvNo;

                $.ajax({
                    url: url,
                    type: "GET",
                    async: true,
                    timeout: 15000, // 15秒超时
                    success: function(response) {
                        performanceMonitor.end(perfKey);

                        try {
                            // 解析响应，提取PDF URLs
                            var $response = $(response);
                            var pdfUrls = [];
                            var platform = null;

                            $response.find(".gpsr-tag-print").each(function() {
                                var $this = $(this);
                                var printUrl = $this.find("input[name='printUrl']").val();
                                if (printUrl && printUrl.indexOf("pdf/gpsr") !== -1) {
                                    pdfUrls.push(window.location.origin + printUrl);
                                }

                                // 尝试识别平台
                                if (!platform) {
                                    if (printUrl) {
                                        platform = 'SMT'; // SMT平台使用PDF
                                    } else {
                                        platform = 'HTML'; // 其他平台使用HTML
                                    }
                                }
                            });

                            // 缓存数据
                            var cacheData = {
                                content: response,
                                pdfUrls: pdfUrls,
                                platform: platform
                            };

                            gpsrCache.set(apvNo, cacheData);

                            // 预加载PDF文件
                            if (pdfUrls.length > 0) {
                                Promise.all(pdfUrls.map(function(url) {
                                    return pdfPreloader.preload(url).catch(function(error) {
                                        console.warn('[GPSR] PDF预加载失败：' + url, error);
                                    });
                                })).then(function() {
                                    console.log('[GPSR] 所有PDF预加载完成：' + apvNo);
                                });
                            }

                            console.log('[GPSR] 标签预加载完成：' + apvNo + '，平台：' + platform);
                            resolve(cacheData);

                        } catch (error) {
                            console.error('[GPSR] 预加载数据解析失败：', error);
                            reject(error);
                        }
                    },
                    error: function(xhr, status, error) {
                        performanceMonitor.end(perfKey);

                        console.warn('[GPSR] 预加载失败：' + apvNo + '，状态：' + status + '，错误：' + error);

                        // 重试机制
                        if (retryCount < CONFIG.RETRY_MAX_COUNT) {
                            console.log('[GPSR] 准备重试预加载：' + apvNo + '，重试次数：' + (retryCount + 1));
                            setTimeout(function() {
                                this.preloadGpsrTag(apvNo, {
                                    local: isLocal,
                                    retryCount: retryCount + 1
                                }).then(resolve).catch(reject);
                            }.bind(this), CONFIG.RETRY_DELAY);
                        } else {
                            reject(new Error('预加载失败，已达最大重试次数'));
                        }
                    }.bind(this)
                });
            }.bind(this));
        },

        /**
         * 打印GPSR标签（优先使用缓存）
         * @param {string} apvNo - 发货单号
         * @param {string} sku - SKU（可选，用于指定打印特定SKU）
         * @param {object} options - 选项配置
         * @returns {Promise} 打印Promise
         */
        printGpsrTag: function(apvNo, sku, options) {
            options = options || {};
            var isLocal = options.local !== false; // 默认为true

            return new Promise(function(resolve, reject) {
                // 开始性能监控
                var perfKey = 'print_' + apvNo + (sku ? '_' + sku : '');
                performanceMonitor.start(perfKey);

                // 检查缓存
                var cached = gpsrCache.get(apvNo);
                if (cached && cached.content) {
                    console.log('[GPSR] 使用缓存打印：' + apvNo);
                    this._doPrintFromCache(cached, sku, perfKey).then(resolve).catch(reject);
                } else {
                    console.log('[GPSR] 缓存未命中，降级到原有方式：' + apvNo);
                    this._fallbackPrint(apvNo, sku, isLocal, perfKey).then(resolve).catch(reject);
                }
            }.bind(this));
        },

        /**
         * 从缓存执行打印
         * @private
         */
        _doPrintFromCache: function(cached, sku, perfKey) {
            return new Promise(function(resolve, reject) {
                try {
                    // 将缓存内容写入页面隐藏区域
                    var $container = this._getOrCreateContainer();
                    $container.html(cached.content);

                    var printCount = 0;
                    var hasError = false;

                    $container.find(".gpsr-tag-print").each(function() {
                        var $this = $(this);
                        var copies = parseInt($this.find("input[name='printQty']").val()) || 1;
                        var printSku = $this.find("input[name='printSku']").val();
                        var printHtml = $this.html();
                        var smtTagPdf = $this.find("input[name='printUrl']").val();

                        // 如果指定了SKU，只打印匹配的SKU
                        if (sku && sku !== printSku) {
                            return true; // 继续下一个
                        }

                        try {
                            this._executePrint(printHtml, copies, smtTagPdf);
                            printCount++;

                            // 如果指定了SKU且已找到匹配项，停止遍历
                            if (sku && sku === printSku) {
                                return false; // 停止遍历
                            }
                        } catch (error) {
                            console.error('[GPSR] 打印执行失败：', error);
                            hasError = true;
                        }
                    }.bind(this));

                    performanceMonitor.end(perfKey);

                    if (printCount > 0) {
                        console.log('[GPSR] 缓存打印完成，打印数量：' + printCount);
                        resolve({ success: true, printCount: printCount });
                    } else if (hasError) {
                        reject(new Error('打印执行失败'));
                    } else {
                        reject(new Error('未找到可打印的GPSR标签'));
                    }

                } catch (error) {
                    performanceMonitor.end(perfKey);
                    console.error('[GPSR] 缓存打印失败：', error);
                    reject(error);
                }
            }.bind(this));
        },

        /**
         * 降级打印（使用原有方式）
         * @private
         */
        _fallbackPrint: function(apvNo, sku, isLocal, perfKey) {
            return new Promise(function(resolve, reject) {
                // 检查原有函数是否存在
                if (typeof window.printLocalGpsrTag === 'function') {
                    try {
                        window.printLocalGpsrTag(apvNo, sku, isLocal);
                        performanceMonitor.end(perfKey);
                        console.log('[GPSR] 降级打印完成：' + apvNo);
                        resolve({ success: true, fallback: true });
                    } catch (error) {
                        performanceMonitor.end(perfKey);
                        console.error('[GPSR] 降级打印失败：', error);
                        reject(error);
                    }
                } else {
                    performanceMonitor.end(perfKey);
                    reject(new Error('原有打印函数不存在，无法降级'));
                }
            });
        },

        /**
         * 执行具体的打印操作
         * @private
         */
        _executePrint: function(printHtml, copies, smtTagPdf) {
            // 检查打印函数是否存在
            if (typeof window.doPrintGpsrTag === 'function') {
                window.doPrintGpsrTag(printHtml, copies, smtTagPdf);
            } else {
                // 如果没有doPrintGpsrTag函数，使用基础打印逻辑
                this._basicPrint(printHtml, copies, smtTagPdf);
            }
        },

        /**
         * 基础打印逻辑（兼容性保证）
         * @private
         */
        _basicPrint: function(printHtml, copies, smtTagPdf) {
            if (typeof window.getLodop !== 'function') {
                throw new Error('打印控件未安装或未加载');
            }

            var LODOP = window.getLodop();
            if (!LODOP) {
                throw new Error('无法获取打印控件');
            }

            var pageLength = "70mm";
            var pageWidth = "60mm";

            LODOP.SET_PRINT_PAGESIZE(0, pageLength, pageWidth, 'Note');

            if (smtTagPdf) {
                // PDF打印
                LODOP.ADD_PRINT_PDF("2mm", 0, pageLength, pageWidth, window.location.origin + smtTagPdf);
                LODOP.SET_PRINT_STYLEA(0, "PDFScalMode", 2);
            } else {
                // HTML打印
                LODOP.ADD_PRINT_HTM("2mm", 0, pageLength, pageWidth, printHtml);
            }

            // 设置打印机
            if (window.jitPrinter75Tag && LODOP.SET_PRINTER_INDEX(window.jitPrinter75Tag)) {
                LODOP.SET_PRINT_COPIES(copies || 1);
                LODOP.PRINT();
            } else {
                throw new Error('打印机未配置或不可用');
            }
        },

        /**
         * 获取或创建容器元素
         * @private
         */
        _getOrCreateContainer: function() {
            var $container = $('#print_gpsr_tag');
            if ($container.length === 0) {
                $container = $('<div id="print_gpsr_tag" style="display: none;"></div>');
                $('body').append($container);
            }
            return $container;
        },

        /**
         * 清理GPSR缓存
         * @param {string} apvNo - 发货单号（可选，不传则清理所有）
         */
        clearGpsrCache: function(apvNo) {
            if (apvNo) {
                gpsrCache.remove(apvNo);
                console.log('[GPSR] 已清理指定缓存：' + apvNo);
            } else {
                gpsrCache.clear();
                console.log('[GPSR] 已清理所有缓存');
            }
        },

        /**
         * 检查GPSR标签是否已缓存
         * @param {string} apvNo - 发货单号
         * @returns {boolean} 是否已缓存
         */
        isCached: function(apvNo) {
            return !!gpsrCache.get(apvNo);
        },

        /**
         * 获取缓存统计信息
         * @returns {object} 统计信息
         */
        getCacheStats: function() {
            return gpsrCache.getStats();
        },

        /**
         * 获取性能统计信息
         * @returns {object} 性能统计
         */
        getPerformanceStats: function() {
            return performanceMonitor.getStats();
        },

        /**
         * 设置配置
         * @param {object} newConfig - 新配置
         */
        setConfig: function(newConfig) {
            Object.assign(CONFIG, newConfig);
            console.log('[GPSR] 配置已更新：', CONFIG);
        },

        /**
         * 获取当前配置
         * @returns {object} 当前配置
         */
        getConfig: function() {
            return Object.assign({}, CONFIG);
        },

        /**
         * 批量预加载GPSR标签
         * @param {Array<string>} apvNos - 发货单号数组
         * @param {object} options - 选项配置
         * @returns {Promise} 批量预加载Promise
         */
        batchPreload: function(apvNos, options) {
            options = options || {};
            var concurrency = options.concurrency || 3; // 并发数限制
            var self = this; // 保存this引用

            return new Promise(function(resolve, reject) {
                var results = [];
                var errors = [];
                var completed = 0;

                // 输入验证
                if (!Array.isArray(apvNos) || apvNos.length === 0) {
                    resolve({
                        success: [],
                        errors: [],
                        total: 0,
                        successCount: 0,
                        errorCount: 0
                    });
                    return;
                }

                // 分批处理
                var batches = [];
                for (var i = 0; i < apvNos.length; i += concurrency) {
                    batches.push(apvNos.slice(i, i + concurrency));
                }

                var processBatch = function(batchIndex) {
                    if (batchIndex >= batches.length) {
                        // 所有批次完成
                        resolve({
                            success: results,
                            errors: errors,
                            total: apvNos.length,
                            successCount: results.length,
                            errorCount: errors.length
                        });
                        return;
                    }

                    var batch = batches[batchIndex];
                    var batchPromises = batch.map(function(apvNo) {
                        return self.preloadGpsrTag(apvNo, options)
                            .then(function(result) {
                                results.push({ apvNo: apvNo, result: result });
                                return result;
                            })
                            .catch(function(error) {
                                errors.push({ apvNo: apvNo, error: error });
                                return null;
                            });
                    });

                    Promise.all(batchPromises).then(function() {
                        completed += batch.length;
                        console.log('[GPSR] 批量预加载进度：' + completed + '/' + apvNos.length);

                        // 处理下一批次
                        setTimeout(function() {
                            processBatch(batchIndex + 1);
                        }, 100); // 批次间隔100ms
                    }).catch(function(error) {
                        console.error('[GPSR] 批次处理失败：', error);
                        reject(error);
                    });
                };

                // 开始处理第一批次
                processBatch(0);
            });
        },

        /**
         * 智能预加载（根据扫描历史预测）
         * @param {string} currentApvNo - 当前发货单号
         * @param {object} options - 选项配置
         */
        smartPreload: function(currentApvNo, options) {
            options = options || {};

            // 预加载当前发货单
            this.preloadGpsrTag(currentApvNo, options).catch(function(error) {
                console.warn('[GPSR] 智能预加载失败：' + currentApvNo, error);
            });

            // 这里可以扩展更复杂的预测逻辑
            // 例如：根据任务号预加载同批次的其他发货单
            // 或者：根据历史扫描模式预加载可能的下一个发货单
        },

        /**
         * 健康检查
         * @returns {object} 健康状态
         */
        healthCheck: function() {
            var health = {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                cache: {
                    size: Object.keys(gpsrCache.data).length,
                    maxSize: CONFIG.CACHE_MAX_SIZE,
                    usage: (Object.keys(gpsrCache.data).length / CONFIG.CACHE_MAX_SIZE * 100).toFixed(2) + '%'
                },
                dependencies: {
                    jquery: typeof $ !== 'undefined',
                    lodop: typeof window.getLodop === 'function',
                    printFunction: typeof window.doPrintGpsrTag === 'function' || typeof window.printLocalGpsrTag === 'function'
                },
                performance: {
                    metricsCount: Object.keys(performanceMonitor.metrics).length
                }
            };

            // 检查依赖
            if (!health.dependencies.jquery) {
                health.status = 'warning';
                health.issues = health.issues || [];
                health.issues.push('jQuery未加载');
            }

            if (!health.dependencies.lodop) {
                health.status = 'warning';
                health.issues = health.issues || [];
                health.issues.push('打印控件未加载');
            }

            if (!health.dependencies.printFunction) {
                health.status = 'warning';
                health.issues = health.issues || [];
                health.issues.push('打印函数不可用');
            }

            // 检查缓存使用率
            if (health.cache.size >= CONFIG.CACHE_MAX_SIZE * 0.9) {
                health.status = health.status === 'healthy' ? 'warning' : health.status;
                health.issues = health.issues || [];
                health.issues.push('缓存使用率过高');
            }

            return health;
        }
    };

    // 兼容性适配器 - 保持与现有代码的兼容性
    var CompatibilityAdapter = {
        /**
         * 重写全局printGpsrTag函数，使其使用缓存管理器
         */
        overridePrintGpsrTag: function() {
            // 保存原有函数
            if (typeof window.printGpsrTag === 'function') {
                window._originalPrintGpsrTag = window.printGpsrTag;
            }

            // 重写为使用缓存管理器
            window.printGpsrTag = function(apvNo, sku) {
                return GpsrCacheManager.printGpsrTag(apvNo, sku).catch(function(error) {
                    console.error('[GPSR] 缓存打印失败，尝试原有方式：', error);
                    if (window._originalPrintGpsrTag) {
                        return window._originalPrintGpsrTag(apvNo, sku);
                    }
                    throw error;
                });
            };

            console.log('[GPSR] 已重写printGpsrTag函数以使用缓存管理器');
        },

        /**
         * 自动集成到现有扫描流程
         */
        autoIntegrate: function() {
            // 监听DOM变化，自动预加载GPSR
            if (typeof MutationObserver !== 'undefined') {
                var observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList') {
                            mutation.addedNodes.forEach(function(node) {
                                if (node.nodeType === 1) { // Element node
                                    var $node = $(node);

                                    // 检查是否包含gpsrPlatform输入框
                                    var $gpsrInput = $node.find("input[name='gpsrPlatform']");
                                    if ($gpsrInput.length > 0 && $gpsrInput.val()) {
                                        var $apvInput = $node.find("input[name='apvNo']");
                                        if ($apvInput.length > 0) {
                                            var apvNo = $apvInput.val();
                                            if (apvNo) {
                                                console.log('[GPSR] 自动检测到GPSR订单，开始预加载：' + apvNo);
                                                GpsrCacheManager.preloadGpsrTag(apvNo).catch(function(error) {
                                                    console.warn('[GPSR] 自动预加载失败：' + apvNo, error);
                                                });
                                            }
                                        }
                                    }
                                }
                            });
                        }
                    });
                });

                // 开始观察
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                console.log('[GPSR] 已启用自动集成模式');
            }
        }
    };

    // 初始化函数
    function initialize() {
        console.log('[GPSR] 缓存管理器初始化中...');

        // 检查依赖
        if (typeof $ === 'undefined') {
            console.warn('[GPSR] jQuery未加载，部分功能可能不可用');
        }

        // 设置定期清理
        setInterval(function() {
            gpsrCache.cleanExpired();
        }, 60000); // 每分钟清理一次

        // 页面卸载时清理缓存
        $(window).on('beforeunload', function() {
            gpsrCache.clear();
        });

        console.log('[GPSR] 缓存管理器初始化完成');

        // 输出健康检查信息
        var health = GpsrCacheManager.healthCheck();
        console.log('[GPSR] 健康检查：', health);
    }

    // 导出到全局
    window.GpsrCacheManager = GpsrCacheManager;

    // 添加便捷访问属性
    window.GpsrCacheManager.cache = gpsrCache;
    window.GpsrCacheManager.performance = performanceMonitor;
    window.GpsrCacheManager.config = CONFIG;
    window.GpsrCacheManager.compatibility = CompatibilityAdapter;

    // 添加便捷方法别名
    window.preloadGpsrTag = GpsrCacheManager.preloadGpsrTag.bind(GpsrCacheManager);
    window.clearGpsrCache = GpsrCacheManager.clearGpsrCache.bind(GpsrCacheManager);

    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // 提供手动启用兼容模式的方法
    window.enableGpsrCompatibilityMode = function() {
        CompatibilityAdapter.overridePrintGpsrTag();
        CompatibilityAdapter.autoIntegrate();
    };

})(window);
