<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
	.modal-body input {
		width: 240px;
	}
	.top_bar{
    	position:fixed;top:0px;
	}
	#task-list td {
		vertical-align:middle;
	}
	.scan-a {
    	width: 120px;
    	height: 120px;
    	line-height: 100px;
    	text-align: center;
    	margin-bottom: 50px;
    	font-size:48px;
	}
	#d-top {
			right: 15px;
	}
	th,td{
		font-size: 18px;
	}
	tr{
		height: 65px;
	}
	.titleTd{
		background-color: #d7d7d7;
		text-align: center;
	}
</style>
</head>

<body>
	<@header method="header" active="12050000"><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">发货管理</a>
					<li class="active">中转仓见单出单</li>
				</ul>
			</div>
		</div>
		<#include "/common/pack_bgcolor_selector.html">		

		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">

			<div class="row">
				<div class="col-md-12">
					<div style="float:left;margin-top: 15px;text-align: center; margin-left: 600px;">
						<span class="control-inline">发货单号/跟踪号/唯一码</span>
						<input type="text" class="form-control input-large input-inline" id="apv-no" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" tabindex="4">
					</div>
					<div style="float: right; margin-right:100px;">
						<button  type="button" class="btn red" onclick="openPrintDialog()">打印机配置</button>
					</div>
				</div>
			</div><!-- end row -->

			<div class="row" style="margin-top: 30px;">
				<div class="col-md-12">
					<div class="col-md-4" id="print_div_left" hidden>
						<iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto"
								id="shippingOrderFrame" name="shippingOrderFrame" width="100%" height="500px"></iframe>
					</div>
					<div class="col-md-4" id="unique_div" hidden>
						<input type="hidden" id="tracking-number"/>
						<span class="control-inline col-md-2" style="font-size: 18px;">平台</span>
						<div class="col-md-10">
							<input type="text" style="width:80%;height:36px;margin-bottom: 20px;text-align: center; font-size: 20px; border: 1px solid #cdcdcd" id="platform" value="" readonly />
						</div>
						<span class="control-inline col-md-2" style="font-size: 18px;">发货单号</span>
						<div class="col-md-10">
							<input type="text" style="width:80%;height:36px;margin-bottom: 20px;text-align: center; font-size: 20px; border: 1px solid #cdcdcd" id="apvNo" value="" readonly />
						</div>
						<div id="tbody">

						</div>
					</div>
					<div class="col-md-4" id="print_div_img" hidden>
						<img src="" width="100%;" height="600px;" onclick="enlarge(this)" />
					</div>
					<div class="col-md-6" id="print_div">
						<!--<div style="border-bottom: 1px #CCC dotted; height: 300px;" >
						</div>-->
						<div id="print_tag" style="width:100%;" hidden></div>
						<iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto"
								id="shippingOrderFrame" name="shippingOrderFrame" width="100%" height="500px"></iframe>
					</div>
				</div>
			</div>
			<div class="row">
				<!--<div class="col-md-12 iframe-container" id="printOtherSS" hidden>
				</div>-->
				<div id="print_tag_ss" class="col-md-8"></div>
				<div id="print_gpsr_tag" style="display: none;"></div>
			</div>

		</div>
		<img id="enlarge" style='position:absolute;width:auto;height:auto;top:10%;left:50%;display:none;'/>
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/pages/pack-style.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/print.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
	<script type="text/javascript">

		window.onload = function () {
			getPrinterList();
		};
	
		$(document).ready(function(){
			pageInit();
			initPrinter();
		}); // end ready

		// 初始化
		function pageInit() {
			$("#apv-no").val('');
			$("#apv-no").select().focus();
		}

		var uniqueSkuArr = [];
		var uniqueSkuIds = [];

		var printObj = {};

		var gpsrPlatform = false;

		function inputnext(obj){
			
			if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				layer.alert("请输入唯一码!");
				return ;
			}

			var input = obj.value.replace(/\s/g,'');
			
			$.get(CONTEXT_PATH + "whAsn/printAsnUuid?input="+input, function(data){
	    		if (data.status == 200) {
	    			var apvWaybillUrl = data.body.billPdfUrl;
					var localPdfUrl = data.body.localPdfUrl;
					var jitBoxNumber = data.body.boxNumber;
					if (apvWaybillUrl === undefined || apvWaybillUrl === ''){
						apvWaybillUrl = localPdfUrl;
					}
					var type = 'SS';
					var isTemu = (data.body.platform && data.body.platform === 'TEMU');

					if (obj.value.split("=").length < 2){
						// 扫描发货单
						$("#print_div_left").removeAttr("hidden");
						if (isTemu) {
							$("#tbody").html('');
							$('#print_tag_ss').html("");
							$("#print_tag_ss").html('<div class="col-md-3" name="printHtml" id="printHtml"></div>\n' +
									' <div class="col-md-3" id="print_xiangm"></div>');
							printXiangMai(data.body.apvNo, data.body.sku);
							setTimeout(function () { printFbaNoAndFnSku(data.body.apvNo, data.body.sku, data.body.quantity);
							}, 500);
						} else {
							printCopies(localPdfUrl, null, jitPrinter, 1,jitBoxNumber);
						}
						return;
					}

					if (data.body.gpsr && data.body.gpsr === 'GPSR'){
						gpsrPlatform = true;
					}

					$("#apvNo").val(data.body.apvNo);
					$("#platform").val(data.body.platform);
	    			if(data.body.uniqueSkuMap){
						$("#tbody").html('');
						type = 'SM';
						initFrame("SM");
	    				// 非单品单件
						var skuMap = data.body.skuMap;
						var packMap = data.body.packMap;
						var html = "";
						for(let [key,value] of Object.entries(data.body.uniqueSkuMap)){
							let matchMaterialsName = '';
							if(skuMap[key].matchMaterialsName){
								matchMaterialsName = skuMap[key].matchMaterialsName;
							}
							let originPack = '不需要原包装';
							if(packMap[key].isOriginalPackage){
								originPack = '需要原包装';
							}
							let skuLabel = '';
							if(packMap[key].skuLabelName){
								skuLabel = packMap[key].skuLabelName;
							}
							if(packMap[key].tag != undefined && packMap[key].tag.indexOf('磁性')!= -1){
								skuLabel += ' '+packMap[key].tag;
							}
							html += "<table class='table table-bordered table-condensed' style='margin-top=20px;background-color: #f9f9f9;' >" +
									"<colgroup><col width='15%'/><col width='35%'/><col width='15%'/><col width='35%'/></colgroup><tbody>" +
									"<tr>" +
									"<td class='titleTd'>SKU</td>" + "<td>"+key+"</td>" + "<td class='titleTd'>商品名称</td>" + "<td>"+skuMap[key].name+"</td>" +
									"</tr>" +
									"<tr>" +
									"<td class='titleTd'>应发数量</td>" + "<td style='font-size: 35px;font-weight: 600;text-align: center;'>"+ value.length +"</td>" +
									"<td class='titleTd'>未扫描数量</td>" + "<td id='"+ key +"_scan' style='color:#ff2222;font-size: 35px;font-weight: 600;text-align: center;'>"+ value.length +"</td>" +
									"</tr>" +
									"<tr>" +
									"<td class='titleTd'>袋子</td>" + "<td>"+skuMap[key].packagingName+"</td>" + "<td class='titleTd'>辅助耗材</td>" + "<td>"+matchMaterialsName+"</td>" +
									"</tr>" +
									"<tr><td class='titleTd'>是否需要原包装</td><td colspan='3' style='color:red;'>"+originPack+"</td></tr>" +
									"<tr><td class='titleTd'>商品特性</td><td colspan='3' style='color:red;'>"+skuLabel+"</td></tr>" +
									"</tbody></table>";
							value.forEach(function(item){
								uniqueSkuIds.push(item.id);
								uniqueSkuArr.push(item.sku + "=" + item.uuid);
							});
						}

						$("#tbody").append(html);
						$("#unique_div").removeClass();
						$("#unique_div").attr("class", "col-md-6");
						$("#unique_div").removeAttr("hidden");
						$("#print_div").removeAttr("hidden");
						$("#print_div_left").attr("hidden", "hidden");
						$("#print_div_img").attr("hidden", "hidden");
						$("#apv-no").attr("onkeypress", "if(event.keyCode==13) { scanUniqueSku(this); return false;}");
						if (isTemu) {
							printObj.isTemu = isTemu;
							printObj.apvNo = data.body.apvNo;
							printObj.sku = data.body.sku;
							printObj.quantity = data.body.quantity;
						} else {
							printObj.apvWaybillUrl = apvWaybillUrl;
							printObj.localPdfUrl = localPdfUrl;
							printObj.location = data.location;
							printObj.message = data.message;
							printObj.jitBoxNumber = jitBoxNumber;
						}
						scanUniqueSku(obj);
					}else{
						$("#tbody").html('');
						initFrame("SS");
						var skuInfo = data.body.skuInfo;
						var packMap = data.body.packMap;
						$("#unique_div").removeClass();
						$("#unique_div").attr("class", "col-md-4");
						$("#unique_div").removeAttr("hidden");
						$("#print_div").attr("hidden", "hidden");
						$("#print_div_left").removeAttr("hidden");
						if(skuInfo.packImageList){
							$("#print_div_img img")[0].src = skuInfo.packImageList[0];
						}
						$("#print_div_img").removeAttr("hidden");
						uniqueSkuIds.push(data.body.SSUniqueSkuId);
						let matchMaterialsName = '';
						if(skuInfo.matchMaterialsName){
							matchMaterialsName = skuInfo.matchMaterialsName;
						}
						let originPack = '不需要原包装';
						if(packMap[skuInfo.sku].isOriginalPackage){
							originPack = '需要原包装';
						}
						let skuLabel = '';
						if(packMap[skuInfo.sku].skuLabelName){
							skuLabel = packMap[skuInfo.sku].skuLabelName;
						}
						if(packMap[skuInfo.sku].tag != undefined && packMap[skuInfo.sku].tag.indexOf('磁性')!= -1){
							skuLabel += ' '+packMap[skuInfo.sku].tag;
						}
						var html = "<table class='table table-bordered table-condensed' style='margin-top=20px;background-color: #f9f9f9;' >" +
								"<colgroup><col width='15%'/><col width='35%'/><col width='15%'/><col width='35%'/></colgroup><tbody>" +
								"<tr>" +
								"<td class='titleTd'>SKU</td>" + "<td>"+skuInfo.sku+"</td>" + "<td class='titleTd'>数量</td>" + "<td>1</td>" +
								"</tr>" +
								"<tr>" +
								"<td class='titleTd'>商品名称</td>" + "<td colspan='3'>"+ skuInfo.name +"</td>" +
								"</tr>" +
								"<tr>" +
								"<td class='titleTd'>袋子</td>" + "<td>"+skuInfo.packagingName+"</td>" + "<td class='titleTd'>辅助耗材</td>" + "<td>"+matchMaterialsName+"</td>" +
								"</tr>" +
								"<tr><td class='titleTd'>是否需要原包装</td><td colspan='3' style='color:red;'>"+originPack+"</td></tr>" +
								"<tr><td class='titleTd'>商品特性</td><td colspan='3' style='color:red;'>"+skuLabel+"</td></tr>" +
								"</tbody></table>";
						$("#tbody").append(html);

						if (isTemu) {
							$("#print_tag_ss").html('<div class="col-md-3" name="printHtml" id="printHtml"></div>\n' +
									' <div class="col-md-3" id="print_xiangm"></div>');
							printXiangMai(data.body.apvNo, data.body.sku);
							setTimeout(function () { printFbaNoAndFnSku(data.body.apvNo, data.body.sku, data.body.quantity);
							}, 500);
						} else {
							// 单品单件
							document.getElementById('shippingOrderFrame').src = localPdfUrl;
							printCopies(localPdfUrl, null, jitPrinter, 1,jitBoxNumber);
						}
					}
					if (gpsrPlatform){
						var apvNo = data.body.apvNo;
						printLocalGpsrTag(apvNo, null, false);
					}
					if (!isTemu){
						printTag(type, data.body.fbaId);
					}
				} else {
					customizeLayer(data.message);
				}
				pageInit();
			});
		}

		function printTag(type, id) {
			$('#print_tag').html("");
			$('#print_tag_ss').html("");
			$.ajax({
				url:CONTEXT_PATH +"fba/packs/asnPrintTag?id="+id,
				type:"GET",
				data:{id:id},
				async: false,
				success : function(response){
					if (type === 'SS'){
						$('#print_tag_ss').html(response);
					} else{
						$('#print_tag').html(response);
						$('#print_tag').removeAttr("hidden");
					}
					var printUrl = $("input[name='printUrl']").val();
					var purposeHouse = $(response).find("input[name='purposeHouse']").val();
					if (printUrl) {
						printUrl = window.location.origin + printUrl;
						var pageLength = "60mm";
						var pageWidth = "60mm";
						printPdfCopies(printUrl, jitPrinter75Tag, pageLength, pageWidth, 1);
					} else {
						var map = new Map();
						$("input[name='base64']").each(function () {
							var key = $(this).val();
							var num = map.get(key);
							if (num === undefined) {
								num = 0;
							}
							num++;
							map.put(key, num);
						})
						map.each(function (k, v) {
							if(purposeHouse && purposeHouse == 'Shein'){
								printWeirdPdfCopies(k, jitPrinter75Tag, "70mm", "60mm", "70mm", "30mm", v);
							}else {
								printCopies(k, jitPrinterTag, null, v, null);
							}
						});
						var skuList = $("#mergeSkuList").val();
						if (skuList){
							$(skuList.split(",")).each(function (index,sku){
								var printCopies = $("input[name='"+sku+"_printCopies']").val();
								var mergePdfList = [];
								$("input[name='"+sku+"_base64Sku']").each(function () {
									mergePdfList.push($(this).val());
								});
								if (mergePdfList.length > 0){
									printMergeCopies(mergePdfList,jitPrinter75Tag,printCopies);
								}
							});
						}
					}

				}
			});
		}

		/**
		 * 用于合并打印
		 * @param temuCodeUrls 合并打印的pdf列表
		 * @param printerTag 打印机
		 * @param copies 打印份数
		 */
		function printMergeCopies(mergePdfList,printerTag,copies){
			var LODOP = getLodop();
			LODOP.SET_PRINT_PAGESIZE(0, '70mm', '60mm', 'Note'); // 设置纸张大小
			$(mergePdfList).each(function(index,item){
				// 缩放pdf的内容，将2张pdf弄到一个页面上
				if (index == 0){
					LODOP.ADD_PRINT_PDF("1mm","0mm","70mm","20mm", item);
				}
				if (index == 1){
					LODOP.ADD_PRINT_PDF("21.39mm","0mm","70mm","40mm", item);
				}
			});
			LODOP.SET_PRINT_STYLEA(0,"PDFScaleMode",0);
			if (LODOP.SET_PRINTER_INDEX(printerTag)) {
				if(copies === undefined){
					copies = 1;
				}
				LODOP.SET_PRINT_COPIES(copies); // 打印份数
				LODOP.PRINT(); // 静默打印
				// LODOP.PRINT_DESIGN();
			}
		}

		function initFrame(type) {
			$("#shippingOrderFrame").remove();
			$("#shippingOrderFrameLeft").remove();
			let frame = '<iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto" id="shippingOrderFrame" name="shippingOrderFrame" width="100%" height="500px"></iframe>';
			if (type == "SS") {
				$("#print_div_left").append(frame);
			}else{
				$("#print_div").append(frame);
			}
		}

		$("#enlarge").click(function() {$("#enlarge").hide(100);});
		// 产品图放大
		function enlarge(obj){
			var url = $(obj).attr("src");
			$("#enlarge").attr("src", url);
			$("#enlarge").show(300);
		}

		function scanUniqueSku(target){
			let uniqueSku = target.value;
			let index = $.inArray(uniqueSku, uniqueSkuArr);
			$('#apv-no').val('');
			if(index == -1){
				layer.alert("唯一码错误或重复扫描!");
				return;
			}
			uniqueSkuArr.splice(index, 1);
			let arr = uniqueSku.split("=");
			let count = $("#" + arr[0] + "_scan").text();
			$("#" + arr[0] + "_scan").text(count - 1);
			if (uniqueSkuArr.length <= 0) {
				$("#apv-no").attr("onkeypress", "if(event.keyCode==13) { inputnext(this); return false;}");
				if (printObj.isTemu){
					$("#print_tag_ss").html('<div class="col-md-3" name="printHtml" id="printHtml"></div>\n' +
							' <div class="col-md-3" id="print_xiangm"></div>');
					printXiangMai(printObj.apvNo, printObj.sku);
					setTimeout(function () { printFbaNoAndFnSku(printObj.apvNo, printObj.sku, printObj.quantity);
					}, 500);
				} else {
					document.getElementById('shippingOrderFrame').src = printObj.localPdfUrl;
					printCopies(printObj.localPdfUrl, null, jitPrinter, 1, printObj.jitBoxNumber);
				}
			}
		}

		function printCopies(message, printerTag, printer, copies, jitBoxNumber) {
			var LODOP = getLodop();
			var printerName;

			if (printer){
				LODOP.SET_PRINT_PAGESIZE(0, '100mm', '100mm', 'Note'); // 设置纸张大小
				LODOP.ADD_PRINT_PDF(0, 0, '100mm', '100mm', message);
				if (jitBoxNumber) {
					LODOP.ADD_PRINT_TEXT(320, 12, 200, 31, jitBoxNumber);
					LODOP.SET_PRINT_STYLEA(0, "FontSize", 30);
				}
				printerName=printer;
			}
			if(printerTag){
				LODOP.SET_PRINT_PAGESIZE(0, '70mm', '30mm', 'Note'); // 设置纸张大小
				LODOP.ADD_PRINT_PDF(0, 0, '70mm', '30mm', message);
				printerName=printerTag;
			}
			if (LODOP.SET_PRINTER_INDEX(printerName)) {
				if(copies === undefined){
					copies = 1;
				}
				LODOP.SET_PRINT_COPIES(copies); // 打印份数
				LODOP.PRINT(); // 静默打印
			}
		}

		function createUniqueSkuLog(){
			$.ajax({
				url: CONTEXT_PATH + "apvs/createUniqueSkuLog",
				type: "post",
				dataType: "json",
				contentType: "application/json;charset=utf-8",
				data: JSON.stringify(uniqueSkuIds),
				success: function(response) {
					if(response.status == '500'){
						console.log("createUniqueSkuLog error:", uniqueSkuIds);
					}
				}
			});
		}

		var cainiao = false;

		function printApvNo(apvNo, orderOrigin, apvWaybillUrl, localPdfUrl){
			document.getElementById('shippingOrderFrame').src = apvWaybillUrl;

			$('#apv-no').val('');
			isFocus = true;
	 		setTimeout(focusSku, 1000);
			createUniqueSkuLog();
			uniqueSkuIds.splice(0, uniqueSkuIds.length);

			printShippingOrderFrame(localPdfUrl);
		}

		var isFocus = false;
		$("body").click(function() {isFocus = false;});
	    function focusSku(){
	    	if (isFocus) {
	    		$('#apv-no').focus();
		    	setTimeout(focusSku, 1000);
			}
	    }

	    function printShippingOrderFrame(localPdfUrl) {
			var printFrame = document.getElementById("shippingOrderFrame");
			var billPdfSrc = printFrame.src;
			console.log(printFrame.src);
			if (localPdfUrl){
				printFrame.src = CONTEXT_PATH + localPdfUrl.split("static/")[1];
				printFrame.contentWindow.print();
				// printFrame.src = billPdfSrc;
			}
		}

		function printXiangMai(packageSn, sku){
			if (!packageSn || !sku) {
				layer.alert("包裹号-SKU为空!");
				return;
			}
			$.ajax({
				url : CONTEXT_PATH + "separateBox/search",
				type:'POST',
				data : {taskNoAndSku : packageSn+"~"+sku, isPrint: true},
				success : function(response) {
					var responseHtml = $(response).find("#show_contents").html();
					var errorMsg = $(response).find("#show_contents").find("#scan-error").text();
					if(errorMsg){
						layer.alert(errorMsg);
						return;
					}
					$("#print_xiangm").html(responseHtml);
					var printHtml = $(response).find("#show_contents").find("#print_content").html();
					if (printHtml) {
						printHtmCopies(printHtml,jitPrinter,1);
					} else {
						var boxNumber = $(response).find("#show_contents").find("#box-number").html();
						if (boxNumber) {
							var asnPrintUrl = document.querySelector("#printTemuTagUrlFrame").src;
							doAsnPrint(asnPrintUrl, boxNumber);
						}
					}
				},
				error : function() {
					layer.alert('打印面单失败!');
				}
			});
		}

		function printFbaNoAndFnSku(orderNo, sku,quantity) {
			if (orderNo && sku) {
				var printPageUrl = CONTEXT_PATH + "skuLabel/printSkuLabel?orderNo=" + orderNo + "&sku=" + sku;
				$.get(printPageUrl, function (data) {
					$("#printHtml").html(data);
					// var printOrderSkuHtml = $("#printHtml").find("#print-item-1").html();
					// if (printOrderSkuHtml){
					// 	printHtmlCopies(printOrderSkuHtml, 1);
					// }
					var printEtHtml = $("#printHtml").find("#dz-print").html();
					if (printEtHtml){
						setTimeout(function () {
							etPrint(printEtHtml, quantity);
						}, 500);
					}
					var skuTag = $("#printHtml").find("#skuTag").val();
					if (skuTag && skuTag.indexOf("宠物") != -1) {
						setTimeout(function () {
							printPetTag(null, quantity);
						}, 500);
					}
					var errorMsg = $("#printHtml").find("#error-msg").html();
					if (errorMsg) {
						layer.alert(errorMsg, {closeBtn: 0}, function (index) {
							layer.close(index);
							$('#sku').select().focus();
						});
						return;
					}
					var printHtml = $("#printHtml").find("#print-item-0").html();
					setTimeout(function () {
						if (printHtml) {
							printTemuLabelCode(printHtml, quantity);
						} else {
							var asnPrintUrl = document.querySelector("#printUrlFrame").src;
							printTemuLabel(asnPrintUrl, jitPrinter75Tag, quantity);
						}
					}, 1000);

					var gpsrTag = $("#printHtml").find("#temu-gpsr").val();
					if (gpsrTag) {
						printPdfCopies(window.location.origin + gpsrTag, jitPrinter75Tag, "70mm", "60mm", quantity);
					}
				});
			}
		}

	</script>
	<!-- END JAVASCRIPTS -->
</body>
</html>