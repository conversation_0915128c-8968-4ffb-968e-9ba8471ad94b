<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
</head>
<body>
<div>
<div id="check_scan_datas" class="border-gray p5">
	<#if !domain.whSku>
		<div class="alert alert-danger">
				<h3>操作失败</h3>
				<p>没有找到相关产品！</p>
		</div>
		<#else>
			<!-- BEGIN PAGE -->
			<form name="submitForm" id="submit-form" method="POST">
				<table>
					<tr>
						<td style="width: 55%;">
							<div>
								<div id="instock-cell">
									<h3>${domain.whAllocationCheckIn.allocationOrderNo }
										<#assign whSku = domain.whSku>
										<span style="color:black;font-size:22px;font-weight: 600">${whSku.qcType }</span>
										<span style="margin-left: 90px;">重量：${whSku.weight}
									</h3>
									<table class="table table-bordered table-condensed mt10" id="item-list">
										<colgroup>
											<col width="100px" />
											<col width="100px" />
											<col width="100px" />
											<col width="250px" />
											<col width="100px" />
											<col width="100px" />
										</colgroup>
										<thead>
										<tr class="">
											<th>良品数量</th>
											<th>不良品类型</th>
											<th>异常数量</th>
											<th>备注</th>
											<th>QC加工装袋</th>
											<th>操作</th>
										</tr>
										</thead>
										<tbody>

										<#assign whSku = domain.whSku>
										<tr id="tr-product-${whSku.id}">
											<input  type="hidden"  name="whAllocationCheckIn.whAllocationCheckInItem.stockId"  value="${domain.whAllocationCheckIn.whAllocationCheckInItem.stockId}">
											<td>
												<input number="true" min="0" digits="true"  required="true" name="whAllocationCheckIn.whAllocationCheckInItem.qcQuantity" class="form-control instock-quantity" value="">
											</td>
											<td>
												<input class="form-control" id="exceptionType" name="whAllocationCheckIn.whAllocationCheckInItem.exceptionTypeStr" type="text" value="">
											</td>
											<td>
												<input number="true" min="0" digits="true"  required="true" name="whAllocationCheckIn.whAllocationCheckInItem.qcExceptionQuantity" class="form-control exception-quantity" value="">
											</td>
											<td>
												<input id="exceptionComment" type="text" class="form-control" name="whAllocationCheckIn.comment" class=" form-control" value="">
											</td>
											<td>
												<select name="qcPacking" class="form-control">
													<option value=""></option>
													<option value="9">超级加工</option>
													<option value="7">重加工</option>
													<option value="5">一般加工</option>
													<option value="3">轻加工</option>
													<option value="0">否</option>
												</select>
											</td>
											<td>
												<button style="font-size: 40px" type="button" class="btn btn-default btn-xs" id="single-build-btn" onclick="buildInStock(${whSku.id});">
													提交
												</button>
											</td>

										</tr>
										<tr class="active">
											<td colspan="7" style="text-align:right;height: 30px"></td>
										</tr>
										</tbody>
									</table>
								</div>

								<input type="hidden" name="uuidSku" id="hidden-uuidSku" value="${whSku.sku}">
								<input type="hidden" name="inId" id="hidden-inId" value="${domain.whAllocationCheckIn.inId}">
								<input type="hidden" name="inputUuid" id="hidden-inputUuid" value="${domain.uuid}">
								<div id="product-cell">
									<table class="table table-bordered table-condensed" id="product-table">
										<colgroup>
											<col width="100px" />
											<col width="200px" />
											<col width="200px" />
											<col width="100px" />
											<col width="100px" />
											<col width="100px" />
											<col width="100px"/>
										</colgroup>
										<thead>
											<tr class="">
												<th>产品图片</th>
												<th>SKU</th>
												<th>产品信息</th>
												<th>系统加工装袋</th>
                                                <th>是否带原包装发货</th>
												<th>产品标识</th>
												<th>入库数量</th>
												<th>抽检数量</th>
											</tr>
										</thead>
										<tbody>

											<#assign whSku = domain.whSku>
											<tr id="tr-product-${whSku.id}">
												<td >
													<img alt="产品缩略图" border="0" width="80px" height="74px" src="${whSku.imageUrl }" />
												</td>
												<td >
													<dl>
														<dt id="product-sku">${whSku.sku }</dt>
														<dd>库位号：${whSku.locationNumber }</dd>
														<dd>开发员：${util('name',whSku.productDeveloper)}</dd>
														<dd>采购员：${util('name',whSku.productBuyer)}</dd>
													</dl>
													<span class="label custom label-sm label-warning">${whSku.statusName }</span>
												</td>
												<td >
													<dl>
														<dd id="product-name-${whSku.id}">产品名称：${whSku.name }</dd>
														<dd id="product-color-${whSku.id}">颜色：${whSku.color }</dd>
														<dd id="product-contain-${whSku.id}">包含：${whSku.contain }</dd>
														<dd id="product-weight-${whSku.id}">重量：${whSku.weight } g</dd>
														<dd id="product-packagingId-${whSku.id}">包装：</dd>
														<dd id="product-skuDecs-${whSku.id}">产品系统质检备注：
															<span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${whSku.skuDecs }</span>
														</dd>
                                                        <dd id="product-skuDecs-${whSku.id}">仓库质检备注：
                                                            <span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${whSku.feature }</span>
                                                        </dd>
                                                        <dd id="product-size-${whSku.id}">标准尺寸：
                                                            <span style="color: red;" >${domain.whSkuWithPmsInfo.size }</span>
                                                        </dd>
													</dl>
												</td>
												<td id="product-skuAlias-${whSku.id}" style="text-align: center;">
                                                    <#if !(domain.systemPack??) || domain.systemPack == 0>
                                                        <span class="qc-packing">否</span>
													<#else >
														<span class="qc-packing">${util('enumName', 'com.estone.sku.enums.ProcessType', domain.systemPack)}</span>
													</#if>
												</td>
                                                <td id="product-skuAlias-${whSku.id}" style="text-align: center;">
													<#if (domain.whSkuWithPmsInfo??) && (domain.whSkuWithPmsInfo.isOriginalPackage == 'true')>
														<span style="color: red;font-size: 14px;font-weight: 600">是</span>
													<#else >
														<span style="color: red;font-size: 14px;font-weight: bold">否</span>
													</#if>
                                                </td>
												<td id="product-skuAlias-${whSku.id}" style="text-align: center;">
													<span style="color: red;font-size: 14px;font-weight: 600">${domain.whSkuWithPmsInfo.skuLabelName}</span>
												</td>
												<td class="qc-quantity">${domain.whAllocationCheckIn.whAllocationCheckInItem.quantity }</td>
												<td class="qc-quantity">${domain.qcQuantity }</td>
											</tr>
										</tbody>
									</table>
									<div id="translate-result">
										<div class="row">
											<div class="col-md-6 translate-src">
											</div>
											<div class="col-md-6 translate-dst" style="background-color: #f0f0f0">
											</div>
										</div>
									</div>
								</div>

							</div>
						</td>
						<td class="img-view">
							<div class="img-content">
								<#include "/sku/skuImgsContain.html">
							</div>
						</td>
					</tr>
				</table>
			</form>
	<!-- END PAGE -->
	</#if>
    <script type="text/javascript" src="${CONTEXT_PATH}js/jquery.imageView.js"></script>
    <script type="text/javascript" src="${CONTEXT_PATH}js/sku/sku-imgs.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
	<script type="text/javascript">
        // 异常类型
        //ar exceptionTypeArray = jQuery.parseJSON($("#exceptionTypes").text());
		var exceptionTypeArray = ${domain.exceptionTypes};
        $("input[name='whAllocationCheckIn.whAllocationCheckInItem.exceptionTypeStr']").select2({
            data : exceptionTypeArray,
            placeholder : "异常类型",
            multiple: true,
            allowClear : true
        });
        $(".instock-quantity").blur(function(){
            var $this = $(this);
            if($this.val() == ''){
                $this.val("");
                return;
            }
            var reg = /^\+?[1-9][0-9]*$/;
            if (!reg.test($this.val())) {
				getErrorInfoAlert("请输入正确的正整数");
                $this.val("");
                return;
            }
            //良品数
            var qty = parseInt($('.instock-quantity').val() == '' ? 0 : $('.instock-quantity').val());

            var inQty = parseInt(${domain.whAllocationCheckIn.whAllocationCheckInItem.quantity });

            //当前入库数 >采购数量
            if(qty  > inQty) {
				$this.val("");
            }
        });
       
        // 单个生成入库单
        function buildInStock(id) {
            // 当前行
            var currentTr = $("#tr-product-" + id);
            //良品数
            var qty = parseInt(currentTr.find('.instock-quantity').val() == '' ? 0 : currentTr.find('.instock-quantity').val());
            //不良品数
            var exQty = parseInt(currentTr.find('.exception-quantity').val() == '' ? 0 : currentTr.find('.exception-quantity').val());

            var inQty = parseInt(${domain.whAllocationCheckIn.whAllocationCheckInItem.quantity });

			let qcPacking = currentTr.find('select[name="qcPacking"]');
            var isQcPacking = qcPacking.val();

            var exceptionType = $("#exceptionType").val();
 
            var reg = /^\+?[1-9][0-9]*$/;
            if (qty > 0 && !reg.test(qty)) {
				getErrorInfoAlert("良品数量输入错误，请输入正确的正整数");
                return;
            }
            if (exQty > 0 && !reg.test(exQty)) {
				getErrorInfoAlert("不良品数量输入错误，请输入正确的正整数");
                return;
            }
            //异常类型选择不包含12.多货，良品数量+不良品数 不能大于入库数量

            if(exceptionType.indexOf("12") == -1 && (qty+exQty > inQty)){
				getErrorInfoAlert("良品数量+不良品数不能大于入库数量!");
                return ;
			}

			if(qty > 0 && qty+exQty < inQty){
				getErrorInfoAlert("良品数量+不良品数不能小于入库数量!");
                return ;
            }

            if(qty > 0 && (isQcPacking == null || isQcPacking=='')){
				getErrorInfoAlert("请选择QC是否加工装袋!");
                return ;
            }

            if((exQty > 0 || $('#exceptionComment').val() != '') && exceptionType == ''){
				getErrorInfoAlert("请输入异常类型！");
                return;
            }
            if(qty <= 0 && exceptionType == '' && $('#exceptionComment').val() == '' && exQty <= 0){
				getErrorInfoAlert("良品数量不能为空！");
                return;
            }
            clicked = true;
            // 生成入库单
            var params = $("#submit-form").serialize();
            $.ajax({
                url : "${CONTEXT_PATH}allocationCheck/skus/pass",
                type: "POST",
                dataType : "json",
                data: params,
                error : function() {
                    App.unblockUI();
					getErrorInfoAlert("系统内部出错，请稍后重试。");
                },
                beforeSend : function() {
                    App.blockUI();
                },
                success: function(data) {
					App.unblockUI();
                    if (data.status == '500') {
						getErrorInfoAlert("失败：" + data.message);
                        return false;
                    }
                    alert("操作成功");
                    $('#uuidSku').val('');
                    $('#hidden-inputUuid').val('');
                    $('#hidden-uuidSku').val('');
                    $('#hidden-boxNo').val("");
                    $('#hidden-boxSku').val("");
                    $('#hidden-inId').val("");
                    $('#s2id_exceptionType').find('li.select2-search-choice').remove();
                    $('.instock-quantity').val('');
                    $('.exception-quantity').val('');
                    $('.span-boxNo').text('');
                    $('#exceptionComment').val('');
                    $('#exceptionType').val('');
					qcPacking.val('');
                    $('#boxNo').val("");
                    $('#boxNo').focus();

                    calsf();// 成功数量

                    // 标识成功
                    $("[id='tr-item-" + $("#product-sku").text() + "']").removeClass("danger").addClass("success");

                    // 清空中间产品信息
                    $("#product-cell").remove();

                    // 清空图片
                    $('.img-view').remove();
                    //清除提交按钮
                    $('#single-build-btn').remove();

                }
            }); // end ajax
        }

		$(document).ready(function(){
  			var images = null;
  			var currentImageIndex = null;
  			var sku = $('#product-sku').text();
		}); // end ready
	</script>
</div>
</div>
</body>
</html>