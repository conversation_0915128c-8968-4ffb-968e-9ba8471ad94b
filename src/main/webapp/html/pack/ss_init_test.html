<!DOCTYPE html>
<html>
<head>
    <title>GPSR集成测试 - ss_init.html</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        button { margin: 5px; padding: 8px 15px; }
        #log { background: #f5f5f5; padding: 10px; height: 300px; overflow-y: auto; font-family: monospace; }
        .log-entry { margin: 2px 0; }
    </style>
</head>
<body>
    <h1>GPSR集成测试 - ss_init.html</h1>
    
    <div class="test-section">
        <h3>🔧 集成状态检查</h3>
        <div id="integration-status">检查中...</div>
    </div>
    
    <div class="test-section">
        <h3>🧪 功能测试</h3>
        <button onclick="testPreload()">测试预加载</button>
        <button onclick="testPrint()">测试打印</button>
        <button onclick="testCompatibility()">测试兼容模式</button>
        <button onclick="testCacheStats()">查看缓存统计</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="test-section">
        <h3>📊 测试日志</h3>
        <div id="log"></div>
    </div>
    
    <!-- 模拟ss_init.html的环境 -->
    <div id="print_gpsr_tag" style="display: none;"></div>
    
    <script>
        // 模拟CONTEXT_PATH
        var CONTEXT_PATH = '/wms/';
        
        // 模拟jQuery（简化版）
        if (typeof $ === 'undefined') {
            window.$ = {
                ajax: function(options) {
                    log('模拟AJAX调用: ' + options.url, 'info');
                    setTimeout(function() {
                        if (options.success) {
                            options.success('<div>模拟GPSR标签内容</div>');
                        }
                    }, 1000);
                }
            };
        }
        
        // 模拟音效播放
        function audioPlay(type) {
            log('播放音效: ' + type, 'info');
        }
        
        // 日志函数
        function log(message, type = 'info') {
            var logDiv = document.getElementById('log');
            var entry = document.createElement('div');
            entry.className = 'log-entry ' + type;
            entry.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 检查集成状态
        function checkIntegrationStatus() {
            var status = document.getElementById('integration-status');
            var checks = [];
            
            // 检查GPSR缓存管理器
            if (typeof GpsrCacheManager !== 'undefined') {
                checks.push('<span class="success">✓ GpsrCacheManager 已加载</span>');
            } else {
                checks.push('<span class="error">✗ GpsrCacheManager 未找到</span>');
            }
            
            // 检查兼容模式函数
            if (typeof enableGpsrCompatibilityMode !== 'undefined') {
                checks.push('<span class="success">✓ enableGpsrCompatibilityMode 函数可用</span>');
            } else {
                checks.push('<span class="error">✗ enableGpsrCompatibilityMode 函数未找到</span>');
            }
            
            // 检查printGpsrTag函数
            if (typeof printGpsrTag !== 'undefined') {
                checks.push('<span class="success">✓ printGpsrTag 函数可用</span>');
            } else {
                checks.push('<span class="warning">⚠ printGpsrTag 函数未找到（需要packing.js）</span>');
            }
            
            // 检查GPSR容器
            if (document.getElementById('print_gpsr_tag')) {
                checks.push('<span class="success">✓ GPSR打印容器存在</span>');
            } else {
                checks.push('<span class="error">✗ GPSR打印容器未找到</span>');
            }
            
            status.innerHTML = checks.join('<br>');
        }
        
        // 测试预加载
        function testPreload() {
            if (typeof GpsrCacheManager === 'undefined') {
                log('GpsrCacheManager 未加载，无法测试', 'error');
                return;
            }
            
            var testApvNo = 'APV' + Date.now();
            log('开始测试预加载: ' + testApvNo, 'info');
            
            GpsrCacheManager.preloadGpsrTag(testApvNo)
                .then(function(result) {
                    log('预加载成功: ' + JSON.stringify(result), 'success');
                })
                .catch(function(error) {
                    log('预加载失败: ' + error.message, 'error');
                });
        }
        
        // 测试打印
        function testPrint() {
            if (typeof GpsrCacheManager === 'undefined') {
                log('GpsrCacheManager 未加载，无法测试', 'error');
                return;
            }
            
            var testApvNo = 'APV' + Date.now();
            log('开始测试打印: ' + testApvNo, 'info');
            
            GpsrCacheManager.printGpsrTag(testApvNo)
                .then(function(result) {
                    log('打印成功: ' + JSON.stringify(result), 'success');
                })
                .catch(function(error) {
                    log('打印失败: ' + error.message, 'error');
                });
        }
        
        // 测试兼容模式
        function testCompatibility() {
            if (typeof enableGpsrCompatibilityMode === 'undefined') {
                log('enableGpsrCompatibilityMode 函数未找到', 'error');
                return;
            }
            
            log('启用兼容模式...', 'info');
            try {
                enableGpsrCompatibilityMode();
                log('兼容模式启用成功', 'success');
                
                // 测试原有函数是否被重写
                if (typeof printGpsrTag !== 'undefined') {
                    log('printGpsrTag 函数可用，测试调用...', 'info');
                    printGpsrTag('TEST_APV_' + Date.now());
                }
            } catch (error) {
                log('兼容模式启用失败: ' + error.message, 'error');
            }
        }
        
        // 查看缓存统计
        function testCacheStats() {
            if (typeof GpsrCacheManager === 'undefined') {
                log('GpsrCacheManager 未加载，无法查看统计', 'error');
                return;
            }
            
            try {
                var cacheStats = GpsrCacheManager.getCacheStats();
                var perfStats = GpsrCacheManager.getPerformanceStats();
                var health = GpsrCacheManager.healthCheck();
                
                log('缓存统计: ' + JSON.stringify(cacheStats), 'info');
                log('性能统计: ' + JSON.stringify(perfStats), 'info');
                log('健康检查: ' + JSON.stringify(health), 'info');
            } catch (error) {
                log('获取统计信息失败: ' + error.message, 'error');
            }
        }
        
        // 页面加载完成后检查状态
        window.onload = function() {
            setTimeout(checkIntegrationStatus, 100);
            log('GPSR集成测试页面已加载', 'info');
        };
    </script>
    
    <!-- 尝试加载GPSR相关脚本 -->
    <script src="/wms/js/gpsr-cache-manager.js"></script>
    <script src="/wms/js/packing.js"></script>
</body>
</html>
