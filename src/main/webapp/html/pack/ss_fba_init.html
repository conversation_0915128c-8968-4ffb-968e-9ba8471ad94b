<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		#Ajaxlog div:first-child {
			color: red;
			font-weight: bold;
		}
		#condition{
			font-size: 18px;
		}
	</style>
</head>
<body>
<@header  method="header" active="15050000" ><#include "/ftl/header.ftl"></@header>
<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">发货扫描</a>
				<li class="active">中转仓单件包装</li>
			</ul>
		</div>
	</div>
	<#include "/common/pack_bgcolor_selector.html">

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div id="scan-area">
				<div class="w300px panel layout-panel layout-panel-west" style="width: 250px; left: 0px; top: 0px;">
					<div class="panel-tool"></div>
				</div>
				<div class="panel-body layout-body mt10" data-options="region:'west',title:'扫描区'," title="" id="input_scan">
					<h3 style="display: inline-block">扫描区域</h3>
					<select class="input-medium" id = "waybillSize-id" disabled="true">
						<option value="1">100*100</option>
						<option value="2">100*150</option>
					</select>

					<label>拣货任务号/周转筐</label>
					<input type="text" class="input-mini" name="scanbox" id="scanbox" onkeypress="if(event.keyCode==13) { inputscanbox(this); return false;}"tabindex="4">

					<label style="margin-left: 15px">SKU</label>
					<input class="input-medium" type="text" tabindex="4"   onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" id="sku" name="sku">
					<button type="button" class="btn red" onclick="failPrint()">
						<i class="icon-print"></i> 打印失败面单
					</button>
					<span id="panel-title" style="display: inline-block;margin-left: 20px"><h1  style="color:blue;font-size:18px;">成功:核对区（成功：0，错误：0）  </h1></span>
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<button  type="button" class="btn red" onclick="openPrintDialog()">打印机配置</button>
				</div>
			</div>

			<div id="check-area" style="padding: 10px">
				<div class="panel-header" style="overflow: hidden;">
					<div style="width:90px;" class="fl panel-title2" id="panel-floor"></div>

				</div>
				<div style="height: 550px;" class="col-md-12">
					<div id="print-waybill" class="col-md-3">
						<div class="row">
							<div class="col-md-12">
								<button type="button" id="printBtn" style="display: none;margin-top: 20px; font-size: 20px" class="btn blue" onclick="printBtn()">打印</button>
								<iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto" id="shippingOrderFrame" name="shippingOrderFrame" width="100%" height="500px"></iframe>
								<iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto" id="lanshouFrame" name="lanshouFrame" width="100%" height="500px"></iframe>
							</div>
						</div>
					</div>
					<div id="scan_datas" class="col-md-8"></div>
					<div id="Ajaxlog" class="col-md-1" style="margin-left:-20px;">
						<div class="col-md-12"></div>
						<div class="col-md-12"></div>
						<div class="col-md-12"></div>
						<div class="col-md-12"></div>
						<div class="col-md-12"></div>
						<div class="col-md-12"></div>
						<div class="col-md-12"></div>
						<div class="col-md-12"></div>
						<div class="col-md-12"></div>
						<div class="col-md-12"></div>
					</div>
				</div>
				<div id="print_tag" style="width:100%; ">
				</div>
				<div id="temu_print"  style="width:100%;"></div>
				<div id="print_gpsr_tag" style="display: none;"></div>
			</div>
		</div>
	</div>

	<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/jquery.sound.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/pages/pack-style.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/print.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>

<script type="text/javascript">

	window.onload = function () {
		getPrinterList();
	};

	$("#waybillSize-id").val("${domain.waybillSize}");

	//记录扫描的apvNo
	var apvNo;
	//记录扫描的apvNo对应的trackingNumber(追踪号)
	var trackingNumber;

	//扫描拣货任务号 或者周转筐 得到的拣货任务号
	var scanTaskNo;
	var taskType;

	var jitAsn=false;
	var asnFirst=false;
	var boxMarkUrl;
	var purposeHouse;

	var cacheKey = "scan_success";
	$(document).ready(function(){
		input_init();

		var storage = new WebStorageCache();
		if (storage.get(cacheKey)) {
			lastSuc = storage.get(cacheKey);
			$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+lastSuc+'</b></h1>');
		}
		initPrinter();
	});

	// 初始化
	function input_init(){
		$('#sku').val('');
		$('#sku').focus();
	}

	//扫描框
	function inputscanbox(obj){

		if(!obj.value || obj.value.replace(/\s/g,'') == ''){
			layer.alert("请输入周转框 或者 拣货任务号!");
			return ;
		}

		var sacnBoxNo = obj.value.replace(/\s/g,'');

		sacnBoxNo = $.trim(sacnBoxNo);

		//中转仓单件
		var pickType = 51;

		var r= $.ajax({
			url : CONTEXT_PATH + "fba/packs/box/scan",
			data : {box : sacnBoxNo, pickType : pickType},
			timeout : 100000,
			success : function(response){

				if(response.status == '200'){
					scanTaskNo = response.message;
					$('#sku').select().focus();

				}else{
					layer.alert(response.message, {closeBtn: 0}, function (index) {
						layer.close(index);
						$('#scanbox').val('');
						// 找不到订单
						$('#scanbox').select().focus();
					});

				}
			},
			error:function(){
				layer.alert('扫描失败，请重新扫描');
			}
		});

	}


	//扫描 sku
	function inputnext(obj) {
		if (!jitPrinter || !jitPrinterTag || !jitPrinter75Tag) {
			layer.alert("请先配置打印机",'error');
			return
		}
		input_submit(obj);
		input_init();
	}

	function printBtn() {
		var diglog = dialog({
			title: '仓库拒收提醒',
			width: 420,
			height: 80,
			content: '<h4>'+'打印箱唛后请务必保证在出库前打印揽收单，确保发货单不会被退回'+'<h4>',
			okValue: '知道了,继续打印',
			ok: function () {diglog.show();},
		});
		diglog.show();
	}


	// 统计扫描成功数量
	function calsf(){

		var storage = new WebStorageCache();

		var lastSuc = 0;
		if (storage.get(cacheKey)) {
			lastSuc = storage.get(cacheKey);
		}

		var suc = 1 + lastSuc;

		storage.set(cacheKey, suc, {exp : 5 * 60 * 60});

		$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+suc+'</b></h1>');
	}

	//获取打印和已经扫描的数据
	function input_submit(obj) {

		var val = $('#sku').val();
		//防止查询大数据
		if(!val || val.trim() == ''){
			layer.alert("请输入有效sku!");
			return ;
		}

		//兼容SKU编码

		//val = getSkuByBarCode(obj);

		var uuid = val.trim();

		if(!(val.indexOf("=") == -1)){
			var realSku = val.split('=')[0];
			$('#sku').val(realSku);
		}

		var sku = $('#input_scan').find('input,select').serialize();
		sku = sku +"&uuid=" + uuid ;

		//面单尺寸
		var waybillSize = $("#waybillSize-id").val();

		if(!scanTaskNo || scanTaskNo.trim() == ''){
			sku = sku +"&waybillSize=" + waybillSize ;
		}else{
			sku = sku +"&waybillSize=" + waybillSize + "&scanTaskNo=" + scanTaskNo;
		}

		var date = new Date();
		//核对提交数据
		var r = $.ajax({
			type : "get",
			url :CONTEXT_PATH +"fba/packs/ss/scan/sku?time=" + date,
			data : sku,
			timeout : 100000,
			async : false,
			beforeSend : function() {
				App.blockUI(null, null, 500);

				$("#sku").attr("disabled", true);
			},
			success : function(r) {
				$('#scan_datas').html(r);
				if($(r).find(".scan_success_sub").length == 1) {

					audioPlay('success');
					var id = $(r).find("input[name='id']").val();
					apvNo = $(r).find("input[name='apvNo']").val();
					trackingNumber = $(r).find("input[name='trackingNumber']").val();
					jitAsn = $(r).find("input[name='jitAsn']").val() == 'true';
					boxMarkUrl = $(r).find("input[name='boxMarkUrl']").val();
					purposeHouse = $(r).find("input[name='purposeHouse']").val();
					asnFirst = $(r).find("input[name='asnFirst']").val() == 'true';
					if (purposeHouse && (purposeHouse == 'Shein' || purposeHouse == 'TEMU')){
						asnFirst = false;
					}
					var options = {
						year: 'numeric',
						month: '2-digit',
						day: '2-digit',
						hour: '2-digit',
						minute: '2-digit',
						second: '2-digit',
						hour12: false
					};
					var content = '<div class="col-md-12">' + apvNo +'<br/>('+trackingNumber+')'
							+"<br/><span style='margin-left:36px;white-space: nowrap;'>"+date.toLocaleString('zh-CN', options).replace(/\//g, "-");
					+ '<span/></div>';

					$("#Ajaxlog").prepend(content);
					$("#Ajaxlog div:last-child").remove();

					addWhUniqueSkuLog(uuid, apvNo);
					debugger;
					if (purposeHouse == undefined || purposeHouse != 'TEMU') {
						//打印面单
						printApvNo(id);
						//统计数量(成功和失败)
						calsf();
					}

                    var sku = $('#sku').val();

					if (jitAsn){
						printJitTag(apvNo, sku, null);
					} else if (asnFirst) {
						let temuCodeUrl = $(r).find('#temuCodeUrl-' + sku).val();
						if (temuCodeUrl) {
							var tagFrame = "<iframe src='"+temuCodeUrl+"'  width='50%' height='300px'></iframe>";
							$('#print_tag').append(tagFrame);
							printPdfCopies(temuCodeUrl, jitPrinterTag, "70mm", "30mm", 1);
						}
					} else {
						if (purposeHouse && purposeHouse == 'TEMU') {
							scanTaskNo = $(r).find("#taskNo").val();
							taskType = $(r).find("#taskType").val();
							temuCheckPass(apvNo, sku, id, scanTaskNo);
						} else {
							//打印其它面单
							printTag(id, sku);
							//打印JIT货品面单
							printJitTag(apvNo, sku, null);
						}
					}

					if (purposeHouse && purposeHouse == 'Shein') {
						printLocalGpsrTag(apvNo, null, false);
					}

					if (jitAsn){
						$('#printBtn').show();
						//printAsnLanShou(id);
					}
					input_init();

				} else {
					App.unblockUI();
					addWhUniqueSkuLog(uuid, '');
					audioPlay('error');
					const errorElement = $(r).find("#scan-error");
					const errorMessage = errorElement.length ? errorElement.text().trim() : "未匹配到单据，请重试";
					layer.alert(errorMessage, {
						closeBtn: false,
						skin: 'layui-layer-lan'
					}, function(index) {
						layer.close(index);
						$('#sku').select().focus();
					});

				}

				// 防止重复扫
				setTimeout(removeDisabled, 1000);
			},
			error : function() {
				layer.alert('扫描失败，请重新扫描/' + sku);
			}
		});
	}

	function printBtn() {
		var diglog = dialog({
			title: '仓库拒收提醒',
			width: 420,
			height: 80,
			content: '<h4>'+'打印箱唛后请务必保证在出库前打印揽收单，确保发货单不会被退回'+'<h4>',
			okValue: '知道了,继续打印',
			ok: function () {diglog.show();},
		});
		diglog.show();
	}

	function printAsnLanShou(id) {
		var url = CONTEXT_PATH+"fba/allocation/printAsnLanShou?id=" + id;
		$.get(url, function(data){
			if (data.status == 200) {
				document.getElementById('lanshouFrame').src = data.message;
				printCopies(data.location, null, jitPrinter, 1, null);
			} else {
				customizeLayer(data.message);
			}
		});
	}

	function removeDisabled() {
		$("#sku").removeAttr("disabled");

		$('#sku').focus();
	}

	function printTag(id, sku) {
		$.ajax({
			url:CONTEXT_PATH +"fba/packs/printTag?id="+id+"&sku="+sku,
			type:"GET",
			data:{id:id},
			success : function(response){
				$('#print_tag').html(response);
				var printUrl = $("input[name='printUrl']").val();
				var purposeHouse = $(response).find("input[name='purposeHouse']").val();
				if (printUrl) {
					printUrl = window.location.origin + printUrl;
					var pageLength = "60mm";
					var pageWidth = "60mm";
					printPdfCopies(printUrl, jitPrinter75Tag, pageLength, pageWidth, 1);
				} else {
					var map = new Map();
					$("input[name='base64']").each(function () {
						var key = $(this).val();
						var num = map.get(key);
						if (num === undefined) {
							num = 0;
						}
						num++;
						map.put(key, num);
					})
					map.each(function (k, v) {
						if(purposeHouse && purposeHouse == 'Shein'){
							printWeirdPdfCopies(k, jitPrinter75Tag, "70mm", "60mm", "70mm", "30mm", v);
						}else{
							printCopies(k, jitPrinterTag, null, v, null);
						}
					});
					// 打印合并后sku标签
					var mergePdfList = [];
					$("input[name='base64Sku']").each(function () {
						var pdf = $(this).val();
						mergePdfList.push(pdf);
					});
					var copies = $("input[name='printCopies']").val();
					pageLength = "70mm";
					pageWidth = "60mm";
					printMergeCopies(mergePdfList,jitPrinter75Tag,copies);
				}
			}
		});
	}



	/**
	 * 用于合并打印
	 * @param temuCodeUrls 合并打印的pdf列表
	 * @param printerTag 打印机
	 * @param copies 打印份数
	 */
	function printMergeCopies(mergePdfList,printerTag,copies){
		var LODOP = getLodop();
		LODOP.SET_PRINT_PAGESIZE(0, '70mm', '60mm', 'Note'); // 设置纸张大小
		$(mergePdfList).each(function(index,item){
			// 缩放pdf的内容，将2张pdf弄到一个页面上
			if (index == 0){
				LODOP.ADD_PRINT_PDF("1mm","0mm","70mm","20mm", item);
			}
			if (index == 1){
				LODOP.ADD_PRINT_PDF("21.39mm","0mm","70mm","40mm", item);
			}
		});
		LODOP.SET_PRINT_STYLEA(0,"PDFScaleMode",0);
		if (LODOP.SET_PRINTER_INDEX(printerTag)) {
			if(copies === undefined){
				copies = 1;
			}
			LODOP.SET_PRINT_COPIES(copies); // 打印份数
			LODOP.PRINT(); // 静默打印
			// LODOP.PRINT_DESIGN();
		}
	}

	function printApvNo(id){
		if (asnFirst && boxMarkUrl) {
			document.getElementById('shippingOrderFrame').src = boxMarkUrl;
			printPdfCopies(boxMarkUrl, jitPrinter, "100mm", "100mm", 1);
			return;
		}
		var url = CONTEXT_PATH + "fba/packs/printXiangmai?id="+id;
		if (jitAsn) {
			url = CONTEXT_PATH + 'fba/allocation/localPrintXiangmai?id=' + id;
		}
		var r= $.ajax({
			url : url,
			timeout : 100000,
			success : function(response){
				if(response.status == '200'){
					var jitPdfUrl = response.body.jitPdfUrl;
					if (jitAsn) {
						jitPdfUrl = response.body.pdfUrl;
					}
					if (!jitPdfUrl) {
						jitPdfUrl = window.location.origin + CONTEXT_PATH + response.message;
						document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + response.message;
					} else {
						document.getElementById('shippingOrderFrame').src = response.message;
					}
					var jitBoxNumber = response.location;
					printCopies(jitPdfUrl, null, jitPrinter, 1, jitBoxNumber);
				}else{
					layer.alert(response.message, {closeBtn: 0}, function (index) {
						layer.close(index);
					});
				}
			},
			error:function(){
				layer.alert('扫描失败，请重新扫描');
			}
		});



	}

	function printCopies(message, printerTag, printer, copies, jitBoxNumber){
		var LODOP = getLodop();
		var printerName;

		if (printer){
			LODOP.SET_PRINT_PAGESIZE(0, '100mm', '100mm', 'Note'); // 设置纸张大小
			LODOP.ADD_PRINT_PDF(0, 0, '100mm', '100mm', message);
			if (jitBoxNumber) {
				LODOP.ADD_PRINT_TEXT(320, 12, 200, 31, jitBoxNumber);
				LODOP.SET_PRINT_STYLEA(0, "FontSize", 35);
			}
			printerName=printer;
		}
		if(printerTag){
			LODOP.SET_PRINT_PAGESIZE(0, '70mm', '30mm', 'Note'); // 设置纸张大小
			LODOP.ADD_PRINT_PDF(0, 0, '70mm', '30mm', message);
			printerName=printerTag;
		}
		if (LODOP.SET_PRINTER_INDEX(printerName)) {
			if(copies === undefined){
				copies = 1;
			}
			LODOP.SET_PRINT_COPIES(copies); // 打印份数
			LODOP.PRINT(); // 静默打印
		}
	}

	var isFocus = false;
	$("body").click(function() {isFocus = false;});
	function focusSku(){
		if (isFocus) {
			$('#sku').focus();
			setTimeout(focusSku, 1000);
		}
	}

	// 撤销已经打印的订单
	function revoke(apvId, apvNo) {
		if (purposeHouse &&purposeHouse == 'TEMU'){
			layer.alert("TEMU无撤销打印功能");
			return;
		}
		if(confirm("确定要撤销已经打印的订单？")) {
			var r = $.ajax({
				type : "get",
				url :CONTEXT_PATH+"fba/packs/revoke" ,
				data : {"apvId": apvId,"apvNo":apvNo},
				timeout : 100000,
				beforeSend : function() {
				},
				success : function(r) {

					$("#scan-apv-" + apvId).remove();
					// $('#printHtml').attr('src', '');
					$('#sku').focus();

					subtractsf();

					var msg = "修改成功";
					layer.alert(msg);

				},
				error : function() {
				}
			});
		}
	}

	// 撤销减数量
	function subtractsf(){

		var storage = new WebStorageCache();

		var lastSuc = 0;
		if (storage.get(cacheKey)) {

			var lastSuc = storage.get(cacheKey);

			var suc = lastSuc - 1;

			if(suc < 0) {
				suc = 0;
			}

			storage.set(cacheKey, suc, {exp : 5 * 60 * 60});

			$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+suc+'</b></h1>');
		}
	}

	//打印失败发货单
	function failPrint(){
		if(apvNo){
			document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + "apvs/failPrint?apvNo=" + apvNo;
		}
		$('#sku').focus();
	}

	// 添加唯一码包装日志
	function addWhUniqueSkuLog(uuid, apvNo) {
		var r = $.ajax({
			type : "get",
			url :CONTEXT_PATH+"apv/packs/addWhUniqueSkuLog" ,
			data : {uuid : uuid, apvNo: apvNo, type: 1, packType: 12},
			timeout : 100000,
			beforeSend : function() {
			},
			success : function(responese) {

			},
			error : function() {
			}
		});
		let val = $("#waybillSize-id").val();
		if(val == 1){
			val = 2;
		}else {
			val = 3;
		}
		addPackExceptionRecord(uuid, apvNo, val);
	}

	// 新增包装异常记录-未匹配发货单
	function addPackExceptionRecord(uuid, apvNo, packPage){
		if(apvNo != undefined && apvNo != ''){
			return;
		}
		$.ajax({
			type: "POST",
			url: CONTEXT_PATH+"apv/packs/createPackExceptionUuidItem",
			data: {uuid: uuid, taskNo: scanTaskNo, scanPage: packPage, packExceptionType: 2},
			success: function(response) {
			},
			error:function () {
			}
		});
	}

	// temu完成包装
	function temuCheckPass(packageSn, sku, apvId, taskNo){
		let quantity = 1;
		let url = "${CONTEXT_PATH}temu/pack/check/pass";
		let param = {"apvId": apvId, "taskNo": taskNo};
		// PASS
		$.get(url, param, function(response) {
			if(response.status == '200'){
				//统计数量(成功和失败)
				calsf();
				$("#temu_print").html('<div class="col-md-3" name="printHtml" id="printHtml"></div>\n' +
						' <div class="col-md-3" id="print_xiangm"></div>');
				printXiangMai(packageSn, sku);
				setTimeout(function () { printFbaNoAndFnSku(packageSn, sku, quantity);
				}, 500);
			} else {
				layer.alert("数据提交失败!请重试。");
			}
		});
	}

	function bindStockOut(box, orderNo, sku,packQty) {
		var diglog = dialog({
			title: '绑定中转仓播种异常周转筐',
			width: 350,
			height: 100,
			url: CONTEXT_PATH + "single/batch/scans/binding/stockout",
			okValue: '确定',
			ok: function () {
				var exportWindow = $(this.iframeNode.contentWindow.document.body);

				var submitForm = exportWindow.find("#submit-form");

				var stockoutBox = $(submitForm).find("#stockout-box").val();

				if (!stockoutBox || stockoutBox == '' || stockoutBox.indexOf('BZYC') == -1) {
					layer.alert("请绑定中转仓播种异常周转筐!");
					$(submitForm).find("#stockout-box").val('');
					return false;
				}
				bindingAndFinish(null, stockoutBox, box, orderNo, sku, diglog, packQty);
			},
		});
		diglog.show();
	}

	function bindingAndFinish(boxCayi, stockoutBox, box, orderNo, sku, diglog, packQty) {
		var result;
		$.ajax({
			url : CONTEXT_PATH + "temu/pack/binding/stockout",
			type : 'post',
			async: false,//使用同步的方式,true为异步方式
			data: {
				boxCayi: $.trim(boxCayi),
				stockoutBox: $.trim(stockoutBox),
				box: box,
				orderNo: orderNo,
				sku: sku,
				packQty: packQty
			},//这里使用json对象
			success : function(data){
				if(data.status == 200){
					layer.alert("绑定成功！");
					$('#check_scan_datas').html('');
					$('#sku').val("");
					$('#sku').select().focus();
					if (diglog != null){
						setTimeout(function () {
							diglog.close().remove();
						}, 100);
					}
				} else {
					layer.alert("绑定失败:"+data.message,function (index){
						layer.close(index);
						if (boxCayi){
							unPackBind();
						} else {
							bindStockOut(box, orderNo, sku, packQty);
						}
					});
				}
			}
		});
	}

	//播种差异
	function unPackBind() {

		var packageSn = apvNo;
		var sku = $("#sku").val();
		var box = $('#orderid').val();
		var boxCayi = '';

		var checkQty = parseInt($('#check_quantity').text());
		var pickQty = parseInt($('#need_qty').text());
		var packQty = pickQty - checkQty;
		if (packQty == 0) {
			layer.alert("拣货数量和核对数量相等，不需要绑定播种差异周转筐");
			return;
		}

		var diglog = dialog({
			title: '绑定中转仓播种差异周转筐',
			width: 350,
			height: 100,
			url: CONTEXT_PATH + "single/batch/scans/binding/gridDiff",
			okValue: '确定',
			ok: function () {
				var exportWindow = $(this.iframeNode.contentWindow.document.body);
				var submitForm = exportWindow.find("#submit-form");
				boxCayi = $(submitForm).find("#grid-diff-box").val();

				if (!boxCayi || boxCayi == '' || boxCayi.indexOf('BZCY') == -1) {
					layer.alert("请绑定中转仓播种差异周转筐!");
					$(submitForm).find("#grid-diff-box").val('');
					return false;
				}
				bindingAndFinish(boxCayi, null, box, packageSn, sku, diglog, packQty);
			},
		});
		diglog.show();
	}

	function printXiangMai(packageSn, sku){
		if (!packageSn || !sku) {
			layer.alert("包裹号-SKU为空!");
			return;
		}
		$.ajax({
			url : CONTEXT_PATH + "separateBox/search",
			type:'POST',
			data : {taskNoAndSku : packageSn+"~"+sku},
			success : function(response) {
				var responseHtml = $(response).find("#show_contents").html();
				var errorMsg = $(response).find("#show_contents").find("#scan-error").text();
				if(errorMsg){
					layer.alert(errorMsg);
					return;
				}
				$("#print_xiangm").html(responseHtml);
				var printHtml = $(response).find("#show_contents").find("#print_content").html();
				if (printHtml) {
					printHtmCopies(printHtml,jitPrinter,1);
				} else {
					var boxNumber = $(response).find("#show_contents").find("#box-number").html();
					if (boxNumber) {
						var asnPrintUrl = document.querySelector("#printTemuTagUrlFrame").src;
						doAsnPrint(asnPrintUrl, boxNumber);
					}
				}
			},
			error : function() {
				layer.alert('打印面单失败!');
			}
		});
	}

	function printFbaNoAndFnSku(orderNo, sku,quantity) {
		if (orderNo && sku) {
			var printPageUrl = CONTEXT_PATH + "skuLabel/printSkuLabel?orderNo=" + orderNo + "&sku=" + sku;
			$.get(printPageUrl, function (data) {
				$("#printHtml").html(data);
				// var printOrderSkuHtml = $("#printHtml").find("#print-item-1").html();
				// if (printOrderSkuHtml){
				// 	printHtmlCopies(printOrderSkuHtml, 1);
				// }
				var printEtHtml = $("#printHtml").find("#dz-print").html();
				if (printEtHtml){
					setTimeout(function () {
						etPrint(printEtHtml, quantity);
					}, 500);
				}
				var skuTag = $("#printHtml").find("#skuTag").val();
				if (skuTag && skuTag.indexOf("宠物") != -1) {
					setTimeout(function () {
						printPetTag(null, quantity);
					}, 500);
				}
				var errorMsg = $("#printHtml").find("#error-msg").html();
				if (errorMsg) {
					layer.alert(errorMsg, {closeBtn: 0}, function (index) {
						layer.close(index);
						$('#sku').select().focus();
					});
					return;
				}
				var printHtml = $("#printHtml").find("#print-item-0").html();
				setTimeout(function () {
					if (printHtml) {
						printTemuLabelCode(printHtml, quantity);
					} else {
						var asnPrintUrl = document.querySelector("#printUrlFrame").src;
						printTemuLabel(asnPrintUrl, jitPrinter75Tag, quantity);
					}
				}, 1000);

				var gpsrTag = $("#printHtml").find("#temu-gpsr").val();
				if (gpsrTag) {
					printPdfCopies(window.location.origin + gpsrTag, jitPrinter75Tag, "70mm", "60mm", quantity);
				}
			});
		}
	}
</script>
</body>
</html>