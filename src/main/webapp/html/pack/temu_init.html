<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
	</style>
</head>
<body>
<@header method="header" active="15000000" ><#include "/ftl/header.ftl"></@header>
<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">发货扫描</a>
				<li class="active">拼多多包装</li>
			</ul>
		</div>
	</div>
	<#include "/common/pack_bgcolor_selector.html">

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div>
				<div class=" panel layout-panel layout-panel-west" >
					<div class="panel-tool"></div>
				</div>
				<div >
					<h3 style="display: inline-block">扫描区域</h3>
					<input  type="hidden" value="" id="apv-no-now"/>
					<input  type="hidden" value="" id="sku-now"/>

					<label>拣货任务号/周转筐</label>
					<input type="text" class="input-mini" name="orderid" id="orderid" onkeypress="if(event.keyCode==13) { inputorderid(this); return false;}"tabindex="4">

					<label style="margin-left: 15px">SKU唯一码</label>
					<input class="input-medium" type="text" tabindex="4"  onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" id="sku" name="sku">

					<div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-title"><h1  style="color:blue;font-size:48px;">成功:<b>0</b></h1></div>
					<div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-floor"></div>
					<div style="display: inline-block;margin: 0 10px" class="panel-title2" id="panel-title-piece"><h1  style="color:red;font-size:48px;">计数:<b>0</b></h1></div>

					<button type="button" class="btn red" onclick="failPrint()">
						<i class="icon-print"></i> 打印失败面单
					</button>
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<button  type="button" class="btn red" onclick="openPrintDialog()">打印机配置</button>
					<span id="check-quantity" style="font-size: 36px;font-weight: 900;color: red;margin-left: 20px"></span>
				</div>

				<div style="height: 500px;" class="col-md-6">
					<div id="check_scan_datas"></div>
				</div>

				<div class="col-md-3" name="printHtml" id="printHtml"></div>
				<div class="col-md-3" id="scan_datas"></div>
			</div>
		</div>
	</div>
	<#include "/common/footer.html">

</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/jquery.sound.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/pages/pack-style.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/print.js?v=${.now?datetime}"></script>

<script type="text/javascript">
	window.onload = function () {
		getPrinterList();
	};


	var uuIdCacheKey = 'CHECK_PACKING_FOR_UUID_CACHE_KEY_TEMU'+ '_' +  new Date().getTime();
	var cacheKey = "temu_check_success";
	var pieceCacheKey = "temu_check_piece_success";

	//订单来源
	var sourceFrom ;

	var taskNo;

	var taskType;


	$(document).ready(function(){
		initSkuUuIdStorageCache(uuIdCacheKey);
		pageInit();

		$('#orderid').val('');
		$('#orderid').select().focus();

		var storage = new WebStorageCache();
		var lastSuc = 0;
		if (storage.get(cacheKey)) {
			lastSuc = storage.get(cacheKey);
			$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+lastSuc+'</b></h1>');
		}
		if (storage.get(pieceCacheKey)) {
			lastSuc = storage.get(pieceCacheKey);
			$('#panel-title-piece').html('<h1  style="color:red;font-size:48px;">计数:<b>'+lastSuc+'</b></h1>');
		}
		initPrinter();

		$('#orderid').focus();

	}); // end ready

	// 初始化
	function pageInit() {
		$('#sku').val('');
		// 完成之后不清空显示
		if (!completeCheck()){
			$('#check_scan_datas').html('');
		}
	}

	//周转筐 触发
	function inputorderid(obj){

		if(!obj.value || obj.value.replace(/\s/g,'') == ''){
			layer.alert("请输入拣货任务号/周转筐!");
			return ;
		}

		var orderId = obj.value.replace(/\s/g,'');

		orderId = $.trim(orderId);

		var url = CONTEXT_PATH + "temu/pack/box/scan";

		var r= $.ajax({
			url : url,
			data : {box : orderId},
			timeout : 100000,
			success : function(response){

				if (response.status == '200') {
					$('#sku').select().focus();
					if (response.body && response.body.whPickingTask) {
						var whPickingTask = response.body.whPickingTask;
						taskNo = whPickingTask.taskNo;
						taskType = whPickingTask.taskType;
					}
					var lessPick = response.location;
					if (taskType != 57 && lessPick) {
						layer.alert("存在拣货数量=0的包裹，请绑定中转仓播种异常周转筐", {closeBtn: 0}, function (index) {
							layer.close(index);
							bindStockOut(orderId, lessPick, null);
						});
					}

				} else {
					layer.alert(response.message, {closeBtn: 0}, function (index) {
						layer.close(index);
						$('#orderid').val('');
						// 找不到订单
						$('#orderid').select().focus();
					});

				}
			},
			error:function(){
				layer.alert('扫描失败，请重新扫描');
			}
		});

	}


	// 第一次扫描SKU
	function inputnext(obj){
		if (!jitPrinter || !jitPrinterTag || !jitPrinter75Tag){
			layer.alert("请先配置打印机",'error');
			$('#sku').val('');
			$('#sku').select().focus();
			return
		}
		var sku = obj.value.replace(/\s/g,'');
		var uuid = sku.trim();
		//扫描唯一码
		if (sku.indexOf("=") == -1) {
			layer.alert('请扫描SKU唯一码', {closeBtn: 0},function (index) {
				$('#sku').val('');
				$('#sku').select().focus();
				layer.close(index);
			});
			return;
		}
		var realSku = sku.split('=')[0];
		$('#sku').val(realSku);
		sku = realSku;
		checkScanPackingUniqueSku(sku, uuid);
	}

	// 校验唯一码重复扫描
	function checkScanPackingUniqueSku(sku, uuid) {
		var r = $.ajax({
			type : "get",
			url :CONTEXT_PATH+"temu/pack/checkUniqueSku" ,
			data : {uuid : uuid},
			timeout : 100000,
			beforeSend : function() {
				App.blockUI();
			},
			success : function(responese) {
				App.unblockUI();
				if (responese.status == '500') {
					layer.alert(responese.message, {closeBtn: 0},function (index) {
						layer.close(index);
						audioPlay('error');
						$('#sku').val("");
						$('#sku').focus();
					});
				} else {
					// 前端缓存校验是否重复扫描
					if(!checkRetrunUuIdStorageCache(uuIdCacheKey, uuid)){
						audioPlay('error');
						layer.alert("唯一码重复扫描！", {closeBtn: 0},function (index) {
							layer.close(index);
							$('#sku').val("");
							$('#sku').focus();
						});
						return;
					}
					var checkQuantity = parseInt($('#check_quantity').text());
					if (checkQuantity && checkQuantity > 0){
						secondScan(sku, uuid);
					} else {
						inputUniqueKey(sku, uuid);
					}
				}
			},
			error : function() {
				App.unblockUI();
				layer.alert('扫描失败，请重新扫描', {closeBtn: 0},function (index) {
					layer.close(index);
					audioPlay('error');
					$('#sku').val("");
					$('#sku').focus();
				});
			}
		});
	}


	function secondScan(sku, uuid) {
		var skuNow = $("#sku-now").val();
		if (skuNow && skuNow == sku) {
			checkIn(sku, uuid);
		} else {
			layer.alert("请先扫描完当前SKU" + skuNow, {closeBtn: 0}, function (index) {
				layer.close(index);
				audioPlay('error');
				$('#sku').val("");
				$('#sku').focus();
			});
			return;
			//inputUniqueKey(sku, uuid);
		}
	}

	//扫描唯一键
	function inputUniqueKey(sku, uuid){

		var uniqueKey = $.trim(uuid);
		var boxNo = $('#orderid').val();
		var r = $.ajax({
			url : CONTEXT_PATH + "temu/pack/check/sku",
			data: {uuid: uniqueKey, boxNo: boxNo},
			timeout : 90 * 1000,
			success : function(response){
				$("#check_scan_datas").html(response);

				var errorMsg = $("#check_scan_datas").find("#scan-error").html();
				if (errorMsg){
					layer.alert(errorMsg, {closeBtn: 0},function (index) {
						layer.close(index);
						audioPlay('error');
						$('#sku').val("");
						$('#sku').focus();
					});
					return;
				}
				if (response.length > 230){
					// 第一次扫描初始化
					initSkuUuIdStorageCache(uuIdCacheKey);
					isFocus = false;
					// 扫描成功
					$('#sku').select().focus();

					var apvNo = $('#check_scan_datas').find("input[name='packageSn']").val();

					sourceFrom = $('#check_scan_datas').find("input[name='sourceFrom']").val();

					//保存apvNo
					$("#apv-no-now").val(apvNo);

					$("#sku-now").val(sku);
					//暂时记录失败

					checkIn(sku, uuid);

				}else {
					audioPlay('error');
				}

			},
			error:function(){
				layer.alert('扫描失败，请重新扫描');
			}
		});

	}


	//核对sku和数量 正确(对应的sku数量增加)
	function checkIn(sku, uuid){
		sku = $.trim(sku.toUpperCase());
		var check_quantity = parseInt($('#check_quantity').text());
		if(completeCheck()){
			layer.alert('检查完毕',{closeBtn: 0},function (index) {
				layer.close(index);
				addWhUniqueSkuLog(uuid, '');
			});
			return;
		}
		if ($('[id="check_sku_' + sku + '"]').length == 0) {
			clear();
			$('#sku').select().focus();


			layer.alert("SKU不存在或已检查完毕",{closeBtn: 0},function (index) {
				layer.close(index);
				addWhUniqueSkuLog(uuid, '');
				audioPlay('error');
			});
			return;
		}

		if (check_quantity == 0){
			layer.alert('拿多了?',{closeBtn: 0},function (index) {
				layer.close(index);
				clear();
				audioPlay('error');
				addWhUniqueSkuLog(uuid, '');
			});
			return;
		}

		// 减少数量
		$('#check_quantity').html(check_quantity - 1);
		// 未扫描数量减少之后是否完成
		if (check_quantity == 1){
			$('#unPackBindBtn').attr("disabled","disabled");
			audioPlay('finish');
		}

		var apvNo = $('#check_scan_datas').find("input[name='packageSn']").val();
		addWhUniqueSkuLog(uuid, apvNo);
		// 未扫描数量
		var pickQty = parseInt($('#pick_qty').text());
		var orderQty = parseInt($('#prepare_qty').text());
		var packQty = pickQty - check_quantity + 1;
		//播种少拣绑定异常周转筐
		if (taskType != 57 && (pickQty == 0 || check_quantity == 1 && pickQty < orderQty)) {
			$('#unPackBindBtn').attr("disabled","disabled");
			var boxNo = $('#orderid').val();
			var packageSn = $("#apv-no-now").val();
			layer.alert("拣货数量小于分配数量，请绑定中转仓播种异常周转筐", {closeBtn: 0}, function (index) {
				layer.close(index);
				bindStockOut(boxNo, packageSn, sku,packQty);
			});
			return;
		}
		// 检查是否完成
		clear();
	}


	function completeCheck(){
		var checkQuantity = parseInt($('#check_quantity').text());
		if (checkQuantity == 0){
			return true;
		}
		return false;
	}

	//清空核对区
	function clear(){
		if (completeCheck()){
			calsf();
			pageInit();
		}else{
			$('#sku').val("");
			$('#sku').focus();
		}
	};

	// 统计扫描成功和失败的数量
	function calsf(){

		var storage = new WebStorageCache();

		var lastSuc = 0;
		if (storage.get(cacheKey)) {
			lastSuc = storage.get(cacheKey);
		}

		var suc = parseInt(1) + lastSuc;

		storage.set(cacheKey, suc, {exp : 5 * 60 * 60});

		$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+suc+'</b></h1>');

		// 记录后台
		var apvId = $('#check_scan_datas').find("input[name='apvId']").val();

		var quantity = parseInt($('#pick_qty').text());

		var url = "${CONTEXT_PATH}temu/pack/check/pass";
		var param = {"apvId": apvId, "taskNo": taskNo};

		$.get(url, param, function(response) {
			if(response.status == '200'){
				// 计件
				calsfPiece(quantity);
				var packageSn = $("#apv-no-now").val();
				var sku = $("#sku-now").val();

				printXiangMai(packageSn, sku);

				setTimeout(function () {
					printFbaNoAndFnSku(packageSn, sku, quantity);
				}, 500);

			} else {
				layer.alert("数据提交失败!请重试。",{closeBtn: 0},function (index) {
					layer.close(index);
				});
				return false;
			}
		});

	}

	// 计数
	function calsfPiece(quantity){

		if(!quantity) {
			quantity = 1;
		}

		var storage = new WebStorageCache();

		var lastSuc = 0;

		if (storage.get(pieceCacheKey)) {
			lastSuc = storage.get(pieceCacheKey);
			lastSuc = parseInt(lastSuc) + parseInt(quantity);
		} else {
			lastSuc = quantity;
		}

		storage.set(pieceCacheKey, lastSuc , {exp : 5 * 60 * 60});

		$('#panel-title-piece').html('<h1  style="color:red;font-size:48px;">计数:<b>'+lastSuc+'</b></h1>');
	}


	function bindStockOut(box, orderNo, sku,packQty) {
		var diglog = dialog({
			title: '绑定中转仓播种异常周转筐',
			width: 350,
			height: 100,
			url: CONTEXT_PATH + "single/batch/scans/binding/stockout",
			okValue: '确定',
			ok: function () {
				var exportWindow = $(this.iframeNode.contentWindow.document.body);

				var submitForm = exportWindow.find("#submit-form");

				var stockoutBox = $(submitForm).find("#stockout-box").val();

				if (!stockoutBox || stockoutBox == '' || stockoutBox.indexOf('BZYC') == -1) {
					layer.alert("请绑定中转仓播种异常周转筐!");
					$(submitForm).find("#stockout-box").val('');
					return false;
				}
				bindingAndFinish(null, stockoutBox, box, orderNo, sku, diglog, packQty);
			},
		});
		diglog.show();
	}
	//播种差异
	function unPackBind() {

		var packageSn = $("#apv-no-now").val();
		var sku = $("#sku-now").val();
		var box = $('#orderid').val();
		var boxCayi = '';

		var checkQty = parseInt($('#check_quantity').text());
		var pickQty = parseInt($('#pick_qty').text());
		var packQty = pickQty - checkQty;
		if (packQty == 0) {
			layer.alert("拣货数量和核对数量相等，不需要绑定播种差异周转筐");
			return;
		}

		var diglog = dialog({
			title: '绑定中转仓播种差异周转筐',
			width: 350,
			height: 100,
			url: CONTEXT_PATH + "single/batch/scans/binding/gridDiff",
			okValue: '确定',
			ok: function () {
				var exportWindow = $(this.iframeNode.contentWindow.document.body);
				var submitForm = exportWindow.find("#submit-form");
				boxCayi = $(submitForm).find("#grid-diff-box").val();

				if (!boxCayi || boxCayi == '' || boxCayi.indexOf('BZCY') == -1) {
					layer.alert("请绑定中转仓播种差异周转筐!");
					$(submitForm).find("#grid-diff-box").val('');
					return false;
				}
				bindingAndFinish(boxCayi, null, box, packageSn, sku, diglog, packQty);
			},
		});
		diglog.show();
	}

	function bindingAndFinish(boxCayi, stockoutBox, box, orderNo, sku, diglog, packQty) {
		var result;
		$.ajax({
			url : CONTEXT_PATH + "temu/pack/binding/stockout",
			type : 'post',
			async: false,//使用同步的方式,true为异步方式
			data: {
				boxCayi: $.trim(boxCayi),
				stockoutBox: $.trim(stockoutBox),
				box: box,
				orderNo: orderNo,
				sku: sku,
				packQty: packQty
			},//这里使用json对象
			success : function(data){
				if(data.status == 200){
					layer.alert("绑定成功！");
					$('#check_scan_datas').html('');
					$('#sku').val("");
					$('#sku').select().focus();
					if (diglog != null){
						setTimeout(function () {
							diglog.close().remove();
						}, 100);
					}
				} else {
					layer.alert("绑定失败:"+data.message,function (index){
						layer.close(index);
						if (boxCayi){
							unPackBind();
						} else {
							bindStockOut(box, orderNo, sku, packQty);
						}
					});
				}
			}
		});
	}

	//打印失败面单
	function failPrint(){
		var apvNo = $("#apv-no-now").val();
		if(apvNo){
			document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + "apvs/failPrint?apvNo=" + apvNo;
		}
		$('#sku').val('');
		$('#sku').focus();
	}

	// 添加唯一码包装日志
	function addWhUniqueSkuLog(uuid, apvNo) {
		if (apvNo){
			addUuIdStorageCache(uuIdCacheKey, uuid);
		}
		var r = $.ajax({
			type : "get",
			url :CONTEXT_PATH+"apv/packs/addWhUniqueSkuLog" ,
			data : {uuid : uuid, apvNo: apvNo, packType: 17},
			timeout : 100000,
			beforeSend : function() {
			},
			success : function(responese) {

			},
			error : function() {
			}
		});
		addPackExceptionRecord(uuid, apvNo);
	}

	// 新增包装异常记录-未匹配发货单
	function addPackExceptionRecord(uuid, apvNo){
		if(apvNo != undefined && apvNo != ''){
			return;
		}
		$.ajax({
			type: "POST",
			url: CONTEXT_PATH+"apv/packs/createPackExceptionUuidItem",
			data: {uuid: uuid, taskNo: taskNo, scanPage: 7, packExceptionType: 2},
			success: function(response) {
			},
			error:function () {
			}
		});
	}

	function printFbaNoAndFnSku(orderNo, sku,quantity) {
		if (orderNo && sku) {
			var printPageUrl = CONTEXT_PATH + "skuLabel/printSkuLabel?orderNo=" + orderNo + "&sku=" + sku;
			$.get(printPageUrl, function (data) {
				$("#printHtml").html(data);
				// var printOrderSkuHtml = $("#printHtml").find("#print-item-1").html();
				// if (printOrderSkuHtml){
				// 	printHtmlCopies(printOrderSkuHtml, 1);
				// }
				var printEtHtml = $("#printHtml").find("#dz-print").html();
				if (printEtHtml){
					setTimeout(function () {
						etPrint(printEtHtml, quantity);
					}, 500);
				}
				var skuTag = $("#printHtml").find("#skuTag").val();
				if (skuTag && skuTag.indexOf("宠物") != -1) {
					setTimeout(function () {
						printPetTag(null, quantity);
					}, 500);
				}
				var errorMsg = $("#printHtml").find("#error-msg").html();
				if (errorMsg) {
					layer.alert(errorMsg, {closeBtn: 0}, function (index) {
						layer.close(index);
						$('#sku').select().focus();
					});
					return;
				}
				var printHtml = $("#printHtml").find("#print-item-0").html();
				setTimeout(function () {
					if (printHtml) {
						printTemuLabelCode(printHtml, quantity);
					} else {
						var asnPrintUrl = document.querySelector("#printUrlFrame").src;
						printTemuLabel(asnPrintUrl, jitPrinter75Tag, quantity);
					}
				}, 1000);

				var gpsrTag = $("#printHtml").find("#temu-gpsr").val();
				if (gpsrTag) {
					printPdfCopies(window.location.origin + gpsrTag, jitPrinter75Tag, "70mm", "60mm", quantity);
				}
			});
		}
	}

	function printXiangMai(packageSn, sku){
		if (!packageSn || !sku) {
			layer.alert("包裹号-SKU为空!");
			return;
		}
		$.ajax({
			url : CONTEXT_PATH + "separateBox/search",
			type:'POST',
			data : {taskNoAndSku : packageSn+"~"+sku},
			success : function(response) {
				var responseHtml = $(response).find("#show_contents").html();
				var errorMsg = $(response).find("#show_contents").find("#scan-error").text();
				if(errorMsg){
					layer.alert(errorMsg);
					return;
				}
				$("#scan_datas").html(responseHtml);
				var printHtml = $(response).find("#show_contents").find("#print_content").html();
				if (printHtml) {
					printHtmCopies(printHtml,jitPrinter,1);
				} else {
					var boxNumber = $(response).find("#show_contents").find("#box-number").html();
					if (boxNumber) {
						var asnPrintUrl = document.querySelector("#printTemuTagUrlFrame").src;
						doAsnPrint(asnPrintUrl, boxNumber);
					}
				}
			},
			error : function() {
				layer.alert('打印面单失败!');
			}
		});
	}


	function printTemuLabel(url, printerTag, copies) {
		var pageLength = "70mm";//纸张长
		var pageWidth = "60mm";//纸张宽
		var LODOP = getLodop();
		LODOP.SET_PRINT_PAGESIZE(0, pageLength, pageWidth, 'Note'); // 设置纸张大小
		LODOP.ADD_PRINT_PDF(0,0,'100%','100%',url);
		var imgUrl = window.location.origin + CONTEXT_PATH + "file/pdf/jit/labelCode/huanbao.png";
		LODOP.ADD_PRINT_IMAGE(70,0,253,80,"<img border='0' src='"+imgUrl+"' width=2830 height=800 />");
		LODOP.SET_PRINT_STYLEA(0,"Stretch",2);
		LODOP.SET_PRINT_STYLEA(0,"TransColor","#FFFFFF");
		LODOP.ADD_PRINT_TEXT(150,7,200,20,"WARNING");
		LODOP.SET_PRINT_STYLEA(0,"Bold",1);
		LODOP.ADD_PRINT_TEXT(165, 12, 304, 58, "1. To avoid danger of suffocation, keep this plastic bag away from babies and children. Do not use this bag in cribs. beds, carriages or play pens.2. This bag is not a toy.");
		LODOP.SET_PRINT_STYLEA(0,"FontSize",7);
		LODOP.SET_PRINT_STYLEA(0,"Bold",1);
		if (LODOP.SET_PRINTER_INDEX(printerTag)) {
			if (copies === undefined) {
				copies = 1;
			}
			LODOP.SET_PRINT_COPIES(copies); // 打印份数
			//LODOP.PRINT_DESIGN(); // 打印设计
			//LODOP.PREVIEW(); // 打印设计
			LODOP.PRINT(); // 静默打印
		}
	}
</script>
</body>
</html>