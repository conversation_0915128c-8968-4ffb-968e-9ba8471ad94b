<!DOCTYPE html>
<html>
<head>
    <title>GPSR缓存管理器测试页面</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .button { padding: 8px 16px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #005a87; }
        .button.secondary { background: #6c757d; }
        .button.success { background: #28a745; }
        .button.danger { background: #dc3545; }
        .log-area { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 10px 0; }
        .stat-card { background: #f8f9fa; padding: 10px; border-radius: 5px; border: 1px solid #dee2e6; }
        .stat-value { font-size: 18px; font-weight: bold; color: #007cba; }
        .input-group { margin: 10px 0; }
        .input-group label { display: inline-block; width: 100px; }
        .input-group input, .input-group select { padding: 5px; border: 1px solid #ddd; border-radius: 3px; }
        .progress-container { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px; }
        .progress-bar { background: linear-gradient(90deg, #007cba, #28a745); }
        .test-step { margin: 5px 0; padding: 8px; border-radius: 3px; }
        .test-step.running { background: #fff3cd; border-left: 4px solid #ffc107; }
        .test-step.success { background: #d4edda; border-left: 4px solid #28a745; }
        .test-step.failed { background: #f8d7da; border-left: 4px solid #dc3545; }
        .test-step.pending { background: #f8f9fa; border-left: 4px solid #6c757d; }
        .auto-test-url { background: #e9ecef; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; word-break: break-all; }
        .batch-progress { margin: 10px 0; }
        .batch-item { margin: 5px 0; padding: 5px; border: 1px solid #dee2e6; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>GPSR缓存管理器测试页面</h1>

    <!-- 自动化测试控制 -->
    <div class="test-section" id="autoTestSection" style="display: none;">
        <h3>🤖 自动化测试进行中...</h3>
        <div class="progress-container" style="margin: 10px 0;">
            <div class="progress-bar" id="progressBar" style="width: 0%; height: 20px; background: #007cba; border-radius: 10px; transition: width 0.3s;"></div>
            <div class="progress-text" id="progressText" style="text-align: center; margin-top: 5px;">准备开始...</div>
        </div>
        <div class="test-results" id="testResults" style="margin: 10px 0;">
            <h4>测试结果：</h4>
            <div id="testResultsList"></div>
        </div>
        <button class="button secondary" onclick="stopAutoTest()">停止测试</button>
        <button class="button" onclick="exportTestResults()">导出结果</button>
        <button class="button success" onclick="copyTestResults()">复制结果</button>
    </div>

    <!-- 基础功能测试 -->
    <div class="test-section">
        <h3>基础功能测试</h3>
        <div class="input-group">
            <label>发货单号:</label>
            <input type="text" id="testApvNo" value="" placeholder="输入真实的发货单号，如：APV202412300001">
        </div>
        <div class="input-group">
            <label>SKU:</label>
            <input type="text" id="testSku" value="" placeholder="输入SKU（可选）">
        </div>
        <div class="input-group">
            <label>接口类型:</label>
            <select id="apiType">
                <option value="local">本地接口 (apv/packs/printGpsrTag)</option>
                <option value="fba">FBA接口 (fba/packs/printGpsrTag)</option>
            </select>
        </div>
        <button class="button" onclick="testPreload()">预加载GPSR</button>
        <button class="button success" onclick="testPrint()">打印GPSR</button>
        <button class="button secondary" onclick="testCheckCache()">检查缓存</button>
        <button class="button danger" onclick="testClearCache()">清理缓存</button>
        <button class="button" onclick="startAutoTest()" style="background: #28a745;">🚀 开始自动化测试</button>
    </div>

    <!-- 批量操作测试 -->
    <div class="test-section">
        <h3>批量操作测试</h3>
        <div class="input-group">
            <label>发货单列表:</label>
            <input type="text" id="batchApvNos" value="" placeholder="用逗号分隔多个真实发货单号，如：APV001,APV002,APV003">
        </div>
        <button class="button" onclick="testBatchPreload()">批量预加载</button>
        <button class="button secondary" onclick="testSmartPreload()">智能预加载</button>
    </div>

    <!-- 接口测试 -->
    <div class="test-section">
        <h3>接口测试</h3>
        <button class="button" onclick="testBackendApi()">测试后端接口</button>
        <button class="button secondary" onclick="testNetworkConnectivity()">测试网络连接</button>
        <button class="button secondary" onclick="validateGpsrData()">验证GPSR数据</button>
        <button class="button secondary" onclick="testRealApvNo()">测试真实发货单</button>
    </div>

    <!-- 配置和监控 -->
    <div class="test-section">
        <h3>配置和监控</h3>
        <button class="button secondary" onclick="showConfig()">显示配置</button>
        <button class="button secondary" onclick="showCacheStats()">缓存统计</button>
        <button class="button secondary" onclick="showPerformanceStats()">性能统计</button>
        <button class="button secondary" onclick="showHealthCheck()">健康检查</button>
        <button class="button" onclick="testConfigUpdate()">更新配置</button>
    </div>

    <!-- 兼容性测试 -->
    <div class="test-section">
        <h3>兼容性测试</h3>
        <button class="button" onclick="enableCompatibility()">启用兼容模式</button>
        <button class="button success" onclick="testOriginalFunction()">测试原函数调用</button>
        <button class="button secondary" onclick="testFallback()">测试降级机制</button>
    </div>

    <!-- 统计信息显示 -->
    <div class="test-section">
        <h3>实时统计</h3>
        <div class="stats-grid" id="statsGrid">
            <!-- 统计卡片将动态生成 -->
        </div>
    </div>

    <!-- 日志输出 -->
    <div class="test-section">
        <h3>操作日志</h3>
        <button class="button secondary" onclick="clearLog()">清理日志</button>
        <div class="log-area" id="logArea"></div>
    </div>

    <!-- 隐藏的GPSR容器 -->
    <div id="print_gpsr_tag" style="display: none;"></div>

    <!-- 引入依赖 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // 设置真实的CONTEXT_PATH - 请根据实际部署路径修改
        window.CONTEXT_PATH = 'CONTEXT_PATH/';

        // 模拟打印相关函数
        window.getLodop = function() {
            return {
                SET_PRINT_PAGESIZE: function() {},
                ADD_PRINT_PDF: function() {},
                ADD_PRINT_HTM: function() {},
                SET_PRINT_STYLEA: function() {},
                SET_PRINTER_INDEX: function() { return true; },
                SET_PRINT_COPIES: function() {},
                PRINT: function() { log('模拟打印执行'); }
            };
        };

        window.jitPrinter75Tag = 'MockPrinter75';

        // 模拟原有的打印函数
        window.printLocalGpsrTag = function(apvNo, sku, local) {
            log('调用原有printLocalGpsrTag函数：' + apvNo + (sku ? ', SKU: ' + sku : ''));
        };

        window.doPrintGpsrTag = function(html, copies, pdf) {
            log('调用doPrintGpsrTag函数，份数：' + copies + (pdf ? ', PDF: ' + pdf : ''));
        };
    </script>

    <!-- 引入GPSR缓存管理器 -->
    <script src="http://*************:8181/wms/js/gpsr-cache-manager.js"></script>

    <script>
        // 日志函数
        function log(message) {
            var logArea = document.getElementById('logArea');
            var timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += '[' + timestamp + '] ' + message + '\n';
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }

        // 更新统计显示
        function updateStats() {
            var cacheStats = GpsrCacheManager.getCacheStats();
            var perfStats = GpsrCacheManager.getPerformanceStats();
            var health = GpsrCacheManager.healthCheck();

            var statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div>缓存数量</div>
                    <div class="stat-value">${cacheStats.totalCount}/${cacheStats.maxSize}</div>
                </div>
                <div class="stat-card">
                    <div>缓存使用率</div>
                    <div class="stat-value">${(cacheStats.totalCount / cacheStats.maxSize * 100).toFixed(1)}%</div>
                </div>
                <div class="stat-card">
                    <div>性能指标数</div>
                    <div class="stat-value">${Object.keys(perfStats).length}</div>
                </div>
                <div class="stat-card">
                    <div>健康状态</div>
                    <div class="stat-value" style="color: ${health.status === 'healthy' ? '#28a745' : '#dc3545'}">${health.status}</div>
                </div>
            `;
        }

        // 测试函数
        function testPreload() {
            var apvNo = document.getElementById('testApvNo').value;
            var apiType = document.getElementById('apiType').value;

            if (!apvNo) {
                alert('请输入发货单号');
                return;
            }

            log('开始预加载：' + apvNo + ' (接口类型：' + apiType + ')');

            var options = {
                local: apiType === 'local'
            };

            GpsrCacheManager.preloadGpsrTag(apvNo, options)
                .then(function(result) {
                    log('预加载成功：' + apvNo + ', 平台：' + result.platform + ', PDF数量：' + result.pdfUrls.length);

                    // 显示详细信息
                    if (result.pdfUrls.length > 0) {
                        log('PDF URLs：' + result.pdfUrls.join(', '));
                    }

                    updateStats();
                })
                .catch(function(error) {
                    log('预加载失败：' + apvNo + ', 错误：' + error.message);
                    console.error('详细错误信息：', error);
                });
        }

        function testPrint() {
            var apvNo = document.getElementById('testApvNo').value;
            var sku = document.getElementById('testSku').value;

            if (!apvNo) {
                alert('请输入发货单号');
                return;
            }

            log('开始打印：' + apvNo + (sku ? ', SKU: ' + sku : ''));
            GpsrCacheManager.printGpsrTag(apvNo, sku)
                .then(function(result) {
                    if (result.fallback) {
                        log('打印成功（降级方式）：' + apvNo);
                    } else {
                        log('打印成功（缓存方式）：' + apvNo + ', 打印数量：' + result.printCount);
                    }
                })
                .catch(function(error) {
                    log('打印失败：' + apvNo + ', 错误：' + error.message);
                });
        }

        function testCheckCache() {
            var apvNo = document.getElementById('testApvNo').value;
            if (!apvNo) {
                alert('请输入发货单号');
                return;
            }

            var isCached = GpsrCacheManager.isCached(apvNo);
            log('缓存检查：' + apvNo + ' - ' + (isCached ? '已缓存' : '未缓存'));
        }

        function testClearCache() {
            var apvNo = document.getElementById('testApvNo').value;
            if (apvNo) {
                GpsrCacheManager.clearGpsrCache(apvNo);
                log('已清理指定缓存：' + apvNo);
            } else {
                GpsrCacheManager.clearGpsrCache();
                log('已清理所有缓存');
            }
            updateStats();
        }

        function testBatchPreload() {
            var apvNosStr = document.getElementById('batchApvNos').value;
            var apvNos = apvNosStr.split(',').map(function(s) { return s.trim(); }).filter(function(s) { return s; });
            var apiType = document.getElementById('apiType').value;

            if (apvNos.length === 0) {
                alert('请输入发货单号列表');
                return;
            }

            log('开始批量预加载：' + apvNos.join(', ') + ' (接口类型：' + apiType + ')');

            var options = {
                local: apiType === 'local',
                concurrency: 2
            };

            GpsrCacheManager.batchPreload(apvNos, options)
                .then(function(result) {
                    log('批量预加载完成：成功 ' + result.successCount + ', 失败 ' + result.errorCount);

                    // 显示详细结果
                    if (result.errors.length > 0) {
                        log('失败的发货单：');
                        result.errors.forEach(function(error) {
                            log('  - ' + error.apvNo + ': ' + error.error.message);
                        });
                    }

                    if (result.success.length > 0) {
                        log('成功的发货单：');
                        result.success.forEach(function(success) {
                            log('  - ' + success.apvNo + ': ' + success.result.platform);
                        });
                    }

                    updateStats();
                })
                .catch(function(error) {
                    log('批量预加载失败：' + error.message);
                    console.error('详细错误信息：', error);
                });
        }

        function testSmartPreload() {
            var apvNo = document.getElementById('testApvNo').value;
            if (!apvNo) {
                alert('请输入发货单号');
                return;
            }

            log('开始智能预加载：' + apvNo);
            GpsrCacheManager.smartPreload(apvNo);
        }

        function showConfig() {
            var config = GpsrCacheManager.getConfig();
            log('当前配置：' + JSON.stringify(config, null, 2));
        }

        function showCacheStats() {
            var stats = GpsrCacheManager.getCacheStats();
            log('缓存统计：' + JSON.stringify(stats, null, 2));
        }

        function showPerformanceStats() {
            var stats = GpsrCacheManager.getPerformanceStats();
            log('性能统计：' + JSON.stringify(stats, null, 2));
        }

        function showHealthCheck() {
            var health = GpsrCacheManager.healthCheck();
            log('健康检查：' + JSON.stringify(health, null, 2));
        }

        function testConfigUpdate() {
            GpsrCacheManager.setConfig({
                CACHE_MAX_SIZE: 15,
                DEBUG_MODE: true,
                PERFORMANCE_LOG: true
            });
            log('配置已更新');
            updateStats();
        }

        function enableCompatibility() {
            enableGpsrCompatibilityMode();
            log('兼容模式已启用');
        }

        function testOriginalFunction() {
            var apvNo = document.getElementById('testApvNo').value;
            if (!apvNo) {
                alert('请输入发货单号');
                return;
            }

            if (typeof printGpsrTag === 'function') {
                printGpsrTag(apvNo, null);
                log('调用printGpsrTag函数：' + apvNo);
            } else {
                log('printGpsrTag函数不存在');
            }
        }

        function testFallback() {
            // 模拟网络错误，测试降级机制
            var originalAjax = $.ajax;
            $.ajax = function() {
                return $.Deferred().reject('模拟网络错误').promise();
            };

            var apvNo = document.getElementById('testApvNo').value;
            if (!apvNo) {
                alert('请输入发货单号');
                return;
            }

            log('测试降级机制（模拟网络错误）：' + apvNo);
            GpsrCacheManager.printGpsrTag(apvNo)
                .then(function(result) {
                    log('降级测试成功：' + JSON.stringify(result));
                })
                .catch(function(error) {
                    log('降级测试失败：' + error.message);
                })
                .finally(function() {
                    // 恢复原始ajax函数
                    $.ajax = originalAjax;
                });
        }

        // 新增：测试后端接口
        function testBackendApi() {
            var apvNo = document.getElementById('testApvNo').value;
            var apiType = document.getElementById('apiType').value;

            if (!apvNo) {
                alert('请输入发货单号');
                return;
            }

            var url = window.CONTEXT_PATH + (apiType === 'local' ? 'apv/packs/printGpsrTag' : 'fba/packs/printGpsrTag') + '?apvNo=' + apvNo;

            log('测试后端接口：' + url);

            $.ajax({
                url: url,
                type: 'GET',
                timeout: 10000,
                success: function(response) {
                    log('接口调用成功，响应长度：' + response.length + ' 字符');

                    // 分析响应内容
                    var $response = $(response);
                    var gpsrTags = $response.find('.gpsr-tag-print');

                    if (gpsrTags.length > 0) {
                        log('找到 ' + gpsrTags.length + ' 个GPSR标签');

                        gpsrTags.each(function(index) {
                            var $tag = $(this);
                            var sku = $tag.find("input[name='printSku']").val();
                            var qty = $tag.find("input[name='printQty']").val();
                            var printUrl = $tag.find("input[name='printUrl']").val();

                            log('标签 ' + (index + 1) + '：SKU=' + sku + ', 数量=' + qty + (printUrl ? ', PDF=' + printUrl : ''));
                        });
                    } else {
                        log('响应中未找到GPSR标签，可能不是GPSR订单或接口返回错误');

                        // 检查是否有错误信息
                        var errorMsg = $response.find('.error, .alert-danger, #error-msg').text();
                        if (errorMsg) {
                            log('错误信息：' + errorMsg.trim());
                        }
                    }
                },
                error: function(xhr, status, error) {
                    log('接口调用失败：' + status + ' - ' + error);
                    log('HTTP状态码：' + xhr.status);
                    if (xhr.responseText) {
                        log('响应内容：' + xhr.responseText.substring(0, 200) + '...');
                    }
                }
            });
        }

        // 新增：测试网络连接
        function testNetworkConnectivity() {
            log('测试网络连接...');

            var testUrls = [
               'http://*************:8181/wms/apv/packs/printGpsrTag?apvNo=TEST',
               'http://*************:8181/wms/fba/packs/printGpsrTag?apvNo=TEST'
            ];

            testUrls.forEach(function(url, index) {
                $.ajax({
                    url: url,
                    type: 'HEAD', // 只请求头部，减少数据传输
                    timeout: 5000,
                    success: function() {
                        log('接口 ' + (index + 1) + ' 可访问：' + url);
                    },
                    error: function(xhr) {
                        log('接口 ' + (index + 1) + ' 不可访问：' + url + ' (状态码：' + xhr.status + ')');
                    }
                });
            });
        }

        // 新增：验证GPSR数据
        function validateGpsrData() {
            var apvNo = document.getElementById('testApvNo').value;
            if (!apvNo) {
                alert('请输入发货单号');
                return;
            }

            var cached = GpsrCacheManager.cache.get(apvNo);
            if (!cached) {
                log('发货单 ' + apvNo + ' 未缓存，请先预加载');
                return;
            }

            log('验证GPSR数据：' + apvNo);

            var $content = $(cached.content);
            var gpsrTags = $content.find('.gpsr-tag-print');

            if (gpsrTags.length === 0) {
                log('❌ 验证失败：未找到GPSR标签');
                return;
            }

            log('✅ 找到 ' + gpsrTags.length + ' 个GPSR标签');

            var validationResults = [];
            gpsrTags.each(function(index) {
                var $tag = $(this);
                var sku = $tag.find("input[name='printSku']").val();
                var qty = $tag.find("input[name='printQty']").val();
                var printUrl = $tag.find("input[name='printUrl']").val();

                var result = {
                    index: index + 1,
                    sku: sku,
                    qty: qty,
                    printUrl: printUrl,
                    valid: true,
                    issues: []
                };

                // 验证SKU
                if (!sku || sku.trim() === '') {
                    result.valid = false;
                    result.issues.push('SKU为空');
                }

                // 验证数量
                if (!qty || isNaN(parseInt(qty)) || parseInt(qty) <= 0) {
                    result.valid = false;
                    result.issues.push('数量无效');
                }

                // 验证PDF URL（如果是SMT平台）
                if (printUrl && printUrl.indexOf('pdf/gpsr') === -1) {
                    result.valid = false;
                    result.issues.push('PDF URL格式不正确');
                }

                validationResults.push(result);

                if (result.valid) {
                    log('✅ 标签 ' + result.index + ' 验证通过：SKU=' + sku + ', 数量=' + qty);
                } else {
                    log('❌ 标签 ' + result.index + ' 验证失败：' + result.issues.join(', '));
                }
            });

            var validCount = validationResults.filter(function(r) { return r.valid; }).length;
            log('验证完成：' + validCount + '/' + validationResults.length + ' 个标签有效');
        }

        // 新增：测试真实发货单
        function testRealApvNo() {
            var apvNo = document.getElementById('testApvNo').value;
            if (!apvNo) {
                alert('请输入发货单号');
                return;
            }

            log('测试真实发货单：' + apvNo);

            // 先测试接口可访问性
            testBackendApi();

            // 然后测试预加载
            setTimeout(function() {
                testPreload();
            }, 1000);

            // 最后测试打印
            setTimeout(function() {
                testPrint();
            }, 3000);
        }

        // 自动化测试相关变量
        var autoTestConfig = {
            stepDelay: 2000,        // 步骤间延迟（毫秒）
            retryCount: 2,          // 重试次数
            timeout: 10000,         // 超时时间（毫秒）
            skipPrint: false,       // 是否跳过打印测试
            skipValidation: false   // 是否跳过验证测试
        };

        var autoTestState = {
            isRunning: false,
            currentStep: 0,
            totalSteps: 5,
            results: [],
            startTime: null,
            currentApvNo: null,
            batchMode: false,
            batchItems: [],
            currentBatchIndex: 0
        };

        // URL参数解析
        function parseUrlParams() {
            var params = {};
            var urlParams = new URLSearchParams(window.location.search);

            // 基础参数
            params.apvNo = urlParams.get('apvNo') || '';
            params.sku = urlParams.get('sku') || '';
            params.apiType = urlParams.get('apiType') || 'local';

            // 自动化测试参数
            params.autoTest = urlParams.get('autoTest') === 'true';
            params.batchTest = urlParams.get('batchTest') === 'true';
            params.apvNos = urlParams.get('apvNos') || '';

            // 配置参数
            params.stepDelay = parseInt(urlParams.get('stepDelay')) || autoTestConfig.stepDelay;
            params.retryCount = parseInt(urlParams.get('retryCount')) || autoTestConfig.retryCount;
            params.timeout = parseInt(urlParams.get('timeout')) || autoTestConfig.timeout;
            params.skipPrint = urlParams.get('skipPrint') === 'true';
            params.skipValidation = urlParams.get('skipValidation') === 'true';

            return params;
        }

        // 应用URL参数到页面
        function applyUrlParams(params) {
            if (params.apvNo) {
                document.getElementById('testApvNo').value = params.apvNo;
            }
            if (params.sku) {
                document.getElementById('testSku').value = params.sku;
            }
            if (params.apiType) {
                document.getElementById('apiType').value = params.apiType;
            }
            if (params.apvNos) {
                document.getElementById('batchApvNos').value = params.apvNos;
            }

            // 更新配置
            autoTestConfig.stepDelay = params.stepDelay;
            autoTestConfig.retryCount = params.retryCount;
            autoTestConfig.timeout = params.timeout;
            autoTestConfig.skipPrint = params.skipPrint;
            autoTestConfig.skipValidation = params.skipValidation;

            log('URL参数已应用：' + JSON.stringify(params));
        }

        // 生成自动化测试URL
        function generateAutoTestUrl(apvNo, sku, apiType) {
            var baseUrl = window.location.origin + window.location.pathname;
            var params = new URLSearchParams();

            if (apvNo) params.set('apvNo', apvNo);
            if (sku) params.set('sku', sku);
            if (apiType) params.set('apiType', apiType);
            params.set('autoTest', 'true');

            return baseUrl + '?' + params.toString();
        }

        // 显示自动化测试URL
        function showAutoTestUrl() {
            var apvNo = document.getElementById('testApvNo').value;
            var sku = document.getElementById('testSku').value;
            var apiType = document.getElementById('apiType').value;

            if (!apvNo) {
                alert('请先输入发货单号');
                return;
            }

            var url = generateAutoTestUrl(apvNo, sku, apiType);

            var urlDiv = document.createElement('div');
            urlDiv.className = 'auto-test-url';
            urlDiv.innerHTML = '<strong>自动化测试URL：</strong><br>' + url +
                '<br><button onclick="copyToClipboard(\'' + url + '\')">复制URL</button>';

            document.getElementById('logArea').appendChild(urlDiv);
            log('自动化测试URL已生成');
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                log('已复制到剪贴板');
            }).catch(function(err) {
                log('复制失败：' + err);
            });
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            log('GPSR缓存管理器测试页面已加载');
            updateStats();

            // 解析URL参数
            var params = parseUrlParams();
            applyUrlParams(params);

            // 如果设置了自动测试，延迟启动
            if (params.autoTest) {
                log('检测到自动测试参数，3秒后开始自动化测试...');
                setTimeout(function() {
                    if (params.batchTest) {
                        startBatchAutoTest();
                    } else {
                        startAutoTest();
                    }
                }, 3000);
            }

            // 定期更新统计
            setInterval(updateStats, 5000);
        });

        // 自动化测试步骤定义
        var autoTestSteps = [
            {
                name: '网络连接测试',
                function: 'testNetworkConnectivity',
                required: true
            },
            {
                name: '后端接口测试',
                function: 'testBackendApi',
                required: true
            },
            {
                name: '预加载GPSR',
                function: 'testPreload',
                required: true
            },
            {
                name: '验证GPSR数据',
                function: 'testValidateGpsrData',
                required: false,
                skipCondition: 'skipValidation'
            },
            {
                name: '打印GPSR',
                function: 'testPrint',
                required: false,
                skipCondition: 'skipPrint'
            }
        ];

        // 开始自动化测试
        function startAutoTest() {
            var apvNo = document.getElementById('testApvNo').value;
            if (!apvNo) {
                alert('请输入发货单号');
                return;
            }

            autoTestState.isRunning = true;
            autoTestState.currentStep = 0;
            autoTestState.results = [];
            autoTestState.startTime = Date.now();
            autoTestState.currentApvNo = apvNo;
            autoTestState.batchMode = false;

            // 显示自动化测试区域
            document.getElementById('autoTestSection').style.display = 'block';

            // 初始化测试结果显示
            initTestResultsDisplay();

            log('🚀 开始自动化测试：' + apvNo);

            // 开始执行测试步骤
            executeNextStep();
        }

        // 开始批量自动化测试
        function startBatchAutoTest() {
            var apvNosStr = document.getElementById('batchApvNos').value;
            var apvNos = apvNosStr.split(',').map(function(s) { return s.trim(); }).filter(function(s) { return s; });

            if (apvNos.length === 0) {
                alert('请输入发货单号列表');
                return;
            }

            autoTestState.isRunning = true;
            autoTestState.batchMode = true;
            autoTestState.batchItems = apvNos.map(function(apvNo) {
                return {
                    apvNo: apvNo,
                    status: 'pending',
                    results: [],
                    startTime: null,
                    endTime: null
                };
            });
            autoTestState.currentBatchIndex = 0;
            autoTestState.startTime = Date.now();

            // 显示自动化测试区域
            document.getElementById('autoTestSection').style.display = 'block';

            // 初始化批量测试结果显示
            initBatchTestResultsDisplay();

            log('🚀 开始批量自动化测试：' + apvNos.length + ' 个发货单');

            // 开始执行批量测试
            executeNextBatchItem();
        }

        // 停止自动化测试
        function stopAutoTest() {
            autoTestState.isRunning = false;
            log('⏹️ 自动化测试已停止');

            // 隐藏自动化测试区域
            document.getElementById('autoTestSection').style.display = 'none';
        }

        // 初始化测试结果显示
        function initTestResultsDisplay() {
            var resultsList = document.getElementById('testResultsList');
            resultsList.innerHTML = '';

            autoTestSteps.forEach(function(step, index) {
                // 检查是否跳过该步骤
                if (step.skipCondition && autoTestConfig[step.skipCondition]) {
                    return;
                }

                var stepDiv = document.createElement('div');
                stepDiv.className = 'test-step pending';
                stepDiv.id = 'step-' + index;
                stepDiv.innerHTML = '<strong>' + step.name + '</strong> - 等待执行...';
                resultsList.appendChild(stepDiv);
            });

            updateProgress(0);
        }

        // 初始化批量测试结果显示
        function initBatchTestResultsDisplay() {
            var resultsList = document.getElementById('testResultsList');
            resultsList.innerHTML = '<div class="batch-progress"><h4>批量测试进度</h4></div>';

            autoTestState.batchItems.forEach(function(item, index) {
                var itemDiv = document.createElement('div');
                itemDiv.className = 'batch-item';
                itemDiv.id = 'batch-item-' + index;
                itemDiv.innerHTML = '<strong>' + item.apvNo + '</strong> - 等待测试...';
                resultsList.appendChild(itemDiv);
            });

            updateBatchProgress();
        }

        // 更新进度条
        function updateProgress(percentage) {
            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('progressText').textContent = Math.round(percentage) + '%';
        }

        // 更新批量测试进度
        function updateBatchProgress() {
            var completed = autoTestState.batchItems.filter(function(item) {
                return item.status === 'completed' || item.status === 'failed';
            }).length;

            var percentage = (completed / autoTestState.batchItems.length) * 100;
            updateProgress(percentage);

            document.getElementById('progressText').textContent =
                completed + '/' + autoTestState.batchItems.length + ' 完成 (' + Math.round(percentage) + '%)';
        }

        // 执行下一个测试步骤
        function executeNextStep() {
            if (!autoTestState.isRunning) {
                return;
            }

            var steps = autoTestSteps.filter(function(step) {
                return !step.skipCondition || !autoTestConfig[step.skipCondition];
            });

            if (autoTestState.currentStep >= steps.length) {
                // 所有步骤完成
                completeAutoTest();
                return;
            }

            var step = steps[autoTestState.currentStep];
            var stepIndex = autoTestSteps.indexOf(step);

            log('执行步骤 ' + (autoTestState.currentStep + 1) + '/' + steps.length + '：' + step.name);

            // 更新UI状态
            var stepDiv = document.getElementById('step-' + stepIndex);
            if (stepDiv) {
                stepDiv.className = 'test-step running';
                stepDiv.innerHTML = '<strong>' + step.name + '</strong> - 执行中...';
            }

            // 更新进度
            var percentage = (autoTestState.currentStep / steps.length) * 100;
            updateProgress(percentage);

            // 执行测试步骤
            executeTestStep(step, stepIndex, 0);
        }

        // 执行单个测试步骤
        function executeTestStep(step, stepIndex, retryCount) {
            var startTime = Date.now();

            try {
                // 根据步骤名称调用对应的测试函数
                var testPromise;
                switch (step.function) {
                    case 'testNetworkConnectivity':
                        testPromise = executeNetworkTest();
                        break;
                    case 'testBackendApi':
                        testPromise = executeBackendTest();
                        break;
                    case 'testPreload':
                        testPromise = executePreloadTest();
                        break;
                    case 'testValidateGpsrData':
                        testPromise = executeValidationTest();
                        break;
                    case 'testPrint':
                        testPromise = executePrintTest();
                        break;
                    default:
                        testPromise = Promise.reject(new Error('未知的测试步骤：' + step.function));
                }

                testPromise
                    .then(function(result) {
                        var endTime = Date.now();
                        var duration = endTime - startTime;

                        // 记录成功结果
                        var testResult = {
                            step: step.name,
                            status: 'success',
                            duration: duration,
                            result: result,
                            timestamp: new Date().toISOString()
                        };

                        autoTestState.results.push(testResult);

                        // 更新UI
                        var stepDiv = document.getElementById('step-' + stepIndex);
                        if (stepDiv) {
                            stepDiv.className = 'test-step success';
                            stepDiv.innerHTML = '<strong>' + step.name + '</strong> - ✅ 成功 (' + duration + 'ms)';
                        }

                        log('✅ ' + step.name + ' 成功完成，耗时：' + duration + 'ms');

                        // 继续下一步
                        autoTestState.currentStep++;
                        setTimeout(executeNextStep, autoTestConfig.stepDelay);
                    })
                    .catch(function(error) {
                        var endTime = Date.now();
                        var duration = endTime - startTime;

                        log('❌ ' + step.name + ' 失败：' + error.message + '，耗时：' + duration + 'ms');

                        // 检查是否需要重试
                        if (retryCount < autoTestConfig.retryCount) {
                            log('🔄 重试 ' + step.name + '，第 ' + (retryCount + 1) + ' 次重试');
                            setTimeout(function() {
                                executeTestStep(step, stepIndex, retryCount + 1);
                            }, 1000);
                        } else {
                            // 记录失败结果
                            var testResult = {
                                step: step.name,
                                status: 'failed',
                                duration: duration,
                                error: error.message,
                                timestamp: new Date().toISOString()
                            };

                            autoTestState.results.push(testResult);

                            // 更新UI
                            var stepDiv = document.getElementById('step-' + stepIndex);
                            if (stepDiv) {
                                stepDiv.className = 'test-step failed';
                                stepDiv.innerHTML = '<strong>' + step.name + '</strong> - ❌ 失败: ' + error.message;
                            }

                            // 继续下一步（非阻塞式）
                            if (!step.required) {
                                autoTestState.currentStep++;
                                setTimeout(executeNextStep, autoTestConfig.stepDelay);
                            } else {
                                // 必需步骤失败，停止测试
                                log('💥 必需步骤失败，停止自动化测试');
                                completeAutoTest();
                            }
                        }
                    });

            } catch (error) {
                log('💥 执行步骤时发生异常：' + error.message);

                // 记录异常结果
                var testResult = {
                    step: step.name,
                    status: 'error',
                    duration: Date.now() - startTime,
                    error: error.message,
                    timestamp: new Date().toISOString()
                };

                autoTestState.results.push(testResult);

                // 更新UI
                var stepDiv = document.getElementById('step-' + stepIndex);
                if (stepDiv) {
                    stepDiv.className = 'test-step failed';
                    stepDiv.innerHTML = '<strong>' + step.name + '</strong> - 💥 异常: ' + error.message;
                }

                // 继续下一步
                autoTestState.currentStep++;
                setTimeout(executeNextStep, autoTestConfig.stepDelay);
            }
        }

        // 具体的测试执行函数
        function executeNetworkTest() {
            return new Promise(function(resolve, reject) {
                var testUrls = [
                    window.CONTEXT_PATH + 'apv/packs/printGpsrTag?apvNo=TEST',
                    window.CONTEXT_PATH + 'fba/packs/printGpsrTag?apvNo=TEST'
                ];

                var results = [];
                var completed = 0;

                testUrls.forEach(function(url, index) {
                    $.ajax({
                        url: url,
                        type: 'HEAD',
                        timeout: autoTestConfig.timeout,
                        success: function() {
                            results[index] = { url: url, status: 'success' };
                            completed++;
                            if (completed === testUrls.length) {
                                resolve({ message: '网络连接正常', results: results });
                            }
                        },
                        error: function(xhr) {
                            results[index] = { url: url, status: 'failed', code: xhr.status };
                            completed++;
                            if (completed === testUrls.length) {
                                var failedCount = results.filter(function(r) { return r.status === 'failed'; }).length;
                                if (failedCount === testUrls.length) {
                                    reject(new Error('所有接口都不可访问'));
                                } else {
                                    resolve({ message: '部分接口可访问', results: results });
                                }
                            }
                        }
                    });
                });
            });
        }

        function executeBackendTest() {
            return new Promise(function(resolve, reject) {
                var apvNo = autoTestState.currentApvNo || document.getElementById('testApvNo').value;
                var apiType = document.getElementById('apiType').value;

                var url = window.CONTEXT_PATH + (apiType === 'local' ? 'apv/packs/printGpsrTag' : 'fba/packs/printGpsrTag') + '?apvNo=' + apvNo;

                $.ajax({
                    url: url,
                    type: 'GET',
                    timeout: autoTestConfig.timeout,
                    success: function(response) {
                        var $response = $(response);
                        var gpsrTags = $response.find('.gpsr-tag-print');

                        if (gpsrTags.length > 0) {
                            var tagInfo = [];
                            gpsrTags.each(function(index) {
                                var $tag = $(this);
                                tagInfo.push({
                                    sku: $tag.find("input[name='printSku']").val(),
                                    qty: $tag.find("input[name='printQty']").val(),
                                    printUrl: $tag.find("input[name='printUrl']").val()
                                });
                            });

                            resolve({
                                message: '后端接口调用成功',
                                responseLength: response.length,
                                gpsrTagCount: gpsrTags.length,
                                tags: tagInfo
                            });
                        } else {
                            reject(new Error('响应中未找到GPSR标签'));
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(new Error('接口调用失败：' + status + ' - ' + error));
                    }
                });
            });
        }

        function executePreloadTest() {
            return new Promise(function(resolve, reject) {
                var apvNo = autoTestState.currentApvNo || document.getElementById('testApvNo').value;
                var apiType = document.getElementById('apiType').value;

                var options = {
                    local: apiType === 'local'
                };

                GpsrCacheManager.preloadGpsrTag(apvNo, options)
                    .then(function(result) {
                        resolve({
                            message: '预加载成功',
                            platform: result.platform,
                            pdfCount: result.pdfUrls.length,
                            pdfUrls: result.pdfUrls
                        });
                    })
                    .catch(function(error) {
                        reject(new Error('预加载失败：' + error.message));
                    });
            });
        }

        function executeValidationTest() {
            return new Promise(function(resolve, reject) {
                var apvNo = autoTestState.currentApvNo || document.getElementById('testApvNo').value;

                var cached = GpsrCacheManager.cache.get(apvNo);
                if (!cached) {
                    reject(new Error('未找到缓存数据'));
                    return;
                }

                var $content = $(cached.content);
                var gpsrTags = $content.find('.gpsr-tag-print');

                if (gpsrTags.length === 0) {
                    reject(new Error('缓存中未找到GPSR标签'));
                    return;
                }

                var validationResults = [];
                var validCount = 0;

                gpsrTags.each(function(index) {
                    var $tag = $(this);
                    var sku = $tag.find("input[name='printSku']").val();
                    var qty = $tag.find("input[name='printQty']").val();
                    var printUrl = $tag.find("input[name='printUrl']").val();

                    var isValid = sku && qty && !isNaN(parseInt(qty)) && parseInt(qty) > 0;
                    if (isValid) validCount++;

                    validationResults.push({
                        index: index + 1,
                        sku: sku,
                        qty: qty,
                        printUrl: printUrl,
                        valid: isValid
                    });
                });

                resolve({
                    message: '数据验证完成',
                    totalTags: gpsrTags.length,
                    validTags: validCount,
                    validationResults: validationResults
                });
            });
        }

        function executePrintTest() {
            return new Promise(function(resolve, reject) {
                var apvNo = autoTestState.currentApvNo || document.getElementById('testApvNo').value;
                var sku = document.getElementById('testSku').value;

                GpsrCacheManager.printGpsrTag(apvNo, sku)
                    .then(function(result) {
                        resolve({
                            message: '打印测试成功',
                            printCount: result.printCount,
                            usedCache: !result.fallback,
                            fallback: result.fallback || false
                        });
                    })
                    .catch(function(error) {
                        reject(new Error('打印测试失败：' + error.message));
                    });
            });
        }

        // 完成自动化测试
        function completeAutoTest() {
            var endTime = Date.now();
            var totalDuration = endTime - autoTestState.startTime;

            autoTestState.isRunning = false;
            updateProgress(100);

            // 生成测试报告
            var report = generateTestReport(totalDuration);

            log('🎉 自动化测试完成，总耗时：' + totalDuration + 'ms');
            log('测试报告：' + JSON.stringify(report, null, 2));

            // 如果是批量测试，继续下一个
            if (autoTestState.batchMode) {
                completeBatchItem();
            }
        }

        // 生成测试报告
        function generateTestReport(totalDuration) {
            var successCount = autoTestState.results.filter(function(r) { return r.status === 'success'; }).length;
            var failedCount = autoTestState.results.filter(function(r) { return r.status === 'failed'; }).length;
            var errorCount = autoTestState.results.filter(function(r) { return r.status === 'error'; }).length;

            return {
                testInfo: {
                    apvNo: autoTestState.currentApvNo,
                    sku: document.getElementById('testSku').value,
                    apiType: document.getElementById('apiType').value,
                    startTime: new Date(autoTestState.startTime).toISOString(),
                    endTime: new Date().toISOString(),
                    totalDuration: totalDuration
                },
                summary: {
                    totalSteps: autoTestState.results.length,
                    successCount: successCount,
                    failedCount: failedCount,
                    errorCount: errorCount,
                    successRate: ((successCount / autoTestState.results.length) * 100).toFixed(2) + '%'
                },
                steps: autoTestState.results,
                config: autoTestConfig
            };
        }

        // 导出测试结果
        function exportTestResults() {
            var report;

            if (autoTestState.batchMode) {
                report = generateBatchTestReport();
            } else {
                var totalDuration = Date.now() - autoTestState.startTime;
                report = generateTestReport(totalDuration);
            }

            var dataStr = JSON.stringify(report, null, 2);
            var dataBlob = new Blob([dataStr], {type: 'application/json'});

            var link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'gpsr-test-report-' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.json';
            link.click();

            log('测试报告已导出');
        }

        // 复制测试结果
        function copyTestResults() {
            var report;

            if (autoTestState.batchMode) {
                report = generateBatchTestReport();
            } else {
                var totalDuration = Date.now() - autoTestState.startTime;
                report = generateTestReport(totalDuration);
            }

            var dataStr = JSON.stringify(report, null, 2);
            copyToClipboard(dataStr);
        }

        // 批量测试相关函数
        function executeNextBatchItem() {
            if (!autoTestState.isRunning || autoTestState.currentBatchIndex >= autoTestState.batchItems.length) {
                completeBatchTest();
                return;
            }

            var currentItem = autoTestState.batchItems[autoTestState.currentBatchIndex];
            currentItem.status = 'running';
            currentItem.startTime = Date.now();
            autoTestState.currentApvNo = currentItem.apvNo;

            // 更新UI
            var itemDiv = document.getElementById('batch-item-' + autoTestState.currentBatchIndex);
            if (itemDiv) {
                itemDiv.innerHTML = '<strong>' + currentItem.apvNo + '</strong> - 🔄 测试中...';
                itemDiv.style.background = '#fff3cd';
            }

            // 设置发货单号到输入框
            document.getElementById('testApvNo').value = currentItem.apvNo;

            log('开始测试批量项目 ' + (autoTestState.currentBatchIndex + 1) + '/' + autoTestState.batchItems.length + '：' + currentItem.apvNo);

            // 重置单项测试状态
            autoTestState.currentStep = 0;
            autoTestState.results = [];

            // 开始执行测试步骤
            executeNextStep();
        }

        function completeBatchItem() {
            var currentItem = autoTestState.batchItems[autoTestState.currentBatchIndex];
            currentItem.endTime = Date.now();
            currentItem.results = autoTestState.results.slice(); // 复制结果

            var successCount = currentItem.results.filter(function(r) { return r.status === 'success'; }).length;
            var totalSteps = currentItem.results.length;

            if (successCount === totalSteps) {
                currentItem.status = 'completed';
            } else {
                currentItem.status = 'failed';
            }

            // 更新UI
            var itemDiv = document.getElementById('batch-item-' + autoTestState.currentBatchIndex);
            if (itemDiv) {
                var duration = currentItem.endTime - currentItem.startTime;
                var statusIcon = currentItem.status === 'completed' ? '✅' : '❌';
                var statusText = currentItem.status === 'completed' ? '成功' : '失败';

                itemDiv.innerHTML = '<strong>' + currentItem.apvNo + '</strong> - ' + statusIcon + ' ' + statusText +
                    ' (' + successCount + '/' + totalSteps + ' 步骤成功, ' + duration + 'ms)';
                itemDiv.style.background = currentItem.status === 'completed' ? '#d4edda' : '#f8d7da';
            }

            log('批量项目完成：' + currentItem.apvNo + ' - ' + currentItem.status);

            // 更新批量进度
            updateBatchProgress();

            // 继续下一个项目
            autoTestState.currentBatchIndex++;
            setTimeout(executeNextBatchItem, autoTestConfig.stepDelay);
        }

        function completeBatchTest() {
            var endTime = Date.now();
            var totalDuration = endTime - autoTestState.startTime;

            autoTestState.isRunning = false;
            updateProgress(100);

            var completedCount = autoTestState.batchItems.filter(function(item) { return item.status === 'completed'; }).length;
            var failedCount = autoTestState.batchItems.filter(function(item) { return item.status === 'failed'; }).length;

            log('🎉 批量自动化测试完成！');
            log('总耗时：' + totalDuration + 'ms');
            log('成功：' + completedCount + '，失败：' + failedCount + '，总计：' + autoTestState.batchItems.length);

            // 生成批量测试报告
            var report = generateBatchTestReport();
            log('批量测试报告：' + JSON.stringify(report, null, 2));
        }

        function generateBatchTestReport() {
            var completedCount = autoTestState.batchItems.filter(function(item) { return item.status === 'completed'; }).length;
            var failedCount = autoTestState.batchItems.filter(function(item) { return item.status === 'failed'; }).length;
            var totalDuration = Date.now() - autoTestState.startTime;

            return {
                batchInfo: {
                    totalItems: autoTestState.batchItems.length,
                    completedCount: completedCount,
                    failedCount: failedCount,
                    successRate: ((completedCount / autoTestState.batchItems.length) * 100).toFixed(2) + '%',
                    startTime: new Date(autoTestState.startTime).toISOString(),
                    endTime: new Date().toISOString(),
                    totalDuration: totalDuration
                },
                items: autoTestState.batchItems.map(function(item) {
                    var successCount = item.results.filter(function(r) { return r.status === 'success'; }).length;
                    return {
                        apvNo: item.apvNo,
                        status: item.status,
                        duration: item.endTime - item.startTime,
                        successSteps: successCount,
                        totalSteps: item.results.length,
                        results: item.results
                    };
                }),
                config: autoTestConfig
            };
        }

        // 添加一些辅助函数到现有的测试函数中
        function testValidateGpsrData() {
            return executeValidationTest()
                .then(function(result) {
                    log('✅ 数据验证成功：' + result.validTags + '/' + result.totalTags + ' 个标签有效');
                    return result;
                })
                .catch(function(error) {
                    log('❌ 数据验证失败：' + error.message);
                    throw error;
                });
        }

        // 修改现有的测试函数以支持自动化测试
        var originalTestPreload = testPreload;
        testPreload = function() {
            if (autoTestState.isRunning) {
                return executePreloadTest();
            } else {
                return originalTestPreload();
            }
        };

        var originalTestPrint = testPrint;
        testPrint = function() {
            if (autoTestState.isRunning) {
                return executePrintTest();
            } else {
                return originalTestPrint();
            }
        };

        // 添加URL生成按钮到页面
        function addUrlGeneratorButton() {
            var buttonHtml = '<button class="button secondary" onclick="showAutoTestUrl()">生成自动化测试URL</button>';
            var basicSection = document.querySelector('.test-section');
            if (basicSection) {
                basicSection.innerHTML += buttonHtml;
            }
        }

        // 在页面加载时添加URL生成按钮
        $(document).ready(function() {
            addUrlGeneratorButton();
        });
    </script>
</body>
</html>
