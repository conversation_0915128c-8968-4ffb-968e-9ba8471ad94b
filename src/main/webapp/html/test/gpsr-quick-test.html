<!DOCTYPE html>
<html>
<head>
    <title>GPSR快速测试 - YSTN25061015000651</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .test-info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .test-buttons { text-align: center; margin: 20px 0; }
        .button { padding: 12px 24px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        .button:hover { background: #005a87; }
        .button.success { background: #28a745; }
        .button.danger { background: #dc3545; }
        .status { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .status.running { background: #fff3cd; border-left: 4px solid #ffc107; }
        .status.success { background: #d4edda; border-left: 4px solid #28a745; }
        .status.failed { background: #f8d7da; border-left: 4px solid #dc3545; }
        .log-area { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; margin: 20px 0; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #007cba, #28a745); width: 0%; transition: width 0.3s; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #dee2e6; border-radius: 5px; }
        .auto-url { background: #e9ecef; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; word-break: break-all; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 GPSR快速测试</h1>
            <p>专门测试发货单：<strong>YSTN25061015000651</strong></p>
        </div>

        <div class="test-info">
            <h3>📋 测试参数</h3>
            <ul>
                <li><strong>发货单号：</strong>YSTN25061015000651</li>
                <li><strong>SKU：</strong>4NB401005-8</li>
                <li><strong>接口类型：</strong>本地接口 (apv/packs/printGpsrTag)</li>
                <li><strong>基础URL：</strong>http://*************:8181/wms/</li>
            </ul>
        </div>

        <div class="test-buttons">
            <button class="button success" onclick="startQuickTest()">🚀 开始快速测试</button>
            <button class="button" onclick="testSingleStep()">🔍 单步测试</button>
            <button class="button danger" onclick="clearResults()">🗑️ 清理结果</button>
        </div>

        <div id="testStatus" class="status" style="display: none;">
            <h4 id="statusTitle">测试状态</h4>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p id="statusText">准备开始...</p>
        </div>

        <div class="test-result" id="testResults" style="display: none;">
            <h4>📊 测试结果</h4>
            <div id="resultContent"></div>
        </div>

        <div class="auto-url" id="autoUrl" style="display: none;">
            <strong>自动化测试URL：</strong><br>
            <span id="urlText"></span><br>
            <button class="button" onclick="copyUrl()">📋 复制URL</button>
        </div>

        <div class="log-area" id="logArea"></div>
    </div>

    <!-- 隐藏的GPSR容器 -->
    <div id="print_gpsr_tag" style="display: none;"></div>

    <!-- 引入依赖 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- 引入GPSR缓存管理器 -->
    <script src="../js/gpsr-cache-manager.js"></script>
    
    <script>
        // 测试配置
        var testConfig = {
            apvNo: 'YSTN25061015000651',
            sku: '4NB401005-8',
            apiType: 'local',
            baseUrl: 'http://*************:8181/wms/'
        };

        var testState = {
            isRunning: false,
            currentStep: 0,
            totalSteps: 5,
            results: [],
            startTime: null
        };

        // 测试步骤定义
        var testSteps = [
            { name: '网络连接测试', function: testNetwork },
            { name: '后端接口测试', function: testBackend },
            { name: 'GPSR预加载测试', function: testPreload },
            { name: '数据验证测试', function: testValidation },
            { name: '打印功能测试', function: testPrint }
        ];

        // 日志函数
        function log(message) {
            var logArea = document.getElementById('logArea');
            var timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += '[' + timestamp + '] ' + message + '\n';
            logArea.scrollTop = logArea.scrollHeight;
            console.log('[GPSR测试] ' + message);
        }

        // 更新状态
        function updateStatus(title, text, type) {
            var statusDiv = document.getElementById('testStatus');
            var statusTitle = document.getElementById('statusTitle');
            var statusText = document.getElementById('statusText');
            
            statusDiv.style.display = 'block';
            statusDiv.className = 'status ' + (type || 'running');
            statusTitle.textContent = title;
            statusText.textContent = text;
        }

        // 更新进度
        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        // 显示结果
        function showResult(result) {
            var resultsDiv = document.getElementById('testResults');
            var resultContent = document.getElementById('resultContent');
            
            resultsDiv.style.display = 'block';
            
            var html = '<table border="1" style="width: 100%; border-collapse: collapse;">';
            html += '<tr><th>步骤</th><th>状态</th><th>耗时</th><th>结果</th></tr>';
            
            testState.results.forEach(function(result) {
                var statusIcon = result.status === 'success' ? '✅' : '❌';
                var statusClass = result.status === 'success' ? 'success' : 'failed';
                
                html += '<tr class="' + statusClass + '">';
                html += '<td>' + result.step + '</td>';
                html += '<td>' + statusIcon + ' ' + result.status + '</td>';
                html += '<td>' + result.duration + 'ms</td>';
                html += '<td>' + (result.message || result.error || '') + '</td>';
                html += '</tr>';
            });
            
            html += '</table>';
            
            var successCount = testState.results.filter(function(r) { return r.status === 'success'; }).length;
            var totalDuration = Date.now() - testState.startTime;
            
            html += '<p><strong>总结：</strong>' + successCount + '/' + testState.results.length + ' 步骤成功，总耗时：' + totalDuration + 'ms</p>';
            
            resultContent.innerHTML = html;
        }

        // 生成自动化URL
        function generateAutoUrl() {
            var baseUrl = window.location.origin + window.location.pathname.replace('gpsr-quick-test.html', 'gpsr-cache-test.html');
            var params = new URLSearchParams();
            
            params.set('apvNo', testConfig.apvNo);
            params.set('sku', testConfig.sku);
            params.set('apiType', testConfig.apiType);
            params.set('autoTest', 'true');
            
            return baseUrl + '?' + params.toString();
        }

        // 显示自动化URL
        function showAutoUrl() {
            var autoUrlDiv = document.getElementById('autoUrl');
            var urlText = document.getElementById('urlText');
            
            var url = generateAutoUrl();
            urlText.textContent = url;
            autoUrlDiv.style.display = 'block';
        }

        // 复制URL
        function copyUrl() {
            var url = generateAutoUrl();
            navigator.clipboard.writeText(url).then(function() {
                log('自动化测试URL已复制到剪贴板');
            }).catch(function(err) {
                log('复制失败：' + err);
            });
        }

        // 开始快速测试
        function startQuickTest() {
            if (testState.isRunning) {
                log('测试正在进行中，请等待完成');
                return;
            }
            
            testState.isRunning = true;
            testState.currentStep = 0;
            testState.results = [];
            testState.startTime = Date.now();
            
            log('🚀 开始GPSR快速测试：' + testConfig.apvNo);
            updateStatus('测试进行中', '正在执行测试步骤...', 'running');
            
            executeNextStep();
        }

        // 执行下一步
        function executeNextStep() {
            if (!testState.isRunning || testState.currentStep >= testSteps.length) {
                completeTest();
                return;
            }
            
            var step = testSteps[testState.currentStep];
            var progress = (testState.currentStep / testSteps.length) * 100;
            
            updateProgress(progress);
            updateStatus('执行步骤 ' + (testState.currentStep + 1) + '/' + testSteps.length, step.name, 'running');
            log('执行步骤：' + step.name);
            
            var startTime = Date.now();
            
            step.function()
                .then(function(result) {
                    var duration = Date.now() - startTime;
                    testState.results.push({
                        step: step.name,
                        status: 'success',
                        duration: duration,
                        message: result.message || '成功',
                        data: result
                    });
                    
                    log('✅ ' + step.name + ' 成功，耗时：' + duration + 'ms');
                    testState.currentStep++;
                    setTimeout(executeNextStep, 1000);
                })
                .catch(function(error) {
                    var duration = Date.now() - startTime;
                    testState.results.push({
                        step: step.name,
                        status: 'failed',
                        duration: duration,
                        error: error.message || '失败'
                    });
                    
                    log('❌ ' + step.name + ' 失败：' + error.message);
                    testState.currentStep++;
                    setTimeout(executeNextStep, 1000);
                });
        }

        // 完成测试
        function completeTest() {
            testState.isRunning = false;
            updateProgress(100);
            
            var successCount = testState.results.filter(function(r) { return r.status === 'success'; }).length;
            var totalDuration = Date.now() - testState.startTime;
            
            if (successCount === testSteps.length) {
                updateStatus('测试完成', '所有步骤都成功完成！', 'success');
                log('🎉 测试完成！所有步骤都成功，总耗时：' + totalDuration + 'ms');
            } else {
                updateStatus('测试完成', '部分步骤失败，请查看详细结果', 'failed');
                log('⚠️ 测试完成，但有 ' + (testSteps.length - successCount) + ' 个步骤失败');
            }
            
            showResult();
            showAutoUrl();
        }

        // 具体的测试函数
        function testNetwork() {
            return new Promise(function(resolve, reject) {
                var testUrl = testConfig.baseUrl + 'apv/packs/printGpsrTag?apvNo=TEST';
                
                $.ajax({
                    url: testUrl,
                    type: 'HEAD',
                    timeout: 5000,
                    success: function() {
                        resolve({ message: '网络连接正常' });
                    },
                    error: function(xhr) {
                        reject(new Error('网络连接失败，状态码：' + xhr.status));
                    }
                });
            });
        }

        function testBackend() {
            return new Promise(function(resolve, reject) {
                var testUrl = testConfig.baseUrl + 'apv/packs/printGpsrTag?apvNo=' + testConfig.apvNo;
                
                $.ajax({
                    url: testUrl,
                    type: 'GET',
                    timeout: 10000,
                    success: function(response) {
                        var $response = $(response);
                        var gpsrTags = $response.find('.gpsr-tag-print');
                        
                        if (gpsrTags.length > 0) {
                            resolve({
                                message: '找到 ' + gpsrTags.length + ' 个GPSR标签',
                                tagCount: gpsrTags.length,
                                responseLength: response.length
                            });
                        } else {
                            reject(new Error('响应中未找到GPSR标签'));
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(new Error('接口调用失败：' + status));
                    }
                });
            });
        }

        function testPreload() {
            return GpsrCacheManager.preloadGpsrTag(testConfig.apvNo, { local: true })
                .then(function(result) {
                    return {
                        message: '预加载成功，平台：' + result.platform,
                        platform: result.platform,
                        pdfCount: result.pdfUrls.length
                    };
                });
        }

        function testValidation() {
            return new Promise(function(resolve, reject) {
                var cached = GpsrCacheManager.cache.get(testConfig.apvNo);
                if (!cached) {
                    reject(new Error('未找到缓存数据'));
                    return;
                }
                
                var $content = $(cached.content);
                var gpsrTags = $content.find('.gpsr-tag-print');
                
                if (gpsrTags.length === 0) {
                    reject(new Error('缓存中未找到GPSR标签'));
                    return;
                }
                
                var validCount = 0;
                gpsrTags.each(function() {
                    var $tag = $(this);
                    var sku = $tag.find("input[name='printSku']").val();
                    var qty = $tag.find("input[name='printQty']").val();
                    
                    if (sku && qty && !isNaN(parseInt(qty))) {
                        validCount++;
                    }
                });
                
                resolve({
                    message: validCount + '/' + gpsrTags.length + ' 个标签验证通过',
                    totalTags: gpsrTags.length,
                    validTags: validCount
                });
            });
        }

        function testPrint() {
            return new Promise(function(resolve) {
                // 在测试环境中使用模拟打印
                log('执行模拟打印测试...');
                setTimeout(function() {
                    resolve({
                        message: '打印测试成功（模拟）',
                        printCount: 1,
                        note: '测试环境使用模拟打印'
                    });
                }, 500);
            });
        }

        // 单步测试
        function testSingleStep() {
            log('单步测试功能开发中...');
        }

        // 清理结果
        function clearResults() {
            document.getElementById('logArea').innerHTML = '';
            document.getElementById('testStatus').style.display = 'none';
            document.getElementById('testResults').style.display = 'none';
            document.getElementById('autoUrl').style.display = 'none';
            log('结果已清理');
        }

        // 页面加载完成
        $(document).ready(function() {
            log('GPSR快速测试页面已加载');
            log('目标发货单：' + testConfig.apvNo);
            log('目标SKU：' + testConfig.sku);
            log('接口地址：' + testConfig.baseUrl);
        });
    </script>
</body>
</html>
