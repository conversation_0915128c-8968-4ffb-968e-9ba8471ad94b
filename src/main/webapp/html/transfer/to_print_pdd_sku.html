<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<#include "/common/include.html">
</head>
<body class="page-header-fixed page-full-width">
	<div id="order-log-area" style="max-height: 405px;overflow: auto">
		<table class="table table-condensed table-bordered  table-striped">
			<colgroup>
				<col width="35%"/>
				<col width="35%"/>
				<col width="30%"/>
			</colgroup>
			<thead>
				<tr>
					<th>SKU</th>
					<th>打印数量</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody id="printSku-body" style="text-align: center;">
				<#list domain.taskItemList as item>
					<tr>
						<td style="vertical-align: middle;">${item.sku}</td>
						<td style="vertical-align: middle;">
							<input class="form-control input-small" type="hidden" id="${item.sku}_sku" name="sku" value="${item.sku}">
							<input class="form-control input-small" type="hidden" id="${item.sku}_quantity" value="${item.gridQuantity}">
							<input class="form-control input-small print-quantity" type="number" max="${item.gridQuantity}" min="0" onblur="checkQuantity('${item.sku}',this)" name="quantity" id="${item.sku}_input_quantity" value="0">
						</td>
						<td style="vertical-align: middle;">
							<button class="btn btn-primary" onclick="doPrint('${item.packageSn}','${item.sku}')">打印</button>
						</td>
					</tr>
				</#list>
			</tbody>
		</table>
	</div>
	<div style="display: none" name="printHtml" id="printHtml"></div>
</body>
<!-- END BODY -->
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/print.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
<script>
	var jitPrinter75Tag;
	$(document).ready(function(){
		var storage = new WebStorageCache();
		if (storage.get("jit_printer75Tag")) {
			jitPrinter75Tag = storage.get("jit_printer75Tag");
		}
	});
    function checkQuantity(sku, obj) {
        var quantity = $("#" + sku + "_quantity").val();
        var num = $(obj).val();
        if(num != ""){
            num = parseInt(num);
			if (num > quantity){
				layer.alert("打印数量不能大于" + quantity, "error");
                $(obj).val("");
			}else if (num < 0){
                layer.alert("打印数量不能小于0", "error");
                $(obj).val("");
			}
		}

    }
	pageLength = "70mm";//纸张长
	pageWidth = "60mm";//纸张宽
	function doPrint(orderNo,sku){
		var quantity = $("#" + sku + "_input_quantity").val();
		if (!quantity || quantity == 0){
			layer.alert("请输入打印数量", {closeBtn: 0}, function (index) {
				layer.close(index);
			});
			return;
		}
		var printPageUrl = CONTEXT_PATH + "skuLabel/printSkuLabel?orderNo=" + orderNo + "&sku=" + sku;
		$.get(printPageUrl, function (data) {
			$("#printHtml").html(data);
			var errorMsg = $("#printHtml").find("#error-msg").html();
			if (errorMsg) {
				layer.alert(errorMsg, {closeBtn: 0}, function (index) {
					layer.close(index);
				});
				return;
			}
			var printHtml = $("#printHtml").find("#print-item-0").html();
			if (printHtml) {
				printTemuLabelCode(printHtml, quantity);
			} else {
				var asnPrintUrl = document.querySelector("#printUrlFrame").src;
				printTemuLabel(asnPrintUrl, jitPrinter75Tag, quantity);
			}

			var skuTag = $("#printHtml").find("#skuTag").val();
			if (skuTag && skuTag.indexOf("宠物") != -1) {
				printPetTag(null, quantity);

			}
			var printEtHtml = $("#printHtml").find("#dz-print").html();
			if (printEtHtml){
				etPrint(printEtHtml, quantity);
			}
			var gpsrTag = $("#printHtml").find("#temu-gpsr").val();
			if (gpsrTag) {
				printPdfCopies(window.location.origin + gpsrTag, jitPrinter75Tag, pageLength, pageWidth, quantity);
			}
		});
	}

	function printTemuLabel(url, printerTag, copies) {
		pageLength = "70mm";//纸张长
		pageWidth = "60mm";//纸张宽
		var LODOP = getLodop();
		LODOP.SET_PRINT_PAGESIZE(0, pageLength, pageWidth, 'Note'); // 设置纸张大小
		LODOP.ADD_PRINT_PDF(0,0,'100%','100%',url);
		var imgUrl = window.location.origin + CONTEXT_PATH + "file/pdf/jit/labelCode/huanbao.png";
		LODOP.ADD_PRINT_IMAGE(70,0,253,80,"<img border='0' src='"+imgUrl+"' width=2830 height=800 />");
		LODOP.SET_PRINT_STYLEA(0,"Stretch",2);
		LODOP.SET_PRINT_STYLEA(0,"TransColor","#FFFFFF");
		LODOP.ADD_PRINT_TEXT(150,7,200,20,"WARNING");
		LODOP.SET_PRINT_STYLEA(0,"Bold",1);
		LODOP.ADD_PRINT_TEXT(165, 12, 304, 58, "1. To avoid danger of suffocation, keep this plastic bag away from babies and children. Do not use this bag in cribs. beds, carriages or play pens.2. This bag is not a toy.");
		LODOP.SET_PRINT_STYLEA(0,"FontSize",7);
		LODOP.SET_PRINT_STYLEA(0,"Bold",1);
		if (LODOP.SET_PRINTER_INDEX(printerTag)) {
			if (copies === undefined) {
				copies = 1;
			}
			LODOP.SET_PRINT_COPIES(copies); // 打印份数
			//LODOP.PRINT_DESIGN(); // 打印设计
			//LODOP.PREVIEW(); // 打印设计
			LODOP.PRINT(); // 静默打印
		}
	}

</script>
</html>