<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<property name="log_path" value="/var/log/wms" />

	<property name="CONSOLE_LOG_PATTERN"
			  value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([${springAppName:-},%X{X-B3-TraceId:-},%X{X-B3-SpanId:-},%X{X-Span-Export:-}]){yellow} %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

	<!-- File Appender for /var/log/wms with INFO level -->
	<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>${log_path}/%d{yyyy-MM-dd}.%i.log.zip
			</FileNamePattern>
			<MaxHistory>30</MaxHistory>
			<!-- 日志总保存量为10GB -->
			<totalSizeCap>10GB</totalSizeCap>
			<timeBasedFileNamingAndTriggeringPolicy
					class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<!--文件达到 最大1G时会被压缩和切割 -->
				<maxFileSize>1GB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d [%t] %p [%c %L] - &lt;%m&gt;%n</pattern>
		</encoder>
		<!-- ThresholdFilter to include INFO and above -->
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>INFO</level>
		</filter>
	</appender>



	<!-- Console Appender with WARN level -->
	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
		</encoder>
		<!--<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>INFO</level>
		</filter>-->
	</appender>
	<logger name="com.estone.common.util.SqlerTemplate" level="INFO" >
		<appender-ref ref="CONSOLE"/>
		<appender-ref ref="FILE" />
	</logger>

	<!-- Root logger configuration -->
	<root level="INFO">
		<appender-ref ref="FILE" />
		<appender-ref ref="CONSOLE" />
	</root>

</configuration>