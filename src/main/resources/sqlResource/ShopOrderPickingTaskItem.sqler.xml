<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryShopOrderPickingTaskItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM shop_order_picking_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND spo_id = :spo_id]>
        <[AND created_date = :created_date]>
        <[AND create_by = :create_by]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryShopOrderPickingTaskItemList" >
    <content >
      <![CDATA[
        SELECT id, task_id, spo_id, created_date, create_by, status
        FROM shop_order_picking_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND spo_id = :spo_id]>
        <[AND created_date = :created_date]>
        <[AND create_by = :create_by]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryShopOrderPickingTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_id, spo_id, created_date, create_by, status
        FROM shop_order_picking_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryShopOrderPickingTaskItem" >
    <content >
      <![CDATA[
        SELECT id, task_id, spo_id, created_date, create_by, status
        FROM shop_order_picking_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND spo_id = :spo_id]>
        <[AND created_date = :created_date]>
        <[AND create_by = :create_by]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createShopOrderPickingTaskItem" >
    <content >
      <![CDATA[
        INSERT INTO shop_order_picking_task_item (task_id, spo_id, created_date, create_by, status)
        VALUES (:task_id, :spo_id, :created_date, :create_by, :status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteShopOrderPickingTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM shop_order_picking_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateShopOrderPickingTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE shop_order_picking_task_item
        SET <[task_id = :task_id,]>
          <[spo_id = :spo_id,]>
          <[created_date = :created_date,]>
          <[create_by = :create_by,]>
          <[status = :status,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>