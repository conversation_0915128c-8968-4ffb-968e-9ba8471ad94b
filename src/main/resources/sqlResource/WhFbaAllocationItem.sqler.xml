<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap>
    <sql datasource="dataSource" id="queryWhFbaAllocationItemCount">
        <content>
            <![CDATA[
        SELECT COUNT(1)
        FROM wh_fba_allocation_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND fba_id = :fba_id]>
        <[AND box_no = :box_no]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND product_sku = :product_sku]>
        <[AND product_barcode = :product_barcode]>
        <[AND store = :store]>
        <[AND site = :site]>
        <[AND fn_sku = :fn_sku]>
        <[AND sell_sku = :sell_sku]>
        <[AND quantity = :quantity]>
        <[AND loading_quantity = :loading_quantity]>
        <[AND putaway_quantity = :putaway_quantity]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND putaway_diff = :putaway_diff]>
        <[AND product_length = :product_length]>
        <[AND product_width = :product_width]>
        <[AND product_height = :product_height]>
        <[AND product_weight = :product_weight]>
        <[AND toll_weight = :toll_weight]>
        <[AND freight = :freight]>
        <[AND other_price = :other_price]>
        <[AND product_price = :product_price]>
        <[AND sell_sku_name = :sell_sku_name]>
        <[AND grid_status = :grid_status]>
        <[AND grid_by = :grid_by]>
        <[AND tag_by = :tag_by]>
        <[AND tag_time = :tag_time]>
        <[AND process_type = :process_type]>
        <[AND re_process = :re_process]>
      ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="queryWhFbaAllocationItemList">
        <content>
            <![CDATA[
        SELECT id, fba_id, box_no, warehouse_id, product_sku, product_barcode, store, site, sku_tags, grid_quantity, sku_barcode,
        fn_sku, sell_sku, quantity, loading_quantity, putaway_quantity, putaway_diff, product_length, grid_time,grid_by,grid_status,
        product_width, product_height, product_weight, toll_weight, freight, other_price, allot_quantity, pick_quantity,tag_time,tag_by,
        product_price, sell_sku_name, box_cost, sku_cost, sku_quantity, load_num, suit_flag, sku_suit_num, sku_img, process_type, re_process,sc_item_id
        ,temu_code_url
        FROM wh_fba_allocation_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND fba_id = :fba_id]>
        <[AND box_no = :box_no]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND product_sku = :product_sku]>
        <[AND product_barcode = :product_barcode]>
        <[AND store = :store]>
        <[AND site = :site]>
        <[AND fn_sku = :fn_sku]>
        <[AND sell_sku = :sell_sku]>
        <[AND quantity = :quantity]>
        <[AND loading_quantity = :loading_quantity]>
        <[AND putaway_quantity = :putaway_quantity]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND putaway_diff = :putaway_diff]>
        <[AND product_length = :product_length]>
        <[AND product_width = :product_width]>
        <[AND product_height = :product_height]>
        <[AND product_weight = :product_weight]>
        <[AND toll_weight = :toll_weight]>
        <[AND freight = :freight]>
        <[AND other_price = :other_price]>
        <[AND product_price = :product_price]>
        <[AND sell_sku_name = :sell_sku_name]>
        <[AND grid_status = :grid_status]>
        <[AND grid_by = :grid_by]>
        <[AND tag_by = :tag_by]>
        <[AND tag_time = :tag_time]>
        <[AND process_type = :process_type]>
        <[AND re_process = :re_process]>
      ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="queryWhFbaAllocationItemByPrimaryKey">
        <content>
            <![CDATA[
        SELECT id, fba_id, box_no, warehouse_id, product_sku, product_barcode, store, site, sku_tags, grid_quantity, sku_barcode,
        fn_sku, sell_sku, quantity, loading_quantity, putaway_quantity, putaway_diff, product_length, grid_time,grid_by,grid_status,
        product_width, product_height, product_weight, toll_weight, freight, other_price, allot_quantity, pick_quantity,tag_time,tag_by,
        product_price, sell_sku_name, box_cost, sku_cost, sku_quantity, load_num, suit_flag, sku_suit_num, sku_img, process_type, re_process,sc_item_id
        ,temu_code_url
        FROM wh_fba_allocation_item
        WHERE 1 = 1
        AND id = :id
      ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="queryWhFbaAllocationItem">
        <content>
            <![CDATA[
        SELECT id, fba_id, box_no, warehouse_id, product_sku, product_barcode, store, site, sku_tags, grid_quantity, sku_barcode,
        fn_sku, sell_sku, quantity, loading_quantity, putaway_quantity, putaway_diff, product_length, grid_time,grid_by,grid_status,
        product_width, product_height, product_weight, toll_weight, freight, other_price, allot_quantity, pick_quantity,tag_time,tag_by,
        product_price, sell_sku_name, box_cost, sku_cost, sku_quantity, load_num, suit_flag, sku_suit_num, sku_img, process_type, re_process,sc_item_id
        ,temu_code_url
        FROM wh_fba_allocation_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND fba_id = :fba_id]>
        <[AND box_no = :box_no]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND product_sku = :product_sku]>
        <[AND product_barcode = :product_barcode]>
        <[AND store = :store]>
        <[AND site = :site]>
        <[AND fn_sku = :fn_sku]>
        <[AND sell_sku = :sell_sku]>
        <[AND quantity = :quantity]>
        <[AND loading_quantity = :loading_quantity]>
        <[AND putaway_quantity = :putaway_quantity]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND putaway_diff = :putaway_diff]>
        <[AND product_length = :product_length]>
        <[AND product_width = :product_width]>
        <[AND product_height = :product_height]>
        <[AND product_weight = :product_weight]>
        <[AND toll_weight = :toll_weight]>
        <[AND freight = :freight]>
        <[AND other_price = :other_price]>
        <[AND product_price = :product_price]>
        <[AND sell_sku_name = :sell_sku_name]>
        <[AND grid_status = :grid_status]>
        <[AND grid_by = :grid_by]>
        <[AND tag_by = :tag_by]>
        <[AND tag_time = :tag_time]>
        <[AND process_type = :process_type]>
        <[AND re_process = :re_process]>
      ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="createWhFbaAllocationItem">
        <content>
            <![CDATA[
        INSERT INTO wh_fba_allocation_item (fba_id, box_no, warehouse_id, product_sku, product_barcode, store, grid_quantity,grid_time,grid_by,grid_status,
          site, fn_sku, sell_sku, sku_tags, quantity, loading_quantity, putaway_quantity, putaway_diff, allot_quantity, pick_quantity,tag_time,tag_by,
          product_length, product_width, product_height, product_weight, toll_weight, box_cost, sku_cost, sku_img, process_type, re_process,
          freight, other_price, product_price, sell_sku_name, sku_quantity, load_num, suit_flag, sku_suit_num,fn_sku_tags,sc_item_id
          ,remark,tag,temu_code_url,temu_tag_url,sku_barcode)
        VALUES (:fba_id, :box_no, :warehouse_id, :product_sku, :product_barcode, :store, :grid_quantity,:grid_time,:grid_by,:grid_status,
          :site, :fn_sku, :sell_sku, :sku_tags, :quantity, :loading_quantity, :putaway_quantity, :putaway_diff, :allot_quantity, :pick_quantity,:tag_time,:tag_by,
          :product_length, :product_width, :product_height, :product_weight, :toll_weight, :box_cost, :sku_cost, :sku_img, :process_type, :re_process,
          :freight, :other_price, :product_price, :sell_sku_name, :sku_quantity, :load_num, :suit_flag, :sku_suit_num, :fn_sku_tags,:sc_item_id
          ,:remark,:tag,:temu_code_url,:temu_tag_url,:sku_barcode)
      ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="deleteWhFbaAllocationItemByPrimaryKey">
        <content>
            <![CDATA[
        DELETE FROM wh_fba_allocation_item
        WHERE 1 = 1
        AND id = :id
      ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="updateWhFbaAllocationItemByPrimaryKey">
        <content>
            <![CDATA[
        UPDATE wh_fba_allocation_item
        SET <[fba_id = :fba_id,]>
          <[box_no = :box_no,]>
          <[warehouse_id = :warehouse_id,]>
          <[product_sku = :product_sku,]>
          <[product_barcode = :product_barcode,]>
          <[store = :store,]>
          <[site = :site,]>
          <[fn_sku = :fn_sku,]>
          <[sell_sku = :sell_sku,]>
          <[quantity = :quantity,]>
          <[loading_quantity = :loading_quantity,]>
          <[putaway_quantity = :putaway_quantity,]>
          <[allot_quantity = :allot_quantity,]>
          <[pick_quantity = :pick_quantity,]>
          <[putaway_diff = :putaway_diff,]>
          <[product_length = :product_length,]>
          <[product_width = :product_width,]>
          <[product_height = :product_height,]>
          <[product_weight = :product_weight,]>
          <[toll_weight = :toll_weight,]>
          <[freight = :freight,]>
          <[box_cost = :box_cost,]>
          <[sku_cost = :sku_cost,]>
          <[sku_tags = :sku_tags,]>
          <[other_price = :other_price,]>
          <[product_price = :product_price,]>
          <[sell_sku_name = :sell_sku_name,]>
          <[sku_quantity = :sku_quantity,]>
          <[load_num = :load_num,]>
          <[suit_flag = :suit_flag,]>
          <[sku_suit_num = :sku_suit_num,]>
          <[sku_img = :sku_img,]>
          <[fn_sku_tags = :fn_sku_tags,]>
          <[grid_quantity = :grid_quantity,]>
          <[grid_status = :grid_status,]>
          <[grid_by = :grid_by,]>
          <[grid_time = :grid_time,]>
          <[tag_by = :tag_by,]>
          <[tag_time = :tag_time,]>
          <[process_type = :process_type,]>
          <[re_process = :re_process,]>
          <[remark = :remark,]>
          <[temu_code_url = :temu_code_url,]>
          <[temu_tag_url = :temu_tag_url,]>
          <[sku_barcode = :sku_barcode,]>
          <[sc_item_id = :sc_item_id,]>
          <[load_by = :load_by,]>
          <[load_time = :load_time,]>
          <[company_name = :company_name,]>
          <[tag = :tag,]>
          <[temu_tag_url = :temu_tag_url,]>
          <[:RESET_SHIPPING_COST)]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="selectFbaAccount">
        <content>
            <![CDATA[
            select DISTINCT account_number from wh_fba_allocation
            ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="removeBoxInfoByPrimaryKey">
        <content>
            <![CDATA[
            UPDATE wh_fba_allocation_item
            SET product_length = null,
                product_width = null,
                product_height = null,
                product_weight = null,
                loading_quantity = null,
                load_num = null
            WHERE id = :id
          ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="clearTagMsg">
        <content>
            <![CDATA[
                UPDATE wh_fba_allocation_item SET tag_by = null, tag_time = null, sku_img = null, process_type = null, re_process = null
                WHERE fba_id = :fba_id and fn_sku = :fn_sku
            ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="updateItemByFbaIdAndFnSku">
        <content>
            <![CDATA[
                UPDATE wh_fba_allocation_item SET
                <[fn_sku = :fn_sku,]>
                <[process_type = :process_type,]>
                <[re_process = :re_process,]>
                fba_id = :fba_id
                WHERE fba_id = :fba_id and fn_sku = :fn_sku
            ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="updateItemByFbaIdAndSku">
        <content>
            <![CDATA[
                UPDATE wh_fba_allocation_item SET
                <[product_sku = :product_sku,]>
                <[grid_quantity = :grid_quantity,]>
                <[grid_status = :grid_status,]>
                <[grid_by = :grid_by,]>
                <[grid_time = :grid_time,]>
                fba_id = :fba_id
                WHERE fba_id = :fba_id and product_sku = :product_sku
            ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="queryFreightPrice">
        <content>
            <![CDATA[
                SELECT  product_sku,store,fn_sku,freight
                FROM wh_fba_allocation_item i
                LEFT JOIN wh_fba_allocation f on i.fba_id=f.id
                WHERE 1 = 1
                <[AND i.product_sku = :product_sku]>
                <[AND i.store = :store]>
                <[AND i.fn_sku = :fn_sku]>
                ORDER BY f.deliver_time DESC LIMIT 1
            ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="undoLbx" >
        <content >
            <![CDATA[
                UPDATE wh_fba_allocation_item
                SET
                    id=id,
                    <[:NULL_FIELD]>
                WHERE 1 = 1
                AND id in (:itemIdList)
  	        ]]>
        </content>
    </sql>

</sqlmap>