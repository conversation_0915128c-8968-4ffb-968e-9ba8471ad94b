<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhFbaAllocationCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_fba_allocation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND fba_no = :fba_no]>
        <[AND shipment_id = :shipment_id]>
        <[AND shipment_id IN (:shipmentIdList)]>
        <[AND apv_type = :apv_type]>
        <[AND purpose_house = :purpose_house]>
        <[AND account_number = :account_number]>
        <[AND status = :status]>
        <[AND status IN (:status_list)]>
        <[AND sm_code = :sm_code]>
        <[AND shipping_method = :shipping_method]>
        <[AND tracking_number = :tracking_number]>
        <[AND tracking_number IN (:trackingNumberList)]>
        <[AND push_time = :push_time]>
        <[AND box_push_by = :box_push_by]>
        <[AND box_push_time = :box_push_time]>
        <[AND confirm_time = :confirm_time]>
        <[AND deliver_by = :deliver_by]>
        <[AND deliver_time = :deliver_time]>
        <[AND cancel_time = :cancel_time]>
        <[AND departure_time = :departure_time]>
        <[AND id IN (SELECT task_item.apv_id from wh_picking_task_item task_item  LEFT JOIN wh_picking_task task ON task.id = task_item.task_id where task.task_no = :task_no)]>
        <[AND bat_no = :bat_no]>
        <[AND pick_out = :pick_out]>
        <[AND purpose_house IN (:purposeHouseList)]>
        <[AND purpose_house IN (:purposeHouseForPage)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhFbaAllocationList" >
    <content >
      <![CDATA[
        SELECT id, fba_no, bat_no, shipment_id, purpose_house, status, sm_code, shipping_method, shipping_method_by_tms, account_number,site, reject_reason,
        tracking_number,tracking_number_by_tms, push_time, box_push_by, box_push_time, confirm_time, deliver_by, shipping_order_no, shipping_company,
        deliver_time, cancel_time, departure_time, task_no, pdf_url,transit_type, tags, salesperson, overseas_up_time,remark, check_by, check_time,pick_out,logistics_aging,
        apv_type, receive_time, merge_time,split_region_flag, is_return, is_asn, exception_order,load_id
        FROM wh_fba_allocation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:idList)]>
        <[AND fba_no = :fba_no]>
        <[AND fba_no IN (:fbaNoList)]>
        <[AND shipment_id = :shipment_id]>
        <[AND shipment_id IN (:shipmentIdList)]>
        <[AND apv_type = :apv_type]>
        <[AND purpose_house = :purpose_house]>
        <[AND account_number = :account_number]>
        <[AND status = :status]>
        <[AND status IN (:status_list)]>
        <[AND sm_code = :sm_code]>
        <[AND shipping_method = :shipping_method]>
        <[AND tracking_number = :tracking_number]>
        <[AND tracking_number IN (:trackingNumberList)]>
        <[AND push_time = :push_time]>
        <[AND box_push_by = :box_push_by]>
        <[AND box_push_time = :box_push_time]>
        <[AND confirm_time = :confirm_time]>
        <[AND deliver_by = :deliver_by]>
        <[AND deliver_time = :deliver_time]>
        <[AND deliver_time >= :upStartDeliverTime]>
        <[AND deliver_time <= :upEndDeliverTime]>
        <[AND cancel_time = :cancel_time]>
        <[AND departure_time = :departure_time]>
        <[AND id IN (SELECT task_item.apv_id from wh_picking_task_item task_item  LEFT JOIN wh_picking_task task ON task.id = task_item.task_id where task.task_no = :task_no)]>
        <[AND bat_no = :bat_no]>
        <[AND purpose_house IN (:purposeHouseList)]>
        <[AND purpose_house IN (:purposeHouseForPage)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhFbaAllocationByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, fba_no, bat_no, shipment_id, purpose_house, status, sm_code, shipping_method, shipping_method_by_tms, account_number,site,pick_out,
        tracking_number, tracking_number_by_tms, push_time, box_push_by, box_push_time, confirm_time, deliver_by, shipping_order_no, shipping_company,
        deliver_time, cancel_time, departure_time, task_no, pdf_url,transit_type, tags, salesperson, overseas_up_time,remark, reject_reason, check_by,
        check_time,logistics_aging, apv_type, receive_time, merge_time,split_region_flag , is_return, is_asn, exception_order,load_id
        FROM wh_fba_allocation
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhFbaAllocation" >
    <content >
      <![CDATA[
        SELECT id, fba_no, bat_no, shipment_id, purpose_house, status, sm_code, shipping_method, shipping_method_by_tms, account_number,site,pick_out,
        tracking_number, tracking_number_by_tms, push_time, box_push_by, box_push_time, confirm_time, deliver_by, shipping_order_no, shipping_company,
        deliver_time, cancel_time, departure_time, task_no, pdf_url,transit_type, tags, salesperson, overseas_up_time,remark, reject_reason, check_by,
        check_time,logistics_aging, apv_type, receive_time,merge_time,split_region_flag, is_return, is_asn, exception_order,load_id
        FROM wh_fba_allocation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND fba_no = :fba_no]>
        <[AND shipment_id = :shipment_id]>
        <[AND apv_type = :apv_type]>
        <[AND purpose_house = :purpose_house]>
        <[AND account_number = :account_number]>
        <[AND status = :status]>
        <[AND status IN (:status_list)]>
        <[AND sm_code = :sm_code]>
        <[AND shipping_method = :shipping_method]>
        <[AND tracking_number = :tracking_number]>
        <[AND push_time = :push_time]>
        <[AND box_push_by = :box_push_by]>
        <[AND box_push_time = :box_push_time]>
        <[AND confirm_time = :confirm_time]>
        <[AND deliver_by = :deliver_by]>
        <[AND deliver_time = :deliver_time]>
        <[AND cancel_time = :cancel_time]>
        <[AND departure_time = :departure_time]>
        <[AND id IN (SELECT task_item.apv_id from wh_picking_task_item task_item  LEFT JOIN wh_picking_task task ON task.id = task_item.task_id where task.task_no = :task_no)]>
        <[AND bat_no = :bat_no]>
        <[AND purpose_house IN (:purposeHouseList)]>
        <[AND purpose_house IN (:purposeHouseForPage)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhFbaAllocation" >
    <content >
      <![CDATA[
        INSERT INTO wh_fba_allocation (fba_no, shipment_id, purpose_house, status, sm_code, shipping_method, check_by, check_time,pick_out,
          tracking_number, push_time, box_push_by, box_push_time, confirm_time, deliver_by, account_number, site, overseas_up_time,
          deliver_time, cancel_time, departure_time, task_no, pdf_url, bat_no, shipping_order_no, shipping_company, tags,salesperson,remark,
          reject_reason, logistics_aging, apv_type, receive_time,merge_time,split_region_flag,is_asn)
        VALUES (:fba_no, :shipment_id, :purpose_house, :status, :sm_code, :shipping_method, :check_by, :check_time,:pick_out,
          :tracking_number, :push_time, :box_push_by, :box_push_time, :confirm_time, :deliver_by, :account_number, :site, :overseas_up_time,
          :deliver_time, :cancel_time, :departure_time, :task_no, :pdf_url, :bat_no, :shipping_order_no, :shipping_company, :tags, :salesperson, :remark,
          :reject_reason, :logistics_aging, :apv_type, :receive_time,:merge_time,:split_region_flag,:is_asn)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhFbaAllocationByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_fba_allocation
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhFbaAllocationByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_fba_allocation
        SET <[fba_no = :fba_no,]>
          <[shipment_id = :shipment_id,]>
          <[purpose_house = :purpose_house,]>
          <[account_number = :account_number,]>
          <[status = :status,]>
          <[sm_code = :sm_code,]>
          <[shipping_method = :shipping_method,]>
          <[shipping_method_by_tms = :shipping_method_by_tms,]>
          <[tracking_number = :tracking_number,]>
          <[tracking_number_by_tms = :tracking_number_by_tms,]>
          <[push_time = :push_time,]>
          <[box_push_by = :box_push_by,]>
          <[box_push_time = :box_push_time,]>
          <[confirm_time = :confirm_time,]>
          <[deliver_by = :deliver_by,]>
          <[deliver_time = :deliver_time,]>
          <[cancel_time = :cancel_time,]>
          <[departure_time = :departure_time,]>
          <[task_no = :task_no,]>
          <[pdf_url = :pdf_url,]>
          <[bat_no = :bat_no,]>
          <[transit_type = :transit_type,]>
          <[shipping_order_no = :shipping_order_no,]>
          <[shipping_company = :shipping_company,]>
          <[tags = :tags,]>
          <[salesperson = :salesperson,]>
          <[remark = :remark,]>
          <[overseas_up_time = :overseas_up_time,]>
          <[reject_reason = :reject_reason,]>
          <[check_by = :check_by,]>
          <[check_time = :check_time,]>
          <[pick_out = :pick_out,]>
          <[logistics_aging = :logistics_aging,]>
          <[merge_time = :merge_time,]>
          <[is_return = :is_return,]>
          <[exception_order = :exception_order,]>
          <[load_id = :load_id,]>
          <[:RESET_SHIPPING_INFO)]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateWhFbaAllocationByIdAndStatus" >
    <content >
      <![CDATA[
        UPDATE wh_fba_allocation
        SET <[fba_no = :fba_no,]>
          <[shipment_id = :shipment_id,]>
          <[purpose_house = :purpose_house,]>
          <[account_number = :account_number,]>
          <[status = :status,]>
          <[sm_code = :sm_code,]>
          <[shipping_method = :shipping_method,]>
          <[tracking_number = :tracking_number,]>
          <[push_time = :push_time,]>
          <[box_push_by = :box_push_by,]>
          <[box_push_time = :box_push_time,]>
          <[confirm_time = :confirm_time,]>
          <[deliver_by = :deliver_by,]>
          <[deliver_time = :deliver_time,]>
          <[cancel_time = :cancel_time,]>
          <[departure_time = :departure_time,]>
          <[task_no = :task_no,]>
          <[pdf_url = :pdf_url,]>
          <[bat_no = :bat_no,]>
          <[shipping_order_no = :shipping_order_no,]>
          <[shipping_company = :shipping_company,]>
          <[salesperson = :salesperson,]>
          <[overseas_up_time = :overseas_up_time,]>
          <[reject_reason = :reject_reason,]>
          <[check_by = :check_by,]>
          <[check_time = :check_time,]>
          <[pick_out = :pick_out,]>
          <[logistics_aging = :logistics_aging,]>
          <[merge_time = :merge_time,]>
           <[split_region_flag = :split_region_flag,]>
           <[load_id = :load_id,]>
        id = id
        WHERE 1 = 1
        AND id = :id
        AND status = :cur_status
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhFbaAllocationAndItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(fa.id)
        FROM wh_fba_allocation fa
        WHERE 1 = 1
        <[AND fa.id = :id]>
        <[AND fa.fba_no = :fba_no]>
        <[AND fa.shipment_id = :shipment_id]>
        <[AND fa.apv_type = :apv_type]>
        <[AND fa.purpose_house = :purpose_house]>
        <[AND fa.account_number = :account_number]>
        <[AND fa.site = :site]>
        <[AND fa.status = :status]>
        <[AND fa.status IN (:status_list)]>
        <[AND fa.sm_code = :sm_code]>
        <[AND fa.shipping_method = :shipping_method]>
        <[AND fa.tracking_number = :tracking_number]>
        <[AND fa.tracking_number like :right_like_tracking_number]>
        <[AND fa.push_time = :push_time]>
        <[AND fa.box_push_by = :box_push_by]>
        <[AND fa.box_push_time = :box_push_time]>
        <[AND fa.confirm_time = :confirm_time]>
        <[AND fa.deliver_by = :deliver_by]>
        <[AND fa.deliver_time = :deliver_time]>
        <[AND fa.cancel_time = :cancel_time]>
        <[AND fa.departure_time = :departure_time]>
        <[AND fa.task_no = :task_no]>
        <[AND fa.bat_no = :bat_no]>
        <[AND fa.pick_out = :pick_out]>
        <[AND fa.split_region_flag = :split_region_flag]>
        <[AND fa.is_asn = :is_asn]>
        <[AND fa.push_time >= :from_push_time]>
        <[AND fa.push_time <= :to_push_time]>
        <[AND fa.receive_time >= :from_receive_time]>
        <[AND fa.receive_time <= :to_receive_time]>
        <[AND fa.cancel_time >= :from_cancel_time]>
        <[AND fa.cancel_time <= :to_cancel_time]>
        <[AND fa.box_push_time >= :from_box_push_time]>
        <[AND fa.box_push_time <= :to_box_push_time]>
        <[AND fa.overseas_up_time >= :from_up_time]>
        <[AND fa.overseas_up_time <= :to_up_time]>
        <[AND fa.deliver_time >= :upStartDeliverTime]>
        <[AND fa.deliver_time <= :upEndDeliverTime]>
        <[AND fa.merge_time >= :from_merge_time]>
        <[AND fa.merge_time <= :to_merge_time]>
        <[AND fa.confirm_time >= :fromConfirmTime]>
        <[AND fa.confirm_time <= :toConfirmTime]>
        <[AND fa.is_return = :is_return]>
        <[AND fa.exception_order = :exception_order]>
        <[:notReturn]>
        <[:NOT_TRANSIT_TYPE]>
        <[AND fa.transit_type = :transit_type]>
        <[:NOT_EXCEPTION_ORDER]>
        <[:NOT_NULL_TRACKING_NUMBER]>
        <[:TAGS_SQL]>
        <[AND fa.fba_no IN (:fbaNoList)]>
        <[AND fa.status IN (:statusList)]>
        <[AND fa.id IN (:idList)]>
        <[AND fa.apv_type IN (:apvTypeList)]>
        <[AND fa.purpose_house IN (:purposeHouseList)]>
        <[AND fa.purpose_house IN (:purposeHouseForPage)]>
        <[AND fa.purpose_house NOT IN (:purposeHouseNotInList)]>
        <[AND fa.tracking_number IN (:trackingNumberList)]>
        <[AND fa.shipping_order_no IN (:shippingOrderNoList)]>
        <[AND fa.shipping_order_no = :shipping_order_no]>
        <[AND fa.account_number IN (:accountNumberList)]>
        <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE tag IN (:tags))]>
        <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE temu_tag_url IN (:lbxNoList))]>
        <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE product_sku IN (:skuList))]>
        <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE product_sku = :sku)]>
        <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE fn_sku IN (:fnSkuList))]>
        <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE fn_sku = :fnSku)]>
        <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE re_process = :re_process)]>
        <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE process_type = :process_type)]>
         <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE temu_tag_url = :lbxNo)]>
        <[AND fa.fba_no IN (SELECT wscta.apv_no FROM wh_scan_shipment_to_apv wscta
                            INNER JOIN wh_scan_shipment wsc ON wscta.scan_shipment_id = wsc.id
                            WHERE is_transfer_house = true AND load_date >= :loadStartTime)]>
        <[AND fa.fba_no IN (SELECT wscta.apv_no FROM wh_scan_shipment_to_apv wscta
                            INNER JOIN wh_scan_shipment wsc ON wscta.scan_shipment_id = wsc.id
                            WHERE is_transfer_house = true AND load_date <= :loadEndTime)]>
        <[AND fa.fba_no IN (SELECT waosc.relevant_no FROM wh_apv_out_stock_chain waosc
                                INNER JOIN wh_transfer_stock stock ON waosc.stock_id = stock.id
                                WHERE stock.location_number IN (:allotLocationNumberList))]>
        <[AND fa.fba_no IN (SELECT waosc.relevant_no FROM wh_apv_out_stock_chain waosc
                                INNER JOIN wh_transfer_stock stock ON waosc.stock_id = stock.id
                                WHERE stock.location_number = :allot_location_numbers)]>
        <[AND fa.id IN (SELECT jpbi.fba_id FROM jit_pick_box_item jpbi WHERE jpbi.status = :pick_box_status)]>
        <[AND fa.id IN (SELECT wae.wh_asn_id FROM wh_asn_extra wae
                        LEFT JOIN asn_pick_box apb ON apb.pick_up_order_no = wae.pick_up_order_id
                        WHERE apb.number IN (:ASN_PICK_BOX_NUMBER_LIST))]>
        <[AND fa.id IN (SELECT wae.wh_asn_id FROM wh_asn_extra wae
                        LEFT JOIN asn_pick_box apb ON apb.pick_up_order_no = wae.pick_up_order_id
                        WHERE apb.number = :ASN_PICK_BOX_NUMBER)]>
        <[AND fa.id IN (SELECT wae.wh_asn_id FROM wh_asn_extra wae WHERE wae.collect_method = :collectMethod) ]>
        <[:NOT_NULL_LBX)]>
        <[:QUERY_DIFF)]>
        <[:APV_TRACK_CONDITION)]>
        <[:QUERY_JIT_PICK_BOX)]>
        <[:ORDER_TYPE)]>
        <[:CAN_PRINT)]>
        <[:SHIPMENT_ID_SQL)]>
        <[:TRANSFER_ORDER_FILTER]>
        <[:areaAndAccessCondition]>
        <[:OVERTIME_SQL]>
        <[:FROM_REMAIN_TIME_SQL]>
        <[:TO_REMAIN_TIME_SQL]>
        <[:IS_EUROPE_SQL]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhFbaAllocationAndItems" >
    <content >
      <![CDATA[
        SELECT fa.id, fa.fba_no, fa.bat_no, fa.shipment_id, fa.purpose_house, fa.status, fa.sm_code, fa.shipping_method,fa.shipping_method_by_tms, fa.account_number, fa.site,
        fa.tracking_number,fa.tracking_number_by_tms, fa.push_time, fa.box_push_by, fa.box_push_time, fa.confirm_time, fa.deliver_by, fa.shipping_order_no, fa.shipping_company,fa.load_id,
        fa.deliver_time, fa.cancel_time, fa.departure_time, fa.task_no, fa.pdf_url, fa.transit_type, fa.tags,fa.salesperson, fa.overseas_up_time,fa.remark,fa.reject_reason,fa.logistics_aging, fa.exception_order,
        fai.id, fai.fba_id, fai.box_no, fai.warehouse_id, fai.product_sku, fai.product_barcode, fai.store, fai.site, fai.sku_img,fai.grid_time,fai.grid_by,fai.grid_status,
        fai.fn_sku, fai.sell_sku, fai.sku_tags, fai.quantity, fai.loading_quantity, fai.putaway_quantity, fai.putaway_diff, fai.product_length,fai.tag_time,fai.tag_by,
        fai.product_width, fai.product_height, fai.product_weight, fai.toll_weight, fai.freight, fai.other_price, fai.allot_quantity, fai.grid_quantity,fai.load_time,fai.load_by,fai.company_name,
        fai.pick_quantity,fai.product_price, fai.sell_sku_name, fai.box_cost, fai.sku_cost, fai.sku_quantity, fai.load_num, fai.suit_flag, fai.sku_suit_num,fai.sku_barcode,fai.sc_item_id,
        fai.fn_sku_tags,fa.check_by, fa.check_time, fa.pick_out,fai.process_type,fai.re_process, fa.merge_time,fa.split_region_flag, fai.tag,fai.temu_code_url,fai.temu_tag_url
        ,(SELECT s.name FROM wh_sku s WHERE s.sku=fai.product_sku) AS 'fai.name', fa.apv_type, fa.receive_time, fa.is_return, fa.is_asn
        ,(SELECT GROUP_CONCAT(DISTINCT(wls.location_number))
            FROM wh_apv_out_stock_chain waosc INNER JOIN wh_transfer_stock stock ON waosc.stock_id = stock.id
            inner join wh_stock wls on wls.id = stock.stock_id
            WHERE stock.stock_id is not null and waosc.relevant_no = fa.fba_no AND waosc.sku = fai.product_sku) AS 'fai.allot_location_numbers'
        ,(SELECT jpbi.status FROM jit_pick_box_item jpbi WHERE jpbi.fba_id = fa.id) AS 'fa.pick_box_status'
        ,(SELECT jpb.number FROM  jit_pick_box jpb left join  jit_pick_box_item  jpbi on jpb.id=jpbi.box_id WHERE jpbi.fba_id = fa.id ) AS 'fa.number'
        <[:QUERY_LOAD_TIME]>
        <[:QUERY_WH_ASN_EXTRA_COLUMN)]>
        <[:QUERY_APV_TRACK_COLUMN)]>
        <[:QUERY_ASN_PICK_BOX_COLUMN]>
        <[:SHIPMENTID_COLUMN)]>
        FROM wh_fba_allocation fa
        LEFT JOIN wh_fba_allocation_item fai ON fai.fba_id = fa.id
        <[:QUERY_WH_ASN_EXTRA)]>
        <[:QUERY_APV_TRACK)]>
        INNER JOIN (
			select fa.id
			from wh_fba_allocation fa
			WHERE 1 = 1
	        <[AND fa.id = :id]>
	        <[AND fa.load_id = :load_id]>
            <[AND fa.fba_no = :fba_no]>
            <[AND fa.shipment_id = :shipment_id]>
            <[AND fa.purpose_house = :purpose_house]>
            <[AND fa.account_number = :account_number]>
            <[AND fa.site = :site]>
            <[AND fa.status = :status]>
            <[AND fa.status IN (:status_list)]>
            <[AND fa.sm_code = :sm_code]>
            <[AND fa.shipping_method = :shipping_method]>
            <[AND fa.tracking_number = :tracking_number]>
            <[AND fa.tracking_number like :right_like_tracking_number]>
            <[AND fa.push_time = :push_time]>
            <[AND fa.box_push_by = :box_push_by]>
            <[AND fa.box_push_time = :box_push_time]>
            <[AND fa.confirm_time = :confirm_time]>
            <[AND fa.deliver_by = :deliver_by]>
            <[AND fa.deliver_time = :deliver_time]>
            <[AND fa.cancel_time = :cancel_time]>
            <[AND fa.departure_time = :departure_time]>
            <[AND fa.task_no = :task_no]>
            <[AND fa.bat_no = :bat_no]>
            <[AND fa.pick_out = :pick_out]>
            <[AND fa.split_region_flag = :split_region_flag]>
            <[AND fa.shipment_id IN (:shipmentIdList)]>
            <[AND fa.apv_type = :apv_type]>
            <[AND fa.is_return = :is_return]>
            <[AND fa.is_asn = :is_asn]>
            <[:notReturn]>
            <[:NOT_TRANSIT_TYPE]>
            <[AND fa.transit_type = :transit_type]>
            <[AND fa.push_time >= :from_push_time]>
            <[AND fa.push_time <= :to_push_time]>
            <[AND fa.receive_time >= :from_receive_time]>
            <[AND fa.receive_time >= :from_remain_time]>
            <[AND fa.receive_time <= :to_receive_time]>
            <[AND fa.receive_time <= :to_remain_time]>
            <[AND fa.cancel_time >= :from_cancel_time]>
            <[AND fa.cancel_time <= :to_cancel_time]>
            <[AND fa.box_push_time >= :from_box_push_time]>
            <[AND fa.box_push_time <= :to_box_push_time]>
            <[AND fa.overseas_up_time >= :from_up_time]>
            <[AND fa.overseas_up_time <= :to_up_time]>
            <[AND fa.deliver_time >= :upStartDeliverTime]>
            <[AND fa.deliver_time <= :upEndDeliverTime]>
            <[AND fa.merge_time >= :from_merge_time]>
            <[AND fa.merge_time <= :to_merge_time]>
            <[AND fa.confirm_time >= :fromConfirmTime]>
            <[AND fa.confirm_time <= :toConfirmTime]>
            <[AND fa.exception_order = :exception_order]>
            <[:NOT_EXCEPTION_ORDER]>
            <[:NOT_NULL_TRACKING_NUMBER]>
            <[:TAGS_SQL]>
            <[AND fa.fba_no IN (:fbaNoList)]>
            <[AND fa.apv_type IN (:apvTypeList)]>
            <[AND fa.status IN (:statusList)]>
            <[AND fa.id IN (:idList)]>
            <[AND fa.purpose_house IN (:purposeHouseList)]>
            <[AND fa.purpose_house IN (:purposeHouseForPage)]>
            <[AND fa.purpose_house NOT IN (:purposeHouseNotInList)]>
            <[AND fa.tracking_number IN (:trackingNumberList)]>
            <[AND fa.shipping_order_no IN (:shippingOrderNoList)]>
            <[AND fa.shipping_order_no = :shipping_order_no]>
            <[AND fa.account_number IN (:accountNumberList)]>
            <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE tag IN (:tags))]>
            <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE temu_tag_url IN (:lbxNoList))]>
            <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE product_sku IN (:skuList))]>
            <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE product_sku = :sku)]>
            <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE fn_sku IN (:fnSkuList))]>
            <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE fn_sku = :fnSku)]>
            <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE re_process = :re_process)]>
            <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE process_type = :process_type)]>
            <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE temu_tag_url = :lbxNo)]>
            <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE load_by = :loadBy)]>
            <[AND fa.fba_no IN (SELECT wscta.apv_no FROM wh_scan_shipment_to_apv wscta
                                INNER JOIN wh_scan_shipment wsc ON wscta.scan_shipment_id = wsc.id
                                WHERE is_transfer_house = true AND load_date >= :loadStartTime)]>
            <[AND fa.fba_no IN (SELECT wscta.apv_no FROM wh_scan_shipment_to_apv wscta
                                INNER JOIN wh_scan_shipment wsc ON wscta.scan_shipment_id = wsc.id
                                WHERE is_transfer_house = true AND load_date <= :loadEndTime)]>
            <[AND fa.fba_no IN (SELECT waosc.relevant_no FROM wh_apv_out_stock_chain waosc
                                INNER JOIN wh_transfer_stock stock ON waosc.stock_id = stock.id
                                WHERE stock.location_number IN (:allotLocationNumberList))]>
            <[AND fa.fba_no IN (SELECT waosc.relevant_no FROM wh_apv_out_stock_chain waosc
                                INNER JOIN wh_transfer_stock stock ON waosc.stock_id = stock.id
                                WHERE stock.location_number = :allot_location_numbers)]>
            <[AND fa.id IN (SELECT jpbi.fba_id FROM jit_pick_box_item jpbi WHERE jpbi.status = :pick_box_status)]>
            <[AND fa.id IN (SELECT wae.wh_asn_id FROM wh_asn_extra wae
                        LEFT JOIN asn_pick_box apb ON apb.pick_up_order_no = wae.pick_up_order_id
                        WHERE apb.number IN (:ASN_PICK_BOX_NUMBER_LIST))]>
            <[AND fa.id IN (SELECT wae.wh_asn_id FROM wh_asn_extra wae
                        LEFT JOIN asn_pick_box apb ON apb.pick_up_order_no = wae.pick_up_order_id
                        WHERE apb.number = :ASN_PICK_BOX_NUMBER)]>
            <[AND fa.id IN (SELECT wae.wh_asn_id FROM wh_asn_extra wae WHERE wae.collect_method = :collectMethod) ]>
            <[:NOT_NULL_LBX)]>
            <[:QUERY_DIFF)]>
            <[:APV_TRACK_CONDITION)]>
            <[:QUERY_JIT_PICK_BOX)]>
            <[:SHIPMENT_ID_SQL)]>
            <[:ORDER_TYPE)]>
            <[:CAN_PRINT)]>
            <[:TRANSFER_ORDER_FILTER]>
            <[:areaAndAccessCondition]>
            <[:OVERTIME_SQL]>
            <[:FROM_REMAIN_TIME_SQL]>
            <[:TO_REMAIN_TIME_SQL]>
            <[:QUEST_LOAD_SQL]>
            <[:IS_EUROPE_SQL]>
            ORDER BY push_time DESC
	        <[:LIMIT]>
		) wfa on fa.id = wfa.id
      ]]>
    </content>
  </sql>

  <!--拣货查询 -->
  <sql datasource="dataSource" id="queryAllocationByPickingTaskId" >
    <content >
      <![CDATA[
        SELECT
        	fa.id, fa.fba_no, fa.status, fa.bat_no, fa.push_time, fa.purpose_house ,fai.id, fai.fba_id, fai.product_sku, fai.grid_quantity,fai.allot_quantity,
        	fai.store, fai.fn_sku, fai.sell_sku, fai.quantity, fai.sku_quantity, fai.pick_quantity, fai.site, fai.grid_status,fai.suit_flag
		FROM wh_fba_allocation fa
        LEFT JOIN wh_fba_allocation_item fai ON fai.fba_id = fa.id
		WHERE 1 = 1
		AND fa.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item  LEFT JOIN wh_picking_task task ON task.id = task_item.task_id where task.id = :task_id)
		ORDER BY fa.id ASC
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryGridQuantity" >
    <content >
      <![CDATA[
        SELECT SUM(grid_quantity) FROM (
          SELECT fba_id,product_sku,grid_quantity FROM wh_fba_allocation_item
          WHERE 1 = 1
          AND fba_id IN (SELECT task_item.apv_id from wh_picking_task_item task_item  LEFT JOIN wh_picking_task task ON task.id = task_item.task_id where task.id = :task_id)
          GROUP BY fba_id,product_sku
        )t
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryMaxPriorityGoodsPrintFba" >
    <content >
      <![CDATA[
        SELECT
        	fba.id, fba.fba_no, fba.status, fba.account_number, fba.purpose_house,fba.pdf_url,fba.task_no,fba.tracking_number,fba.is_asn,fba.transit_type
        	,e.package_method,e.box_mark_url
		FROM wh_fba_allocation fba
		JOIN wh_asn_extra e ON e.wh_asn_id = fba.id
		WHERE 1 = 1
		<[AND fba.id IN (SELECT fba_id FROM wh_fba_allocation_item fai WHERE fai.product_sku = :sku) ]>
		<[AND fba.status in (:statusList)]>
		<[AND fba.apv_type = :apvType]>
		<[AND fba.fba_no = :fbaNo]>
		<[:QUERY_BY_TASK_NO]>
		<[:QUERY_BY_TRANSIT_TYPE]>
		<[AND fba.purpose_house in (:purposeHouseList)]>
		<[:ASN_FIRST]>
		ORDER BY <[:CHANNEL_SORT]> fba.id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="clearMergeTimeByFbaId" >
    <content >
      <![CDATA[
        UPDATE wh_fba_allocation
        SET merge_time = NULL
        WHERE 1 = 1
        AND id IN (:fba_id_list)
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="unBoxCountInTask" >
    <content >
      <![CDATA[
        SELECT
            COUNT(*)
        FROM
            wh_picking_task_item wpti
            LEFT JOIN wh_fba_allocation wa ON wa.fba_no = wpti.apv_no
            LEFT JOIN wh_fba_allocation_item fai ON fai.fba_id = wa.id
        WHERE
            fai.box_no IS NULL
            AND wpti.task_id IN ( SELECT task_id FROM wh_picking_task_item WHERE apv_no = :apv_no )
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="unPackCountInTask" >
    <content >
      <![CDATA[
        SELECT
            COUNT(*)
        FROM
            wh_picking_task_item wpti
            LEFT JOIN wh_fba_allocation wa ON wa.fba_no = wpti.apv_no
            LEFT JOIN temu_prepare_order_item te ON te.package_sn = wpti.apv_no
        WHERE
            (wa.status = 15 OR te.package_status = 7)
            AND wpti.task_id IN ( SELECT task_id FROM wh_picking_task_item WHERE apv_no = :apv_no )
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryToDeleteList" >
    <content >
      <![CDATA[
        SELECT
            a.id as id,a.fba_no AS relevant_no,i.product_sku AS sku, i.sku_barcode AS skuBarcode
        FROM
            wh_fba_allocation a
            LEFT JOIN wh_fba_allocation_item i ON i.fba_id = a.id
            WHERE 1=1
            AND a.purpose_house IN ('SMT','Shein','TEMU')
            AND a.`status` IN (17,18)
	        <[AND a.deliver_time  >= :from_deliver_date]>
	        <[AND a.deliver_time  < :to_deliver_date]>
	    UNION
	    SELECT
            a.id as id,a.fba_no AS relevant_no,i.product_sku AS sku, i.sku_barcode AS skuBarcode
        FROM
            wh_fba_allocation a
            LEFT JOIN wh_fba_allocation_item i ON i.fba_id = a.id
            WHERE 1=1
            AND a.purpose_house IN ('SMT','Shein','TEMU')
	        <[:CANCEL_QUERY]>
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryLocalJitToDeleteList" >
    <content >
      <![CDATA[
        SELECT
          apv.apv_no,apv_item.sku,apv_item.buyer_checkout
          FROM wh_apv apv
          LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
          WHERE apv.ship_status IN (22, 23) AND apv.status IN (17, 18)
          <[AND apv.apv_no IN (SELECT wt.apv_no FROM apv_track wt WHERE 1 = 1 AND wt.deliver_time >= :from_deliver_date)]>
          <[AND apv.apv_no IN (SELECT wt.apv_no FROM apv_track wt WHERE 1 = 1 AND wt.deliver_time < :to_deliver_date)]>
        UNION
        SELECT
          apv.apv_no,apv_item.sku,apv_item.buyer_checkout
          FROM wh_apv apv
          LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
          WHERE apv.ship_status IN (22, 23)
	      <[:CANCEL_QUERY]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTransferOrderMegerCount" >
    <content >
      <![CDATA[
      SELECT COUNT(id) AS totalCount FROM (
        SELECT fa.id
        <[:areaAndAccessCondition]>
        <[:TOTAL_LOCATIONS_SQL]>
        FROM wh_fba_allocation fa
        WHERE 1 = 1
        <[AND fa.id IN (:idList)]>
        <[AND fa.fba_no IN (:orderNoList)]>
        <[AND fa.apv_type = :apv_type]>
        <[AND fa.apv_type IN (:apvTypeList)]>
        <[AND fa.split_region_flag = :split_region_flag]>
        <[AND fa.push_time >= :from_push_time]>
        <[AND fa.push_time <= :to_push_time]>
        <[AND fa.receive_time >= :from_receive_time]>
        <[AND fa.receive_time <= :to_receive_time]>
        <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE product_sku IN (:skuList))]>
        <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE product_sku = :sku)]>
        <[AND fa.status = :status]>
        <[AND fa.status IN (:status_list)]>
        <[:TRANSFER_ORDER_FILTER]>
        <[:NOT_NULL_LBX)]>
        <[:TAGS_SQL]>
        <[:FROM_REMAIN_TIME_SQL]>
        <[:TO_REMAIN_TIME_SQL]>
        <[:NOT_QUERY]>
        <[:OVERTIME_SQL]>
        <[:LOCATIONS_HAVING]>
        UNION ALL
        SELECT tpoi.id
         <[:areaAndAccessConditionTemu]>
        <[:TOTAL_LOCATIONS_TEMU_SQL]>
        FROM temu_prepare_order_item tpoi
        INNER JOIN temu_prepare_order tpo ON  tpo.id = tpoi.prepare_order_id
        WHERE 1 = 1
        <[AND tpo.type = :type]>
        <[AND tpo.creation_date >= :from_receive_time]>
        <[AND tpo.creation_date <= :to_receive_time]>
        <[AND tpo.push_time >= :from_push_time]>
        <[AND tpo.push_time <= :to_push_time]>
        <[AND tpo.hot_sale = :hot_sale]>
        <[AND tpo.split_region_flag = :split_region_flag]>
        <[AND tpoi.sku = :sku]>
        <[AND tpoi.sku in (:skuList)]>
        <[AND tpoi.package_status = :packageStatus]>
        <[AND tpoi.id = :itemId]>
        <[AND tpoi.id in (:itemIdList)]>
        <[AND tpoi.package_sn IN (:orderNoList)]>
        <[:NOT_QUERY_TEMU]>
        <[:APV_TYPE_TEMU]>
        <[:LOCATIONS_HAVING]>
        ) combined_transfer_meger
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryTransferOrderMegers" >
    <content >
      <![CDATA[
      select * from (
      SELECT fa.id as id, fa.fba_no as orderNo,fa.purpose_house as saleChannel,fa.apv_type as apvType,fa.salesperson as salesperson ,
            fa.account_number as accountNumber ,fa.tags as tag,(SELECT GROUP_CONCAT(DISTINCT(wls.location_number))
            FROM wh_apv_out_stock_chain waosc INNER JOIN wh_transfer_stock stock ON waosc.stock_id = stock.id
            inner join wh_stock wls on wls.id = stock.stock_id
            WHERE stock.stock_id is not null and waosc.relevant_no = fa.fba_no AND waosc.sku = fai.product_sku) AS allotLocationNumbers
            ,fa.status as status,fa.push_time as pushTime, fa.receive_time as receiveTime,fai.temu_tag_url as trackingNumber,
            fa.shipping_company as shippingCompany, null as hotSale, fa.cancel_time as cancelTime,
            fai.id as itemId,fai.product_sku as sku,fai.quantity as quantity,null as type
        <[:QUERY_LOAD_TIME]>
        <[:QUERY_WH_ASN_EXTRA_COLUMN)]>
        <[:QUERY_COLUMN]>
        FROM wh_fba_allocation fa
        LEFT JOIN wh_fba_allocation_item fai ON fai.fba_id = fa.id
        <[:QUERY_WH_ASN_EXTRA)]>
        INNER JOIN (
			select fa.id
			<[:areaAndAccessCondition]>
            <[:TOTAL_LOCATIONS_SQL]>
			from wh_fba_allocation fa
			WHERE 1 = 1
	        <[AND fa.id IN (:idList)]>
	        <[AND fa.fba_no IN (:orderNoList)]>
            <[AND fa.apv_type = :apv_type]>
            <[AND fa.split_region_flag = :split_region_flag]>
            <[AND fa.push_time >= :from_push_time]>
            <[AND fa.push_time <= :to_push_time]>
            <[AND fa.receive_time >= :from_receive_time]>
            <[AND fa.receive_time <= :to_receive_time]>
            <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE product_sku IN (:skuList))]>
            <[AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE product_sku = :sku)]>
            <[AND fa.status = :status]>
            <[AND fa.status IN (:status_list)]>
            <[AND fa.purpose_house = :saleChannel]>
            <[AND fa.purpose_house IN (:saleChannelList)]>
            <[:TRANSFER_ORDER_FILTER]>
            <[:NOT_NULL_LBX)]>
            <[:TAGS_SQL]>
            <[:FROM_REMAIN_TIME_SQL]>
            <[:TO_REMAIN_TIME_SQL]>
             <[:NOT_QUERY]>
            <[:OVERTIME_SQL]>
            <[:LOCATIONS_HAVING]>
		) wfa on fa.id = wfa.id
		UNION ALL
		select
            tpo.id as id , tpoi.package_sn as orderNo, 'temu' as saleChannel, IF(tpoi.real_quantity>1,'SM','SS') as apvType, tpo.seller as salesperson,
            tpo.account_number as accountNumber,null  as tag
            ,(select GROUP_CONCAT(wls.location_number) from wh_apv_out_stock_chain sch inner join wh_transfer_stock sto on sch.stock_id = sto.id
            inner join wh_stock wls on wls.id = sto.stock_id
             where sto.stock_id is not null and sch.relevant_no = tpo.prepare_order_no and sch.sku = tpoi.sku) as allotLocationNumbers
             ,tpoi.package_status as status,tpo.push_time as pushTime, tpo.creation_date as receiveTime,tpo.express_delivery as trackingNumber,
            tpo.shipping_company as shippingCompany, tpo.hot_sale as hotSale, null as cancelTime,
            tpoi.id as itemId,tpoi.sku as sku,tpoi.real_quantity as quantity,tpo.type as type,null as loadTime,null as packageMethod
            <[:areaAndAccessConditionTemu]>
            <[:TOTAL_LOCATIONS_TEMU_SQL]>
        FROM  temu_prepare_order_item tpoi
        INNER JOIN temu_prepare_order tpo ON  tpo.id = tpoi.prepare_order_id
        WHERE 1 = 1
        <[AND tpo.type = :type]>
        <[AND tpo.creation_date >= :from_receive_time]>
        <[AND tpo.creation_date <= :to_receive_time]>
        <[AND tpo.push_time >= :from_push_time]>
        <[AND tpo.push_time <= :to_push_time]>
        <[AND tpo.hot_sale = :hot_sale]>
        <[AND tpo.status = :status]>
        <[AND tpo.split_region_flag = :split_region_flag]>
        <[AND tpoi.sku = :sku]>
        <[AND tpoi.sku in (:skuList)]>
        <[AND tpoi.package_status = :packageStatus]>
        <[AND tpoi.id = :itemId]>
        <[AND tpoi.id in (:itemIdList)]>
        <[AND tpoi.package_sn IN (:orderNoList)]>
        <[:NOT_QUERY_TEMU]>
        <[:APV_TYPE_TEMU]>
        <[:LOCATIONS_HAVING]>
        ) as combined_transfer_meger  ORDER BY pushTime DESC
      ]]>
    </content>
  </sql>
</sqlmap>