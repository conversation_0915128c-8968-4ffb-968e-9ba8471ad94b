<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryFbaUpMonitorCount" >
    <content >
      <![CDATA[
        SELECT count(*)
        FROM fba_up_monitor
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND shipment_id = :shipment_id]>
        <[AND fba_no = :fba_no]>
        <[AND destination_warehouse_id = :destination_warehouse_id]>
        <[AND sales_person = :sales_person]>
        <[AND total_quantity = :total_quantity]>
        <[AND received_quantity = :received_quantity]>
        <[AND difference_quantity = :difference_quantity]>
        <[AND shipping_date = :shipping_date]>
        <[AND received_date = :received_date]>
        <[AND estimated_shelf_date = :estimated_shelf_date]>
        <[AND actual_shelf_date = :actual_shelf_date]>
        <[AND waiting_days = :waiting_days]>
        <[AND status = :status]>
        <[AND alert_status = :alert_status]>
        <[AND sku_code = :sku_code]>
        <[AND fnsku = :fnsku]>
        <[AND seller_sku = :seller_sku]>
        <[AND created_at = :created_at]>
        <[AND updated_at = :updated_at]>
        
        <[AND shipment_id IN (:shipmentIdList)]>
        <[AND sku_code IN (:skuCodeList)]>
        <[AND fnsku IN (:fnskuList)]>
        <[AND id IN (:idList)]>
        <[AND sales_person IN (:salesPersonList)]>
        <[AND alert_status IN (:alertStatusList)]>
        <[AND status IN (:fbaStatusList)]>
        <[AND destination_warehouse_id IN (:destinationWarehouseIdList)]>
        
        <[AND DATE(received_date) >= :receivedDateStart]>
        <[AND DATE(received_date) <= :receivedDateEnd]>
        <[AND DATE(created_at) >= :createdAtStart]>
        <[AND DATE(created_at) <= :createdAtEnd]>
        
        <[AND waiting_days >= :waitingDaysMin]>
        <[AND waiting_days <= :waitingDaysMax]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryFbaUpMonitorList" >
    <content >
      <![CDATA[
        SELECT id, shipment_id, fba_no, destination_warehouse_id, sales_person, total_quantity, 
        received_quantity, difference_quantity, shipping_date, received_date, estimated_shelf_date, 
        actual_shelf_date, waiting_days, status, alert_status, sku_code, fnsku, seller_sku, 
        created_at, updated_at
        FROM fba_up_monitor
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND shipment_id = :shipment_id]>
        <[AND fba_no = :fba_no]>
        <[AND destination_warehouse_id = :destination_warehouse_id]>
        <[AND sales_person = :sales_person]>
        <[AND total_quantity = :total_quantity]>
        <[AND received_quantity = :received_quantity]>
        <[AND difference_quantity = :difference_quantity]>
        <[AND shipping_date = :shipping_date]>
        <[AND received_date = :received_date]>
        <[AND estimated_shelf_date = :estimated_shelf_date]>
        <[AND actual_shelf_date = :actual_shelf_date]>
        <[AND waiting_days = :waiting_days]>
        <[AND status = :status]>
        <[AND alert_status = :alert_status]>
        <[AND sku_code = :sku_code]>
        <[AND fnsku = :fnsku]>
        <[AND seller_sku = :seller_sku]>
        <[AND created_at = :created_at]>
        <[AND updated_at = :updated_at]>
        

        <[AND shipment_id IN (:shipmentIdList)]>
        <[AND sku_code IN (:skuCodeList)]>
        <[AND fnsku IN (:fnskuList)]>
         <[AND id IN (:idList)]>
        <[AND sales_person IN (:salesPersonList)]>
        <[AND alert_status IN (:alertStatusList)]>
        <[AND status IN (:fbaStatusList)]>
        <[AND destination_warehouse_id IN (:destinationWarehouseIdList)]>
        
        <[AND DATE(received_date) >= :receivedDateStart]>
        <[AND DATE(received_date) <= :receivedDateEnd]>
        <[AND DATE(created_at) >= :createdAtStart]>
        <[AND DATE(created_at) <= :createdAtEnd]>
        
        <[AND waiting_days >= :waitingDaysMin]>
        <[AND waiting_days <= :waitingDaysMax]>
        
        ORDER BY created_at DESC, id DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryFbaUpMonitorByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, shipment_id, fba_no, destination_warehouse_id, sales_person, total_quantity, 
        received_quantity, difference_quantity, shipping_date, received_date, estimated_shelf_date, 
        actual_shelf_date, waiting_days, status, alert_status, sku_code, fnsku, seller_sku, 
        created_at, updated_at
        FROM fba_up_monitor
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryFbaUpMonitor" >
    <content >
      <![CDATA[
        SELECT id, shipment_id, fba_no, destination_warehouse_id, sales_person, total_quantity, 
        received_quantity, difference_quantity, shipping_date, received_date, estimated_shelf_date, 
        actual_shelf_date, waiting_days, status, alert_status, sku_code, fnsku, seller_sku, 
        created_at, updated_at
        FROM fba_up_monitor
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND shipment_id = :shipment_id]>
        <[AND fba_no = :fba_no]>
        <[AND destination_warehouse_id = :destination_warehouse_id]>
        <[AND sales_person = :sales_person]>
        <[AND total_quantity = :total_quantity]>
        <[AND received_quantity = :received_quantity]>
        <[AND difference_quantity = :difference_quantity]>
        <[AND shipping_date = :shipping_date]>
        <[AND received_date = :received_date]>
        <[AND estimated_shelf_date = :estimated_shelf_date]>
        <[AND actual_shelf_date = :actual_shelf_date]>
        <[AND waiting_days = :waiting_days]>
        <[AND status = :status]>
        <[AND alert_status = :alert_status]>
        <[AND sku_code = :sku_code]>
        <[AND fnsku = :fnsku]>
        <[AND seller_sku = :seller_sku]>
        <[AND created_at = :created_at]>
        <[AND updated_at = :updated_at]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createFbaUpMonitor" >
    <content >
      <![CDATA[
        INSERT INTO fba_up_monitor (shipment_id, fba_no, destination_warehouse_id, sales_person, total_quantity, 
          received_quantity, difference_quantity, shipping_date, received_date, estimated_shelf_date, 
          actual_shelf_date, waiting_days, status, alert_status, sku_code, fnsku, seller_sku, 
          created_at, updated_at)
        VALUES (:shipment_id, :fba_no, :destination_warehouse_id, :sales_person, :total_quantity, 
          :received_quantity, :difference_quantity, :shipping_date, :received_date, :estimated_shelf_date, 
          :actual_shelf_date, :waiting_days, :status, :alert_status, :sku_code, :fnsku, :seller_sku, 
          :created_at, :updated_at)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteFbaUpMonitorByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM fba_up_monitor
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateFbaUpMonitorByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE fba_up_monitor
        SET <[shipment_id = :shipment_id,]>
          <[fba_no = :fba_no,]>
          <[destination_warehouse_id = :destination_warehouse_id,]>
          <[sales_person = :sales_person,]>
          <[total_quantity = :total_quantity,]>
          <[received_quantity = :received_quantity,]>
          <[difference_quantity = :difference_quantity,]>
          <[shipping_date = :shipping_date,]>
          <[received_date = :received_date,]>
          <[estimated_shelf_date = :estimated_shelf_date,]>
          <[actual_shelf_date = :actual_shelf_date,]>
          <[waiting_days = :waiting_days,]>
          <[status = :status,]>
          <[alert_status = :alert_status,]>
          <[sku_code = :sku_code,]>
          <[fnsku = :fnsku,]>
          <[seller_sku = :seller_sku,]>
          <[created_at = :created_at,]>
          <[updated_at = :updated_at,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>