package com.estone.system.downloadcenter.enums;

public enum WhDownloadContentEnum {
    CHECK_IN("1", "入库单列表"), SKU("2",  "sku列表"), APV("3", "发货单列表"),
    STOCK_MONITOR("4",  "库存监控"), DRP_TURNOVER_ITEM("5",  "出入库明细"), PURCHASE_ORDER("6",  "采购单"),
    CHECK_IN_EXCEPTION("7", "入库单异常"),STOCK("8", "库存列表"),INVOICING_CHANGE_COUNT("9", "月度汇总"),
    PRE_STORAGE_STOCK_TRANSFER("10", "存货迁移任务明细"),
    PRE_STORAGE_STOCK("11", "存货查询"),
    TRANSIT_BATCH_DETAIL("13", "中转仓库存明细批次"),LEND("14","外借单管理"),LEND_DESTROY("15","外借单核销"),
    LEND_CHECK_IN("16","外借入库单"),
    APV_EXPRESS("17", "快递发货单列表"),
    CLOTHING_BOUTIQUE_DEV_PRODUCT("18", "精品SKU"),
    CLOTHING_BOUTIQUE_DEV_CONSUMABLE("19", "精品耗材"),
    ORDER_CANCEL_REPORT("20","订单取消报表"),
    ORDER_CANCEL_REPORT_ITEM("21","订单取消报表明细"),
    ALL_STATISTICS("22", "总作业报表"),
    BOUTIQUE_STOCK_RELATION("23", "精品SKU衍生列表"),
    BOUTIQUE_OUT_ORDER("24", "精品sku货件出库单"),
    BOUTIQUE_OUT_ORDER_ITEM("25", "精品sku货件出库单明细"),
    BOUTIQUE_PURCHASE_ORDER("26", "精品采购单"),
    BOUTIQUE_STOCK("27", "精品库存"),

    BOUTIQUE_STOCK_LOG("28", "精品库存日志"),
    INVENTORY_STATEMENT("29","盘点报表"),
    EXP_MANAGE_CHECK_TASK("30","保质期批次核对任务"),

    ARCHIVE_STOCK("31", "归档库存"),

    OVERSEAS_RETURN_SKU_COUNT("32","海外退件上架SKU统计报表"),

    OVERSEAS_RETURN_ORDER_COUNT("32","海外退件上架平台订单统计报表"),

    AFTER_SALE_DAY_OF_STOCK("33","售后结算进销存"),
    LOCATION_CHECK_TASK("34", "库位核对任务"),

    STOCK_CHANGE_RECORD("35","总库存明细"),

    SPO_PICKING_TASK("36","内购单拣货任务"),

    SHOP_ORDER("37","内购单"),

    INVENTORY_DETAILS("38","耗材进销存明细"),

    CE_MANAGE("39","ce图片管理"),

    MERGE_SKU_TASK("40","合并sku任务"),

    MERGE_SKU_MANAGE("41","合并SKU管理"),

    SCAN_SHIPMENT("51","结袋卡"),
    SCAN_SHIPMENT_DETAIL("52","结袋卡详情"),
    PUSH_AND_DELIVER_COUNT("53","推单交运量"),

    APV_OVERSIZE("54", "超体积发货单列表"),

    TEMU_PREPARE_ORDER("55", "拼多多备货单"),

    TEMU_PACKAGE("56", "拼多多包裹管理"),
    TEMU_RETURN_PACKAGE("57", "TEMU退仓管理"),
    TEMU_SUMMARY_MONTHLY("58", "TEMU月度库存汇总"),

    COMBINE_SKU("61", "合并SKU"),
    COMBINE_SKU_TASK("62", "组装任务"),
    COMBINE_SKU_PICKING_TASK("62", "组装拣货任务"),

    PDA_RETURN_RACK_QUERY_RECORD("63", "PDA返架查询记录"),

    SCRAP_RECOMMEND_MONTH("64", "报废建议"),

    EXCEPTION_RECORD("65", "交运异常记录"),
    EXPRESS_RECORD("66", "快递签收记录"),
    SMT_RETURN_ORDER("67", "托管退仓管理"),

    ALLOCATION_DEMAND_AUTO("68", "两仓自动调拨需求"),
    OTHERS("69", "其它"),

    INVENTORY_ABNORMAL_DATA("70", "库区盘点异常数据"),
    SKU_LABEL_RETURN_STATISTICS("71", "SKU标签退货统计"),
    MONTH_PURCHASE_FREIGHT_UNIT_PRICE_ALL("72", "汇总采购运费成本单价"),
    SKU_LABEL_RETURN_STATISTICS_DETAIL("73", "SKU标签退货统计明细"),

    SMT_INVENTORY_SUMMARY("74", "SMT仓发库存汇总"),
    SMT_INVENTORY_DETAIL("75", "SMT仓发出入库明细"),
    SMT_INVENTORY_EXCEPTION("76", "SMT仓发异常"),

    SHEIN_RETURN_ORDER("77", "SHEIN仓发异常"),

    SMT_INVOICING_COUNT("78", "SMT仓发进销存汇总"),

    TRANSIT_STOCK_LOG("79", "中转仓库存日志"),
    GRID_EXCEPTION("80", "播种异常管理"),

    FBA_ALLOCATION_ORDER("81", "FBA调拨发货单管理"),
    ASN_ALLOCATION_ORDER("82", "海外仓发货单管理"),
    JIT_ASN_ALLOCATION_ORDER("83", "SMT仓发单管理"),

    ASN_TRANSFER_ALLOCATION_ORDER("84", "中转仓发货单管理"),
    FBA_SHIPMENT_ORDER("85", "FBA货件单管理"),
    SMT_PREPARE_ORDER_COMPARISON("86", "备货单对比"),
    RETURN_ORDER_COMPARISON("87", "退货单差异比对"),
    SHIPMENT_ORDER_COMPARISON("88", "发货单差异比对"),
    VERIFY_CHECK_IN_ORDER("89", "入库核查单"),
    STOCK_TRACE_WH_CHECK_IN_ORDER("90", "库存溯源-入库明细"),

    STOCK_TRACE_WH_CHECK_IN_ORDER_DETAIL("91", "每月库存快照"),
    STOCK_TRACE_WH_CHECK_IN_MATCH_RESULT("92", "库存溯源-库存匹配"),
    INVENTORY_AGE_REPORT("93", "库存溯源-库龄报表"),
    WAREHOUSE_SHIPMENT("94", "仓发自寄管理"),
    ;

    private String name;
    private String code;

     WhDownloadContentEnum( String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static WhDownloadContentEnum build(String code) {
        WhDownloadContentEnum[] values = values();
        for (WhDownloadContentEnum type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static String getDisplayByCode(String code) {
        WhDownloadContentEnum[] values = values();
        for (WhDownloadContentEnum type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        WhDownloadContentEnum[] values = values();
        for (WhDownloadContentEnum type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
