package com.estone.xxljob;

import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.common.*;
import com.estone.apv.service.WhApvService;
import com.estone.asn.service.WhAsnCheckOutService;
import com.estone.common.util.JedisUtils;
import com.estone.warehouse.service.WhRecordService;
import com.estone.xxljob.config.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 *
 * @Description: 扣减剩余库存定时任务
 *
 * @ClassName: DeductWhrecordJob
 * @Author: wanglin
 * @Date: 2018年8月21日
 * @Version: 0.0.1
 */
@Slf4j
@Component
public class DeductWhrecordJobHandler extends AbstractJobHandler {

    DeductWhrecordJobHandler(){
        super("DeductWhrecordJobHandler");
    }

    @Resource
    private WhApvService whApvService;

    @Resource
    private WhAsnCheckOutService whAsnCheckOutService;

    @Resource
    private WhRecordService whRecordService;

    @Override
    //@XxlJob("DeductWhrecordJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        logger.warn("*****************定时匹配库存[规则匹配]*****************");
        XxlJobLogger.log("----------- 定时匹配库存[规则匹配] -------------------");
        if (JedisUtils.exists(ApvTaskRedisLock.BATCH_MAPPING_INVENTORY.getName())) {
            XxlJobLogger.log("----------- 正在分配中 -------------------");
            return ReturnT.FAIL;
        }
        try {
            JedisUtils.set(ApvTaskRedisLock.BATCH_MAPPING_INVENTORY.getName(), "lock", 3600L);
            JedisUtils.del(ApvAllotStock.BATCH_ALLOT_OUT_OF_STOCK_KEY);

            //优先匹配优选仓库存
            //whApvService.pacStockAllotWhApv();

            // 优先匹配海外仓
            whAsnCheckOutService.batchExeAllot(false);

            // 优先匹配虚拟海外仓 改状态并且扣库存
            WhApvQueryCondition query0 = new WhApvQueryCondition(WhApvQueryCondition.RULE_VIRTUAL_OVERSEA);
            query0.setOrderBy(ApvOrderBy.CREATE_DATE_ASC.intCode());
            String message = whApvService.batchAllotWhApv(query0, null);
            logger.warn("虚拟海外仓: " + message);

            // 匹配特急订单 改状态并且扣库存
            WhApvQueryCondition query1 = new WhApvQueryCondition(WhApvQueryCondition.RULE_URGENT);
            query1.setOrderBy(ApvOrderBy.CREATE_DATE_ASC.intCode());
            message = whApvService.batchAllotWhApv(query1, null);
            logger.warn("特急订单: " + message);

            WhApvQueryCondition query = new WhApvQueryCondition();
            // 待分配 缺货 apv
            query.setStatusList(Arrays.asList(ApvStatus.STOCKOUT_NOT.intCode(), ApvStatus.WAITING_ALLOT.intCode()));
            // 排除安检退件类型的发货单
            query.setExcludeShipStatus(ApvOrderType.SECURITY_CHECK_REFUND.intCode());
            query.setOrderBy(ApvOrderBy.CREATE_DATE_ASC.intCode());
            // 改状态并且扣库存
            message = whApvService.batchAllotWhApv(query, null);
            logger.warn("普通订单: " + message);

            // TODO 匹配取消、已拣返架等待返架库存
            List<Integer> apvIds = JedisUtils.smembers(ApvAllotStock.BATCH_ALLOT_OUT_OF_STOCK_KEY);
            if (CollectionUtils.isNotEmpty(apvIds)){
                logger.warn("allotAndCancelByApvIds : " + StringUtils.join(apvIds, ","));
                try {
                    message = whApvService.allotAndCancelByApvIds(apvIds);
                    logger.warn("job allotStock message : " + message);
                }catch (Exception e){
                    logger.error("匹配待返架库存失败：" + e.getMessage(),e);
                }
            }

            // 待分配 安检退件
            WhApvQueryCondition query2 = new WhApvQueryCondition();
            // 待分配
            query2.setStatus(ApvStatus.WAITING_ALLOT.intCode());

            // 安检退件
            query2.setShipStatus(ApvOrderType.SECURITY_CHECK_REFUND.intCode());

            List<WhApv> whApvAndItemList = whApvService.queryWhApvAndItemList(query2, null);

            // 批量修改状态为待包装
            if (CollectionUtils.isNotEmpty(whApvAndItemList)) {
                whRecordService.batchUpdateQuantityBySecurityRefund(whApvAndItemList);
            }
            
            // 匹配缺货订单上架后未匹配的库存
            whApvService.allotUpNotAllotApv();
        }
        catch (Exception e) {
            logger.error("批量匹配库存异常", e);
            XxlJobLogger.log("批量匹配库存异常", e);
        }
        finally {
            JedisUtils.del(ApvTaskRedisLock.BATCH_MAPPING_INVENTORY.getName());
        }
        return ReturnT.SUCCESS;
    }
}
