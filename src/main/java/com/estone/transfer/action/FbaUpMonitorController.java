package com.estone.transfer.action;
import com.estone.common.SelectJson;
import com.estone.transfer.bean.FbaUpMonitor;
import com.estone.transfer.bean.FbaUpMonitorQueryCondition;
import com.estone.transfer.enums.FbaAlertStatus;
import com.estone.transfer.enums.FbaStatus;
import com.estone.transfer.service.FbaUpMonitorService;
import com.estone.transfer.service.WhFbaAllocationService;
import com.estone.transfer.service.impl.FbaUpMonitorServiceImpl;
import com.estone.common.util.model.ApiResult;
import com.estone.common.util.model.CQuery;
import com.estone.common.util.POIUtils;
import com.estone.system.downloadcenter.enums.WhDownloadContentEnum;
import com.estone.system.downloadcenter.service.WhDownloadCenterService;
import com.whq.tool.component.Pager;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Objects;
import java.text.SimpleDateFormat;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "fbaUpMonitor")
@Slf4j
public class FbaUpMonitorController {
    @Resource
    private FbaUpMonitorService fbaUpMonitorService;
    
    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Resource
    private WhDownloadCenterService whDownloadCenterService;

    private static final String[] HEADERS = {"编号", "货件编号", "发货单号", "目的仓", "销售人员", 
        "SKU代码", "FNSKU", "SellerSku", "发货数量", "上架数量", "差异数量",
        "预警状态",  "签收时间", "预计上架时间", "上架时间", "等待天数", "货件状态"};

    /**
     * 获取FBA状态下拉选项（合并签收状态和上架状态）
     * @return ApiResult<List<SelectJson>>
     */
    @PostMapping(value = "getFbaStatusOptions")
    @ResponseBody
    public ApiResult<?> getFbaStatusOptions() {
        try {
            return ApiResult.newSuccess(SelectJson.getSelectJsonList(FbaStatus.values()));
        } catch (Exception e) {
            log.error("获取FBA状态下拉选项失败", e);
            return ApiResult.newError("获取FBA状态下拉选项失败：" + e.getMessage());
        }
    }

    /**
     * 获取预警状态下拉选项
     * @return ApiResult<List<SelectJson>>
     */
    @PostMapping(value = "getAlertStatusOptions")
    @ResponseBody
    public ApiResult<?> getAlertStatusOptions() {
        try {
            return ApiResult.newSuccess(SelectJson.getSelectJsonList(FbaAlertStatus.values()));
        } catch (Exception e) {
            log.error("获取预警状态下拉选项失败", e);
            return ApiResult.newError("获取预警状态下拉选项失败：" + e.getMessage());
        }
    }

    /**
     * 获取销售人员下拉选项
     * @return ApiResult<List<SelectJson>>
     */
    @PostMapping(value = "getSalespersonOptions")
    @ResponseBody
    public ApiResult<?> getSalespersonOptions() {
        try {
            // 查询去重的销售人员列表
            List<String> salespersonList = whFbaAllocationService.queryDistinctSalespersonList();
            
            // 转换为下拉选项格式
            List<SelectJson> options = new ArrayList<>();
            for (String salesperson : salespersonList) {
                options.add(new SelectJson(salesperson, salesperson));
            }
            
            return ApiResult.newSuccess(options);
        } catch (Exception e) {
            log.error("获取销售人员下拉选项失败", e);
            return ApiResult.newError("获取销售人员下拉选项失败：" + e.getMessage());
        }
    }



    /**
     * 分页查询FBA货件监控数据
     * @param query 查询条件
     * @return ApiResult<CQueryResult<FbaUpMonitor>>
     */
    @PostMapping(value = "list")
    @ResponseBody
    public ApiResult<?> getFbaUpMonitorList(@RequestBody CQuery<FbaUpMonitorQueryCondition> query) {
        try {
            if (query == null)
                return ApiResult.newError("param error");
            if (query.getSearch() == null) {
                query.setSearch(new FbaUpMonitorQueryCondition());
            }
            query.getSearch().setReadOnly(true);
            return ((FbaUpMonitorServiceImpl) fbaUpMonitorService).list(query);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 获取各状态货件统计数量
     * @param query 查询条件
     * @return ApiResult<Map<String, Integer>>
     */
    @PostMapping(value = "getStatusStatistics")
    @ResponseBody
    public ApiResult<?> getStatusStatistics(@RequestBody FbaUpMonitorQueryCondition query) {
        try {
            if (query == null) {
                query = new FbaUpMonitorQueryCondition();
            }
            query.setReadOnly(true);
            
            // 调用Service层方法获取状态统计
            Map<String, Integer> statistics = fbaUpMonitorService.getStatusStatistics(query);
            
            // 为了兼容前端，将total映射为all
            if (statistics.containsKey("total")) {
                statistics.put("all", statistics.get("total"));
                statistics.remove("total");
            }
            
            // 为了兼容前端，将not_up映射为received (查询筛选中的"未上架"对应选项卡中的"已签收")
            if (statistics.containsKey("not_up")) {
                statistics.put("received", statistics.get("not_up"));
            }
            
            return ApiResult.newSuccess(statistics);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }



    /**
     * 导出FBA货件监控数据
     * @param query 查询条件
     * @return ApiResult<String> 导出结果
     */
    @PostMapping(value = "export")
    @ResponseBody
    public ApiResult<?> exportFbaUpMonitorData(@RequestBody FbaUpMonitorQueryCondition query) {
        try {
            if (Objects.isNull(query)) {
                query = new FbaUpMonitorQueryCondition();
            }
            query.setReadOnly(true);
            
            String fileName = "FBA货件上架监控_" + System.currentTimeMillis() + ".xlsx";
            boolean isAll = whDownloadCenterService.checkFieldAllNull(query);
            Pager pager = new Pager();
            pager.setPageNo(-1);
            
            FbaUpMonitorQueryCondition finalQuery = query;
            whDownloadCenterService.downloading(fileName, HEADERS, WhDownloadContentEnum.FBA_UP_MONITOR, isAll, pager,
                    (page) -> {
                        List<FbaUpMonitor> fbaUpMonitors = fbaUpMonitorService.queryFbaUpMonitors(finalQuery, page);
                        return this.getExportData(fbaUpMonitors);
                    });
                    
            return ApiResult.newSuccess("导出任务已创建，请到下载中心查看结果");
            
        } catch (Exception e) {
            log.error("FBA货件监控数据导出失败", e);
            return ApiResult.newError("导出失败：" + e.getMessage());
        }
    }

    /**
     * 转换FBA监控数据为导出格式
     * @param fbaUpMonitors FBA货件监控数据列表
     * @return 转换后的导出数据
     */
    private List<List<String>> getExportData(List<FbaUpMonitor> fbaUpMonitors) {
        List<List<String>> data = new ArrayList<>();
        if (CollectionUtils.isEmpty(fbaUpMonitors)) {
            return data;
        }
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        for (FbaUpMonitor monitor : fbaUpMonitors) {
            List<String> row = new ArrayList<>(HEADERS.length);
            
            // 基础信息
            row.add(POIUtils.transferObj2Str(monitor.getId()));
            row.add(POIUtils.transferObj2Str(monitor.getShipmentId()));
            row.add(POIUtils.transferObj2Str(monitor.getFbaNo()));
            row.add(POIUtils.transferObj2Str(monitor.getDestinationWarehouseId()));
            row.add(POIUtils.transferObj2Str(monitor.getSalesPerson()));
            
            // SKU信息
            row.add(POIUtils.transferObj2Str(monitor.getSkuCode()));
            row.add(POIUtils.transferObj2Str(monitor.getFnsku()));
            row.add(POIUtils.transferObj2Str(monitor.getSellerSku()));
            
            // 数量信息
            row.add(POIUtils.transferObj2Str(monitor.getTotalQuantity()));
            row.add(POIUtils.transferObj2Str(monitor.getReceivedQuantity()));
            row.add(POIUtils.transferObj2Str(monitor.getDifferenceQuantity()));

            row.add(StringUtils.isBlank(monitor.getAlertStatus()) ? "" : 
                    Objects.isNull(FbaAlertStatus.getNameByCode(monitor.getAlertStatus())) ? 
                    monitor.getAlertStatus() : FbaAlertStatus.getNameByCode(monitor.getAlertStatus()));
            
            // 时间信息（所有时间字段统一使用Timestamp格式）
            row.add(POIUtils.transferObj2Str(Objects.isNull(monitor.getReceivedDate()) ? "" : sdf.format(monitor.getReceivedDate())));
            row.add(POIUtils.transferObj2Str(Objects.isNull(monitor.getEstimatedShelfDate()) ? "" : sdf.format(monitor.getEstimatedShelfDate())));
            row.add(POIUtils.transferObj2Str(Objects.isNull(monitor.getActualShelfDate()) ? "" : sdf.format(monitor.getActualShelfDate())));
            
            // 其他信息
            row.add(POIUtils.transferObj2Str(monitor.getWaitingDays()));
            // 状态信息（转换为中文名称，使用合并后的FBA状态）
            row.add(StringUtils.isBlank(monitor.getStatus()) ? "" :
                    Objects.isNull(FbaStatus.getNameByCode(monitor.getStatus())) ?
                            monitor.getStatus() : FbaStatus.getNameByCode(monitor.getStatus()));
            
            data.add(row);
        }
        return data;
    }
    
}