package com.estone.transfer.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved.
 * Project Name:wms
 * Package Name:com.estone.transfer.enums
 * File Name:SeasonType.java
 * Description:季节类型枚举
 * Author:Amoi
 * Date:2024-01-15
 * ---------------------------------------------------------------------------
 */
public enum SeasonType {
    OFF_SEASON(0, "淡季"),
    PEAK_SEASON(1, "旺季");

    private Integer code;
    private String name;

    SeasonType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        return Arrays.stream(values())
                .filter(type -> Objects.equals(type.code, code))
                .findFirst()
                .map(SeasonType::getName)
                .orElse(null);
    }
} 