package com.estone.transfer.bean;

import lombok.Data;
import java.util.List;

@Data
public class FbaUpMonitorQueryCondition extends FbaUpMonitor {
    private static final long serialVersionUID = 1;

    private Boolean readOnly = false;
    
    /**
     * 货件编号列表 - 支持批量查询
     */
    private List<String> shipmentIdList;
    
    /**
     * SKU代码列表 - 支持批量查询
     */
    private List<String> skuCodeList;
    
    /**
     * FNSKU列表 - 支持批量查询
     */
    private List<String> fnskuList;
    
    /**
     * 销售人员列表 - 支持多选
     */
    private List<String> salesPersonList;
    
    /**
     * 签收时间开始
     */
    private String receivedDateStart;
    
    /**
     * 签收时间结束
     */
    private String receivedDateEnd;
    
    /**
     * 创建时间开始
     */
    private String createdAtStart;
    
    /**
     * 创建时间结束
     */
    private String createdAtEnd;
    
    /**
     * 预警状态列表
     */
    private List<String> alertStatusList;

    /**
     * FBA状态列表（合并签收状态和上架状态）
     */
    private List<String> fbaStatusList;

    private List<Integer> idList;

}