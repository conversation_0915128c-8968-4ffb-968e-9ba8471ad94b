package com.estone.transfer.dao;

import java.util.List;

import com.estone.apv.bean.WhApvOutStockChain;
import com.estone.transfer.bean.*;
import com.whq.tool.component.Pager;

public interface WhFbaAllocationDao {
    int queryWhFbaAllocationCount(WhFbaAllocationQueryCondition query);

    List<WhFbaAllocation> queryWhFbaAllocationList();

    List<WhFbaAllocation> queryWhFbaAllocationList(WhFbaAllocationQueryCondition query, Pager pager);

    WhFbaAllocation queryWhFbaAllocation(Integer primaryKey);

    WhFbaAllocation queryWhFbaAllocation(WhFbaAllocationQueryCondition query);

    void createWhFbaAllocation(WhFbaAllocation entity);

    void batchCreateWhFbaAllocation(List<WhFbaAllocation> entityList);

    void batchUpdateWhFbaAllocation(List<WhFbaAllocation> entityList);

    void deleteWhFbaAllocation(Integer primaryKey);

    int updateWhFbaAllocation(WhFbaAllocation entity);

    int updateWhFbaAllocationByIdAndStatus(WhFbaAllocation entity,Integer status);

    int queryWhFbaAllocationAndItemCount(WhFbaAllocationQueryCondition query);

    List<WhFbaAllocation> queryWhFbaAllocationAndItems(WhFbaAllocationQueryCondition query, Pager pager);


    // 拣货查询
    List<WhFbaAllocation> queryAllocationByPickingTaskId(Integer taskId);

    int queryGridQuantity(Integer taskId);

    List<WhFbaAllocation> scanMaxPriorityGoodsPrintFba(WhFbaAllocationQueryCondition queryCondition);

    void clearMergeTimeByFbaId(List<Integer> fbaIdList);

    /**
     * 根据单号查询单号所在任务里所有未装箱单据数量
     *
     * @param fbaNo
     * @return
     */
    int unBoxCountInTask(String fbaNo);
    int unPackCountInTask(String fbaNo);


    List<WhApvOutStockChain> queryToDeleteList(String endDeliverDate);

    List<String> queryToDeletePocketCardList(String startTime, String endTime);

    void updateWhFbaAllocationByCancel(WhFbaAllocation whFbaAllocation, boolean b);

    int queryTransferOrderMegerCount(TransferOrderMegerDto query);

    List<TransferOrderMegerVo> queryTransferOrderMegers(TransferOrderMegerDto query, Pager page);
}