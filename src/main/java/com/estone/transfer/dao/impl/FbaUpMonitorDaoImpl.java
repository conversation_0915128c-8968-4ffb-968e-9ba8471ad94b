package com.estone.transfer.dao.impl;

import com.estone.common.util.CommonUtils;
import com.estone.common.util.SqlerTemplate;
import com.estone.transfer.bean.FbaUpMonitor;
import com.estone.transfer.bean.FbaUpMonitorQueryCondition;
import com.estone.transfer.dao.FbaUpMonitorDao;
import com.estone.transfer.dao.mapper.FbaUpMonitorDBField;
import com.estone.transfer.dao.mapper.FbaUpMonitorMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.List;

@Repository("fbaUpMonitorDao")
public class FbaUpMonitorDaoImpl implements FbaUpMonitorDao {

    private void setQueryCondition(SqlerRequest request, FbaUpMonitorQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(FbaUpMonitorDBField.ID, DataType.INT, query.getId());
        if (StringUtils.isNotBlank(query.getShipmentId()) && StringUtils.contains(query.getShipmentId(),",")) {
            query.setShipmentIdList(CommonUtils.splitList(query.getShipmentId(), ","));
            query.setShipmentId(null);
        }
        request.addDataParam(FbaUpMonitorDBField.SHIPMENT_ID, DataType.STRING, query.getShipmentId());
        request.addDataParam(FbaUpMonitorDBField.FBA_NO, DataType.STRING, query.getFbaNo());
        request.addDataParam(FbaUpMonitorDBField.DESTINATION_WAREHOUSE_ID, DataType.STRING, query.getDestinationWarehouseId());
        request.addDataParam(FbaUpMonitorDBField.SALES_PERSON, DataType.STRING, query.getSalesPerson());
        request.addDataParam(FbaUpMonitorDBField.STATUS, DataType.STRING, query.getStatus());
        request.addDataParam(FbaUpMonitorDBField.ALERT_STATUS, DataType.STRING, query.getAlertStatus());
        if (StringUtils.isNotBlank(query.getSkuCode()) && StringUtils.contains(query.getSkuCode(),",")) {
            query.setSkuCodeList(CommonUtils.splitList(query.getSkuCode(), ","));
            query.setSkuCode(null);
        }
        request.addDataParam(FbaUpMonitorDBField.SKU_CODE, DataType.STRING, query.getSkuCode());
        request.addDataParam(FbaUpMonitorDBField.FNSKU, DataType.STRING, query.getFnsku());
        request.addDataParam(FbaUpMonitorDBField.SELLER_SKU, DataType.STRING, query.getSellerSku());
        request.addDataParam(FbaUpMonitorDBField.WAITING_DAYS, DataType.INT, query.getWaitingDays());
        
        // 扩展查询条件
        if (CollectionUtils.isNotEmpty(query.getShipmentIdList())) {
            request.addDataParam("shipmentIdList", DataType.STRING, query.getShipmentIdList());
        }
        if (CollectionUtils.isNotEmpty(query.getSkuCodeList())) {
            request.addDataParam("skuCodeList", DataType.STRING, query.getSkuCodeList());
        }
        if (CollectionUtils.isNotEmpty(query.getFnskuList())) {
            request.addDataParam("fnskuList", DataType.STRING, query.getFnskuList());
        }
        if (CollectionUtils.isNotEmpty(query.getSalesPersonList())) {
            request.addDataParam("salesPersonList", DataType.STRING, query.getSalesPersonList());
        }
        if (CollectionUtils.isNotEmpty(query.getAlertStatusList())) {
            request.addDataParam("alertStatusList", DataType.STRING, query.getAlertStatusList());
        }
        // FBA状态列表查询（查询status字段）
        if (CollectionUtils.isNotEmpty(query.getFbaStatusList())) {
            request.addDataParam("fbaStatusList", DataType.STRING, query.getFbaStatusList());
        }
        if (CollectionUtils.isNotEmpty(query.getIdList())) {
            request.addDataParam("idList", DataType.INT, query.getIdList());
        }
        // 时间范围查询
        if (query.getReceivedDateStart() != null) {
            request.addDataParam("receivedDateStart", DataType.STRING, query.getReceivedDateStart());
        }
        if (query.getReceivedDateEnd() != null) {
            request.addDataParam("receivedDateEnd", DataType.STRING, query.getReceivedDateEnd());
        }
        if (query.getCreatedAtStart() != null) {
            request.addDataParam("createdAtStart", DataType.STRING, query.getCreatedAtStart());
        }
        if (query.getCreatedAtEnd() != null) {
            request.addDataParam("createdAtEnd", DataType.STRING, query.getCreatedAtEnd());
        }

    }

    @Override
    public int queryFbaUpMonitorCount(FbaUpMonitorQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryFbaUpMonitorCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<FbaUpMonitor> queryFbaUpMonitorList() {
        SqlerRequest request = new SqlerRequest("queryFbaUpMonitorList");
        return SqlerTemplate.query(request, new FbaUpMonitorMapper());
    }

    @Override
    public List<FbaUpMonitor> queryFbaUpMonitorList(FbaUpMonitorQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryFbaUpMonitorList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new FbaUpMonitorMapper());
    }

    @Override
    public FbaUpMonitor queryFbaUpMonitor(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryFbaUpMonitorByPrimaryKey");
        request.addDataParam(FbaUpMonitorDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new FbaUpMonitorMapper());
    }

    @Override
    public FbaUpMonitor queryFbaUpMonitor(FbaUpMonitorQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryFbaUpMonitor");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new FbaUpMonitorMapper());
    }

    @Override
    public void createFbaUpMonitor(FbaUpMonitor entity) {
        SqlerRequest request = new SqlerRequest("createFbaUpMonitor");
        request.addDataParam(FbaUpMonitorDBField.SHIPMENT_ID, DataType.STRING, entity.getShipmentId());
        request.addDataParam(FbaUpMonitorDBField.FBA_NO, DataType.STRING, entity.getFbaNo());
        request.addDataParam(FbaUpMonitorDBField.DESTINATION_WAREHOUSE_ID, DataType.STRING, entity.getDestinationWarehouseId());
        request.addDataParam(FbaUpMonitorDBField.SALES_PERSON, DataType.STRING, entity.getSalesPerson());
        request.addDataParam(FbaUpMonitorDBField.TOTAL_QUANTITY, DataType.INT, entity.getTotalQuantity());
        request.addDataParam(FbaUpMonitorDBField.RECEIVED_QUANTITY, DataType.INT, entity.getReceivedQuantity());
        request.addDataParam(FbaUpMonitorDBField.DIFFERENCE_QUANTITY, DataType.INT, entity.getDifferenceQuantity());
        request.addDataParam(FbaUpMonitorDBField.SHIPPING_DATE, DataType.TIMESTAMP, entity.getShippingDate());
        request.addDataParam(FbaUpMonitorDBField.RECEIVED_DATE, DataType.TIMESTAMP, entity.getReceivedDate());
        request.addDataParam(FbaUpMonitorDBField.ESTIMATED_SHELF_DATE, DataType.TIMESTAMP, entity.getEstimatedShelfDate());
        request.addDataParam(FbaUpMonitorDBField.ACTUAL_SHELF_DATE, DataType.TIMESTAMP, entity.getActualShelfDate());
        request.addDataParam(FbaUpMonitorDBField.WAITING_DAYS, DataType.INT, entity.getWaitingDays());
        request.addDataParam(FbaUpMonitorDBField.STATUS, DataType.STRING, entity.getStatus());
        request.addDataParam(FbaUpMonitorDBField.ALERT_STATUS, DataType.STRING, entity.getAlertStatus());
        request.addDataParam(FbaUpMonitorDBField.SKU_CODE, DataType.STRING, entity.getSkuCode());
        request.addDataParam(FbaUpMonitorDBField.FNSKU, DataType.STRING, entity.getFnsku());
        request.addDataParam(FbaUpMonitorDBField.SELLER_SKU, DataType.STRING, entity.getSellerSku());
        request.addDataParam(FbaUpMonitorDBField.CREATED_AT, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(FbaUpMonitorDBField.UPDATED_AT, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateFbaUpMonitor(FbaUpMonitor entity) {
        SqlerRequest request = new SqlerRequest("updateFbaUpMonitorByPrimaryKey");
        request.addDataParam(FbaUpMonitorDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(FbaUpMonitorDBField.SHIPMENT_ID, DataType.STRING, entity.getShipmentId());
        request.addDataParam(FbaUpMonitorDBField.FBA_NO, DataType.STRING, entity.getFbaNo());
        request.addDataParam(FbaUpMonitorDBField.DESTINATION_WAREHOUSE_ID, DataType.STRING, entity.getDestinationWarehouseId());
        request.addDataParam(FbaUpMonitorDBField.SALES_PERSON, DataType.STRING, entity.getSalesPerson());
        request.addDataParam(FbaUpMonitorDBField.TOTAL_QUANTITY, DataType.INT, entity.getTotalQuantity());
        request.addDataParam(FbaUpMonitorDBField.RECEIVED_QUANTITY, DataType.INT, entity.getReceivedQuantity());
        request.addDataParam(FbaUpMonitorDBField.DIFFERENCE_QUANTITY, DataType.INT, entity.getDifferenceQuantity());
        request.addDataParam(FbaUpMonitorDBField.SHIPPING_DATE, DataType.TIMESTAMP, entity.getShippingDate());
        request.addDataParam(FbaUpMonitorDBField.RECEIVED_DATE, DataType.TIMESTAMP, entity.getReceivedDate());
        request.addDataParam(FbaUpMonitorDBField.ESTIMATED_SHELF_DATE, DataType.TIMESTAMP, entity.getEstimatedShelfDate());
        request.addDataParam(FbaUpMonitorDBField.ACTUAL_SHELF_DATE, DataType.TIMESTAMP, entity.getActualShelfDate());
        request.addDataParam(FbaUpMonitorDBField.WAITING_DAYS, DataType.INT, entity.getWaitingDays());
        request.addDataParam(FbaUpMonitorDBField.STATUS, DataType.STRING, entity.getStatus());
        request.addDataParam(FbaUpMonitorDBField.ALERT_STATUS, DataType.STRING, entity.getAlertStatus());
        request.addDataParam(FbaUpMonitorDBField.SKU_CODE, DataType.STRING, entity.getSkuCode());
        request.addDataParam(FbaUpMonitorDBField.FNSKU, DataType.STRING, entity.getFnsku());
        request.addDataParam(FbaUpMonitorDBField.SELLER_SKU, DataType.STRING, entity.getSellerSku());
        request.addDataParam(FbaUpMonitorDBField.CREATED_AT, DataType.TIMESTAMP, entity.getCreatedAt());
        request.addDataParam(FbaUpMonitorDBField.UPDATED_AT, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateFbaUpMonitor(List<FbaUpMonitor> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createFbaUpMonitor");
            for (FbaUpMonitor entity : entityList) {
                request.addBatchDataParam(FbaUpMonitorDBField.SHIPMENT_ID, DataType.STRING, entity.getShipmentId());
                request.addBatchDataParam(FbaUpMonitorDBField.FBA_NO, DataType.STRING, entity.getFbaNo());
                request.addBatchDataParam(FbaUpMonitorDBField.DESTINATION_WAREHOUSE_ID, DataType.STRING, entity.getDestinationWarehouseId());
                request.addBatchDataParam(FbaUpMonitorDBField.SALES_PERSON, DataType.STRING, entity.getSalesPerson());
                request.addBatchDataParam(FbaUpMonitorDBField.TOTAL_QUANTITY, DataType.INT, entity.getTotalQuantity());
                request.addBatchDataParam(FbaUpMonitorDBField.RECEIVED_QUANTITY, DataType.INT, entity.getReceivedQuantity());
                request.addBatchDataParam(FbaUpMonitorDBField.DIFFERENCE_QUANTITY, DataType.INT, entity.getDifferenceQuantity());
                request.addBatchDataParam(FbaUpMonitorDBField.SHIPPING_DATE, DataType.TIMESTAMP, entity.getShippingDate());
                request.addBatchDataParam(FbaUpMonitorDBField.RECEIVED_DATE, DataType.TIMESTAMP, entity.getReceivedDate());
                request.addBatchDataParam(FbaUpMonitorDBField.ESTIMATED_SHELF_DATE, DataType.TIMESTAMP, entity.getEstimatedShelfDate());
                request.addBatchDataParam(FbaUpMonitorDBField.ACTUAL_SHELF_DATE, DataType.TIMESTAMP, entity.getActualShelfDate());
                request.addBatchDataParam(FbaUpMonitorDBField.WAITING_DAYS, DataType.INT, entity.getWaitingDays());
                request.addBatchDataParam(FbaUpMonitorDBField.STATUS, DataType.STRING, entity.getStatus());
                request.addBatchDataParam(FbaUpMonitorDBField.ALERT_STATUS, DataType.STRING, entity.getAlertStatus());
                request.addBatchDataParam(FbaUpMonitorDBField.SKU_CODE, DataType.STRING, entity.getSkuCode());
                request.addBatchDataParam(FbaUpMonitorDBField.FNSKU, DataType.STRING, entity.getFnsku());
                request.addBatchDataParam(FbaUpMonitorDBField.SELLER_SKU, DataType.STRING, entity.getSellerSku());
                request.addBatchDataParam(FbaUpMonitorDBField.CREATED_AT, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(FbaUpMonitorDBField.UPDATED_AT, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateFbaUpMonitor(List<FbaUpMonitor> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateFbaUpMonitorByPrimaryKey");
            for (FbaUpMonitor entity : entityList) {
                request.addBatchDataParam(FbaUpMonitorDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(FbaUpMonitorDBField.SHIPMENT_ID, DataType.STRING, entity.getShipmentId());
                request.addBatchDataParam(FbaUpMonitorDBField.FBA_NO, DataType.STRING, entity.getFbaNo());
                request.addBatchDataParam(FbaUpMonitorDBField.DESTINATION_WAREHOUSE_ID, DataType.STRING, entity.getDestinationWarehouseId());
                request.addBatchDataParam(FbaUpMonitorDBField.SALES_PERSON, DataType.STRING, entity.getSalesPerson());
                request.addBatchDataParam(FbaUpMonitorDBField.TOTAL_QUANTITY, DataType.INT, entity.getTotalQuantity());
                request.addBatchDataParam(FbaUpMonitorDBField.RECEIVED_QUANTITY, DataType.INT, entity.getReceivedQuantity());
                request.addBatchDataParam(FbaUpMonitorDBField.DIFFERENCE_QUANTITY, DataType.INT, entity.getDifferenceQuantity());
                request.addBatchDataParam(FbaUpMonitorDBField.SHIPPING_DATE, DataType.TIMESTAMP, entity.getShippingDate());
                request.addBatchDataParam(FbaUpMonitorDBField.RECEIVED_DATE, DataType.TIMESTAMP, entity.getReceivedDate());
                request.addBatchDataParam(FbaUpMonitorDBField.ESTIMATED_SHELF_DATE, DataType.TIMESTAMP, entity.getEstimatedShelfDate());
                request.addBatchDataParam(FbaUpMonitorDBField.ACTUAL_SHELF_DATE, DataType.TIMESTAMP, entity.getActualShelfDate());
                request.addBatchDataParam(FbaUpMonitorDBField.WAITING_DAYS, DataType.INT, entity.getWaitingDays());
                request.addBatchDataParam(FbaUpMonitorDBField.STATUS, DataType.STRING, entity.getStatus());
                request.addBatchDataParam(FbaUpMonitorDBField.ALERT_STATUS, DataType.STRING, entity.getAlertStatus());
                request.addBatchDataParam(FbaUpMonitorDBField.SKU_CODE, DataType.STRING, entity.getSkuCode());
                request.addBatchDataParam(FbaUpMonitorDBField.FNSKU, DataType.STRING, entity.getFnsku());
                request.addBatchDataParam(FbaUpMonitorDBField.SELLER_SKU, DataType.STRING, entity.getSellerSku());
                request.addBatchDataParam(FbaUpMonitorDBField.CREATED_AT, DataType.TIMESTAMP, entity.getCreatedAt());
                request.addBatchDataParam(FbaUpMonitorDBField.UPDATED_AT, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteFbaUpMonitor(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteFbaUpMonitorByPrimaryKey");
        request.addDataParam(FbaUpMonitorDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}