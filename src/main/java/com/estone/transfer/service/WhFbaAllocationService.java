package com.estone.transfer.service;

import java.util.Date;
import java.util.List;

import com.estone.apv.domain.WhApvGoodsDo;
import com.estone.asn.bean.WhAsnExtra;
import com.estone.foreign.bean.PushOmsPacData;
import com.estone.scan.deliver.bean.WhScanShipment;
import com.estone.transfer.bean.*;
import com.global.iop.util.ApiException;
import com.estone.transfer.bean.*;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;
import com.estone.common.util.model.ApiResult;
import com.estone.transfer.bean.FbaDeliverRequest;

public interface WhFbaAllocationService {
    List<WhFbaAllocation> queryAllWhFbaAllocations();

    List<WhFbaAllocation> queryWhFbaAllocations(WhFbaAllocationQueryCondition query, Pager pager);

    WhFbaAllocation getWhFbaAllocation(Integer id);

    WhFbaAllocation getWhFbaAllocationDetail(Integer id);

    WhFbaAllocation queryWhFbaAllocation(WhFbaAllocationQueryCondition query);

    void createWhFbaAllocation(WhFbaAllocation whFbaAllocation);

    void batchCreateWhFbaAllocation(List<WhFbaAllocation> entityList);

    void deleteWhFbaAllocation(Integer id);

    int updateWhFbaAllocation(WhFbaAllocation whFbaAllocation);

    void batchUpdateWhFbaAllocation(List<WhFbaAllocation> entityList);

    /**
     * 推送FBA待分播确认状态到OMS
     * @param whFbaAllocations
     */
    void pushFbaWaitGridStatusToOMS(List<WhFbaAllocation> whFbaAllocations);

    /**
     * 查询条目和明细
     *
     * @param query
     * @param page
     * @return
     */
    List<WhFbaAllocation> queryWhFbaAllocationAndItems(WhFbaAllocationQueryCondition query, Pager page);

    /**
     * 根据拣货任务id查询订单
     * @param taskId
     * @return
     */
    List<WhFbaAllocation> queryAllocationByPickingTaskId(Integer taskId);

    String doGenerateFbaAllocation(WhFbaAllocation whFbaAllocation);

    void downloadJitSkuPdf(WhAsnExtra whAsnExtra, WhFbaAllocation whFbaAllocation, boolean downloadMd);

    /**
     * 更新shein订单的追踪单号信息
     * @param whFbaAllocation
     * @return 出错时会返回错误信息，否则返回null
     */
    String doUpdateFbaAllocation(WhFbaAllocation whFbaAllocation);

    /**
     * 更新标签信息
     * @param whFbaAllocation
     * @return
     */
    String doUpdateTags(WhFbaAllocation whFbaAllocation);

    void doGenAllocationDemand(List<String> lockList,List<WhFbaAllocationData> allocationDatas) throws Exception;

    /**
     * 推送消息到OMS
     *
     * @param allocation
     */
    void sendMsg(WhFbaAllocation allocation);

    /**
     * 推送消息到TMS
     *
     * @param allocation
     * @param type 1.推送装箱信息，2.推送发货状态
     */
    void sendMsgToTms(WhFbaAllocation allocation, Integer type);

    /**
     * 交运
     *
     * @param allocation
     * @return
     */
    ResponseJson doDeliver(String fbaNo);

    ResponseJson doDeliver(List<WhFbaAllocation> allocations);

    ResponseJson doDeliverRetry(List<WhFbaAllocation> allocations, Date deliverDate);

    void doCancelFbaAllocation(WhFbaAllocation whFbaAllocation) throws Exception;

    void uploadBoxInfoComplete(WhFbaAllocation whFbaAllocation);

    ResponseJson doLoad(List<WhFbaAllocation> whFbaAllocations);

    ResponseJson doAsnLoad(List<WhFbaAllocation> whFbaAllocations);

    void createOnWayOrderAndItem(List<WhFbaAllocation> allocations);

    void syncTmsWarningInfo(WhFbaAllocation allocation, OnWayOrder onWayOrder, List<Integer> itemIdList);

    /**
     * 更新海外仓上架数量
     *
     * @param query
     */
    void updateOverseasUpQuantity(WhFbaAllocationQueryCondition query, Boolean isAuto);

    List<WhFbaAllocation> updateWhFbaAllocationStatus(List<Integer> ids, Integer code, String logContent);

    /**
     *fba部分sku取消、海外仓部分sku取消
     */
     ResponseJson doCancelPortionFbaSku(WhFbaAllocation allocation);

    /**
     * 通知FBA订单待播种确认状态到待播种状态
     * @param allocations 要进行状态修改的订单
     * @return 错误信息
     */
     String notifyFbaToGrid(List<WhFbaAllocation> allocations);

     /**
      * 根据任务号查询播种数量
      *
      * @param taskId
      * @return
      */
     int queryGridQuantity(Integer taskId);

     /**
      * 仓库审核
      * 
      * @param id
      * @param allocationStr
      * @return
      */
     ResponseJson doCheckLabel(Integer id, String allocationStr);

    ResponseJson doLoad(String fbaNo);

    WhFbaAllocation scanMaxPriorityGoodsPrintFba(WhFbaAllocationQueryCondition queryCondition, WhApvGoodsDo domain);

    void revokeScanedWhApvByStatus(Integer apvId, Integer integer);

    void passBasketScan(Integer apvId);

    void passMoreProductScan(WhFbaAllocation whFbaAllocation);

    /**
     * 根据单号查询单号所在任务里所有未装箱单据数量
     *
     * @param fbaNo
     * @return
     */
    int unBoxCountInTask(String fbaNo);
    int unPackCountInTask(String fbaNo);

    void deleteTransferDeliverFbaPdf(String startTime, String endTime);

    ResponseJson importTransferReturnOrder(List<String> trackingNumber);

    void pushTransferReturnOrder(List<PushOmsPacData> omsPacDataList);

    String jitCreateCoOrder(List<WhFbaAllocation> allocationList);

    String jitCreateCoOrderv2(List<Integer> ids);

    String jitCreateCoOrder(WhFbaAllocation allocation, boolean otherSuccess) throws ApiException;

    void syncWhFbaAllocationPickupOrderNo(String orderNos, Boolean updateStatusFlag);

    List<String> doUndoIntoCar(WhFbaAllocation whFbaAllocation, List<WhFbaAllocationItem> whFbaAllocationItemList, String lbxNo,String expressCompany);

    List<WarehouseLoadingSummary> queryTodayIntoCarTotal(Integer loadBy);

    String cancelJitOrder(WhFbaAllocation whFbaAllocation);

    String cancelJitCoOrder(WhFbaAllocation whFbaAllocation);

    String cancelJitPickOrder(WhFbaAllocation whFbaAllocation);

/*    String createPickupOrderV2(List<Integer> ids);*/

    void doSubmitBoxInformation(String fbaNo, List<WhFbaAllocationItem> whFbaAllocationItemList);

    void doSubmitFnSkuInformation(WhFbaAllocationItem item);

    void doFinishFbaBox(String fbaNo);

    String syncCreatePickupOrderV3(List<WhFbaAllocation> fbaAllocations, WhScanShipment scanShipment, Integer retry);

    boolean syncJitFbaPickupOrderNos(String fbaNo);

    List<TransferOrderMegerVo> queryTransferOrderMegers(TransferOrderMegerDto query, Pager page);
}