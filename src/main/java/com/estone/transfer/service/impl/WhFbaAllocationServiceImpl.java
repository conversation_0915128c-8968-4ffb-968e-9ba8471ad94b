package com.estone.transfer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.allocation.bean.WhApvAllocationDemand;
import com.estone.allocation.bean.WhApvAllocationDemandItem;
import com.estone.allocation.bean.WhApvAllocationDemandSku;
import com.estone.allocation.dao.impl.WhApvAllocationDemandDaoImpl;
import com.estone.allocation.enums.AllocationDemandApvStatus;
import com.estone.allocation.enums.AllocationDemandTaskStatus;
import com.estone.allocation.enums.AllocationDemandTaskType;
import com.estone.android.PdaExceptionCode;
import com.estone.apv.bean.*;
import com.estone.apv.common.ApvStatus;
import com.estone.apv.common.ApvTaskRedisLock;
import com.estone.apv.domain.WhApvGoodsDo;
import com.estone.apv.enums.ApvExpressStatus;
import com.estone.apv.enums.ApvTaxTypeEnum;
import com.estone.apv.service.*;
import com.estone.apv.util.ApvPackUtils;
import com.estone.asn.bean.WhAsn;
import com.estone.asn.bean.WhAsnExtra;
import com.estone.asn.bean.WhAsnExtraQueryCondition;
import com.estone.asn.enums.AsnPackageMethodEnum;
import com.estone.asn.enums.AsnStatus;
import com.estone.asn.enums.OrderType;
import com.estone.asn.service.WhAsnExtraService;
import com.estone.checkin.enums.ShippingCompanyEnum;
import com.estone.checkin.utils.GetSkuQcCategoryDescUtil;
import com.estone.common.SaleChannel;
import com.estone.common.enums.CountryEnum;
import com.estone.common.enums.LogModule;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.core.mq.Queues;
import com.estone.core.mq.RabbitMqExchange;
import com.estone.foreign.bean.PushOmsPacData;
import com.estone.foreign.bean.TmsWarningDayDTO;
import com.estone.pac.bean.TakeStockDTO;
import com.estone.pac.enums.RecordSourceEnum;
import com.estone.pac.enums.TakeStockReasonEnum;
import com.estone.pac.service.TakeStockRecordService;
import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.bean.WhPickingTaskQueryCondition;
import com.estone.picking.bean.WhPickingTaskSku;
import com.estone.picking.bean.WhPickingTaskSkuQueryCondition;
import com.estone.picking.enums.PickTaskGridStatus;
import com.estone.picking.enums.PickingTaskSkuStatus;
import com.estone.picking.enums.PickingTaskStatus;
import com.estone.picking.enums.PickingTaskWarehouseType;
import com.estone.picking.service.WhPickingTaskService;
import com.estone.picking.service.WhPickingTaskSkuService;
import com.estone.scan.deliver.bean.WhScanShipment;
import com.estone.scan.deliver.bean.WhScanShipmentQueryCondition;
import com.estone.scan.deliver.service.WhScanShipmentService;
import com.estone.sku.bean.*;
import com.estone.sku.enums.UniqueSkuStep;
import com.estone.sku.service.*;
import com.estone.statistics.bean.WhAssetChangeItem;
import com.estone.statistics.bean.WhAssetChangeItemQueryCondition;
import com.estone.statistics.bean.WhDrpTurnoverItme;
import com.estone.statistics.bean.WhDrpTurnoverItmeQueryCondition;
import com.estone.statistics.enums.AssetOrderType;
import com.estone.statistics.enums.DrpTurnoverInventoryType;
import com.estone.statistics.enums.DrpTurnoverOderType;
import com.estone.statistics.service.WhAssetChangeItemService;
import com.estone.statistics.service.WhDrpTurnoverItmeService;
import com.estone.system.param.bean.SystemParam;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.common.AmqMessageModuleName;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.estone.temu.enums.CancelTypeEnum;
import com.estone.transfer.bean.*;
import com.estone.transfer.dao.JitPickBoxDao;
import com.estone.transfer.dao.JitPickBoxItemDao;
import com.estone.transfer.dao.WhFbaAllocationDao;
import com.estone.transfer.enums.*;
import com.estone.transfer.response.CreatCoOrderResponse;
import com.estone.transfer.service.*;
import com.estone.transfer.utils.TmsUtils;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.WhAllocateRecord;
import com.estone.warehouse.bean.WhAllocateRecordItem;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.enums.StockLogStep;
import com.estone.warehouse.enums.WhAllocateInputTypeEnum;
import com.estone.warehouse.enums.WhAllocateTypeEnum;
import com.estone.warehouse.service.WhAllocateRecordService;
import com.estone.warehouse.service.WhStockService;
import com.global.iop.util.ApiException;
import com.google.common.collect.Lists;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import jodd.util.ArraysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RDelayedQueue;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service("whFbaAllocationService")
@Slf4j
public class WhFbaAllocationServiceImpl implements WhFbaAllocationService {
    @Resource
    private WhFbaAllocationDao whFbaAllocationDao;

    @Resource
    private WhFbaAllocationItemService whFbaAllocationItemService;

    @Resource
    private WhAsnExtraService whAsnExtraService;

    @Resource
    private WhFbaAllocationDataService whFbaAllocationDataService;

    @Resource
    private WhFbaUpdateStockService whFbaUpdateStockService;

    @Resource
    private TransferStockService transferStockService;

    @Resource
    private AmqMessageService amqMessageService;

    @Resource
    private WhFbaAllocationStockService whFbaAllocationStockService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private WhApvAllocationDemandDaoImpl whApvAllocationDemandDaoImpl;

    @Resource
    private TransferStockRelationService transferStockRelationService;

    @Resource
    private WhFbaAllocationHandleService whFbaAllocationHandleService;

    @Resource
    private WhStockService whStockService;

    @Resource
    private WhAssetChangeItemService whAssetChangeItemService;

    @Resource
    private WhAllocateRecordService whAllocateRecordService;

    @Resource
    private WhSkuExtendService whSkuExtendService;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private WhTransitStockLogService stockLogService;

    @Resource
    private WhPickingTaskService whPickingTaskService;

    @Resource
    private WhPickingTaskSkuService whPickingTaskSkuService;

    @Resource
    private WhApvGridService whApvGridService;

    @Resource
    private WhApvGridItemService whApvGridItemService;

    @Resource
    private TransferStockCountService transferStockCountService;

    @Resource
    private WhFbaChangeService whFbaChangeService;

    @Resource
    private ExpManageService expManageService;

    @Resource
    private TakeStockRecordService takeStockRecordService;

    @Resource
    private AmqpTemplate amqpTemplate;

    @Resource
    private OnWayOrderService onWayOrderService;

    @Resource
    private OnWayOrderItemService onWayOrderItemService;

    @Resource
    private WhApvOutStockChainService whApvOutStockChainService;

    @Resource
    private TransitBatchHandleService transitBatchHandleService;

    @Resource
    private WhUniqueSkuLogService whUniqueSkuLogService;

    @Resource
    private ApvStatusUpdateService apvStatusUpdateService;

    @Resource
    private WhApvService whApvService;

    @Resource
    private ApvExpressService apvExpressService;

    @Resource
    private WhDrpTurnoverItmeService whDrpTurnoverItmeService;

    @Resource
    private JitPickBoxDao jitPickBoxDao;
    
    @Resource
    private JitPickBoxItemDao jitPickBoxItemDao;

    @Resource
    private AsnPickBoxService asnPickBoxService;

    @Resource
    private SkuTagInfoService skuTagInfoService;

    @Resource
    private WhScanShipmentService scanShipmentService;

    @Resource
    private AliExpressCallService aliExpressCallService;

    @Resource(name = "smtQueryOrderRDelayedQueue")
    private RDelayedQueue<String> rDelayedQueue;

    @Resource
    private ApvTrackService apvTrackService;

    @Resource
    private WhFbaShipmentService whFbaShipmentService;

    @Resource
    private JitPickupOrderService jitPickupOrderService;

    @Resource
    private PackExceptionUuidItemService packExceptionUuidItemService;

    @Resource
    private WarehouseSelfDeliveryConfigService warehouseSelfDeliveryConfigService;



    @Resource
    private ApvWaybillService apvWaybillService;
    private final static ExecutorService executors = Executors.newFixedThreadPool(20);
    private final static ExecutorService executorServices = ExecutorUtils.newFixedThreadPool(10);

    final static SystemLogUtils SCANSHIPMENTLOG = SystemLogUtils.create(LogModule.SCANSHIPMENT.getCode());

    public final Integer SMT_RETRY_NUM = 10;

    private final static String TRANSFER_RETURN_ORDER_PUSH_FAIL_KEY = "TRANSFER_RETURN_ORDER_PUSH_FAIL_KEY";

    @Override
    public WhFbaAllocation getWhFbaAllocation(Integer id) {
        WhFbaAllocation whFbaAllocation = whFbaAllocationDao.queryWhFbaAllocation(id);
        return whFbaAllocation;
    }

    @Override
    public WhFbaAllocation getWhFbaAllocationDetail(Integer id) {
        WhFbaAllocation whFbaAllocation = whFbaAllocationDao.queryWhFbaAllocation(id);
        // 关联查询
        return whFbaAllocation;
    }

    @Override
    public WhFbaAllocation queryWhFbaAllocation(WhFbaAllocationQueryCondition query) {
        Assert.notNull(query, "query is null!");
        WhFbaAllocation whFbaAllocation = whFbaAllocationDao.queryWhFbaAllocation(query);
        return whFbaAllocation;
    }

    @Override
    public List<WhFbaAllocation> queryAllWhFbaAllocations() {
        return whFbaAllocationDao.queryWhFbaAllocationList();
    }

    @Override
    public List<WhFbaAllocation> queryWhFbaAllocations(WhFbaAllocationQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = whFbaAllocationDao.queryWhFbaAllocationCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhFbaAllocation>();
            }
        }
        List<WhFbaAllocation> whFbaAllocations = whFbaAllocationDao.queryWhFbaAllocationList(query, pager);
        return whFbaAllocations;
    }

    @Override
    public void createWhFbaAllocation(WhFbaAllocation whFbaAllocation) {
        try {
            whFbaAllocationDao.createWhFbaAllocation(whFbaAllocation);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateWhFbaAllocation(List<WhFbaAllocation> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whFbaAllocationDao.batchCreateWhFbaAllocation(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteWhFbaAllocation(Integer id) {
        try {
            whFbaAllocationDao.deleteWhFbaAllocation(id);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public int updateWhFbaAllocation(WhFbaAllocation whFbaAllocation) {
        try {
            return whFbaAllocationDao.updateWhFbaAllocation(whFbaAllocation);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateWhFbaAllocation(List<WhFbaAllocation> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whFbaAllocationDao.batchUpdateWhFbaAllocation(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public List<WhFbaAllocation> queryWhFbaAllocationAndItems(WhFbaAllocationQueryCondition query, Pager page) {
        Assert.notNull(query, "query is null!");
        if (page != null && page.isQueryCount()) {
            int count = whFbaAllocationDao.queryWhFbaAllocationAndItemCount(query);
            page.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhFbaAllocation>();
            }
        }
        List<WhFbaAllocation> whFbaAllocations = whFbaAllocationDao.queryWhFbaAllocationAndItems(query, page);
        return whFbaAllocations;
    }

    @Override
    public List<WhFbaAllocation> queryAllocationByPickingTaskId(Integer taskId) {
        return whFbaAllocationDao.queryAllocationByPickingTaskId(taskId);
    }

    /**
     * fba的单，拣货完成进入待播种状态时，其推送给OMS进行确认
     * @param whFbaAllocations 要进行推送的单
     */
    @Override
    public void pushFbaWaitGridStatusToOMS(List<WhFbaAllocation> whFbaAllocations){
        if (CollectionUtils.isEmpty(whFbaAllocations)){
            return;
        }
        try {
            SystemParam systemParam = CacheUtils.SystemParamGet("OMS_PARAM.PUSH_FBA_WAIT_GRID_STATUS");
            if (null == systemParam) {
                log.error("systemParam OMS_PARAM.PUSH_FBA_WAIT_GRID_STATUS is null");
                return;
            }
            String url = systemParam.getParamValue();
            if (StringUtils.isBlank(url)) {
                log.error("systemParam OMS_PARAM.PUSH_FBA_WAIT_GRID_STATUS paramValue is null");
                return;
            }
            List<String> fbaNoList = whFbaAllocations.stream().map(WhFbaAllocation::getFbaNo).collect(Collectors.toList());
            log.info("推送订单系统待分播确认订单信息，调用接口参数：" + JSON.toJSONString(fbaNoList));
            ApiResult apiResult = HttpUtils.post(url, HttpUtils.ACCESS_TOKEN, fbaNoList, ApiResult.class);
            log.info("推送订单系统待分播确认订单信息，接口返回结果：" + JSON.toJSONString(apiResult));
            if (Objects.isNull(apiResult) || !apiResult.isSuccess()) {
                log.error("推送订单系统待分播确认订单信息，调用接口失败!" + (Objects.nonNull(apiResult) ? apiResult.getErrorMsg() : ""));
                whFbaAllocations.forEach(f -> SystemLogUtils.FBAALLOCATIONLOG.log(f.getId(), "推送OMS待播种确认状态失败!"));
                return;
            }
            whFbaAllocations.forEach(f -> SystemLogUtils.FBAALLOCATIONLOG.log(f.getId(), "推送OMS待播种确认状态成功!"));
        }catch (Exception e){
            log.error("推送订单系统待分播确认订单信息，调用接口失败!", e);
        }
    }


    @Override
    @StockServicelock
    public String doGenerateFbaAllocation(WhFbaAllocation whFbaAllocation) {
        //销售人员
        WhFbaAllocation receivedWhFbaAllocation = whFbaAllocation;
        String salesperson = whFbaAllocation.getSalesperson();
        boolean isFba = whFbaAllocation.isFba();
        boolean isShein = whFbaAllocation.isShein();
        boolean isTransferOrder = whFbaAllocation.isTransfer();
        String amazonSite = null;
        if (isFba && StringUtils.substring(whFbaAllocation.getAccountNumber(), 2, 3).equals("-")) {
            amazonSite = StringUtils.substring(whFbaAllocation.getAccountNumber(), 0, 2);
        }
        log.info("doGenerateFbaAllocation ShipmentId:" + JSONObject.toJSONString(whFbaAllocation));
        WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
        queryCondition.setIsAmazonFba(isFba);
        queryCondition.setIsTransferOrder(isTransferOrder);
        queryCondition.setQueryWhAsnExtra(true);
        if (isFba) {
            if (StringUtils.isNotBlank(whFbaAllocation.getPlanNo())) {
                whFbaAllocation.setShipmentId(whFbaAllocation.getPlanNo());
            }
            whFbaAllocation.setSite(null);
            queryCondition.setFbaNo(whFbaAllocation.getFbaNo());
        } else {
            queryCondition.setFbaNo(whFbaAllocation.getFbaNo());
        }
        List<WhFbaAllocation> whFbaAllocationList = queryWhFbaAllocationAndItems(queryCondition, null);
        if (CollectionUtils.isNotEmpty(whFbaAllocationList)) {
            if (whFbaAllocationList.get(0).getStatus() > AsnPrepareStatus.ALLOTING.intCode()) {
                return String.format("单号【%s】已存在！", whFbaAllocation.getShipmentId());
            }
            whFbaAllocation = whFbaAllocationList.get(0);//setId(whFbaAllocationList.get(0).getId());
        }
        whFbaAllocation.setSalesperson(salesperson);
        List<WhFbaAllocationItem> items = whFbaAllocation.getItems();

        whFbaAllocation.setAccountNumber(whFbaAllocation.getUnprefixedAccountNumber());
        //分配库存条件
        for (WhFbaAllocationItem item : items) {
            item.setSite(whFbaAllocation.getSite());
            item.setStore(whFbaAllocation.getAccountNumber());
            item.setSkuQuantity(item.getSkuQuantity() == null ? 0 : item.getSkuQuantity());
        }
        //分配库存
        Map<String, TransferStock> stockMap = whFbaAllocationStockService.getStockMap(whFbaAllocation);
        // jit的无库存记录，返回无库存记录的sku
        String allotError = null;
        if(isTransferOrder && !isShein)
            allotError = transferStockCheck(whFbaAllocation, stockMap);
        if (StringUtils.isNotBlank(allotError)) return allotError;
        // 分配库存
        allotError = whFbaAllocationHandleService.doAllot(whFbaAllocation, stockMap);
        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setSkus(items.stream().map(WhFbaAllocationItem::getProductSku).distinct().collect(Collectors.toList()));
        List<WhStock> stocks = whStockService.queryWhStocks(query, null);
        Map<String, List<WhStock>> skuMap = Optional.ofNullable(stocks).orElse(new ArrayList<>()).stream()
                .collect(Collectors.groupingBy(s -> s.getSku()));

        TransferStockRelationQueryCondition relationQueryCondition = new TransferStockRelationQueryCondition();
        relationQueryCondition.setSkuList(new ArrayList<>(skuMap.keySet()));
        relationQueryCondition.setStore(whFbaAllocation.getAccountNumber());
        List<TransferStockRelation> transferStockRelationList = transferStockRelationService
                .queryTransferStockRelations(relationQueryCondition, null);
        Map<String, List<TransferStockRelation>> relationMap = Optional.ofNullable(transferStockRelationList)
                .orElse(new ArrayList<>()).stream().collect(Collectors
                        .groupingBy(s -> (s.getSku() + s.getFnSku() + s.getSellSku() + s.getStore()).toUpperCase()));
        // 分配失败，校验是否生成库存记录
        List<TransferStockCount> stockCounts = new ArrayList();
        List<TransferStock> createTransferStockList = new ArrayList();
        List<TransferStockRelation> createList = new ArrayList<>();
        for (WhFbaAllocationItem item : items) {
            if (!isFba)
                item.setFnSku(item.getProductSku());
            // 生成FNSKU映射记录
            String relationKey = (item.getProductSku() + item.getFnSku() + item.getSellSku() + item.getStore())
                    .toUpperCase();
            if (MapUtils.isEmpty(relationMap) || !relationMap.containsKey(relationKey)) {
                TransferStockRelation transferStockRelation = new TransferStockRelation();
                if (item.getSuitFlag() == null) {
                    transferStockRelation.setFlag(0);
                }
                else {
                    transferStockRelation.setFlag(item.getSuitFlag());
                }
                transferStockRelation.setFnSku(item.getFnSku());
                transferStockRelation.setSellSku(item.getSellSku());
                transferStockRelation.setSite(whFbaAllocation.getSite());
                transferStockRelation.setSku(item.getProductSku());
                transferStockRelation.setStore(whFbaAllocation.getAccountNumber());
                createList.add(transferStockRelation);
            }
            if (StringUtils.isBlank(allotError)) continue;
            // 操作中转仓库存统计
            TransferStockQueryCondition queryCount = new TransferStockQueryCondition();
            queryCount.setSku(item.getProductSku());
            if (transferStockCountService.queryTransferStockCountCount(queryCount) == 0) {
                WhSkuQueryCondition whSkuQueryCondition = new WhSkuQueryCondition();
                whSkuQueryCondition.setSku(item.getProductSku());
                WhSku whSku = whSkuService.queryWhSku(whSkuQueryCondition);
                // 入库时没有对应的sku库存
                TransferStockCount stockCount = new TransferStockCount();
                stockCount.setSku(item.getProductSku());
                if (whSku != null) {
                    stockCount.setSkuName(whSku.getName());
                    stockCount.setLocation(whSku.getLocationNumber());
                    stockCount.setImg(whSku.getImageUrl());
                    stockCount.setWarehouseId(whSku.getWarehouseId());
                    item.setSellSkuName(whSku.getName());
                }
                stockCount.setLastUpdatedBy(DataContextHolder.getUserId());
                stockCount.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                stockCounts.add(stockCount);
            }
            // 中转仓明细
            String key = whFbaAllocation.isFba()?whFbaAllocation.getUnprefixedAccountNumber() + item.getProductSku()
                    :whFbaAllocation.getUnprefixedAccountNumber() + whFbaAllocation.getSite() + item.getProductSku();
            if (stockMap == null || stockMap.get(key) == null) {
                List<WhStock> whStockList = skuMap.get(item.getProductSku());
                Integer stockId = null;
                String locationNumber=null;
                if (CollectionUtils.isEmpty(whStockList)) {
                    WhStock stock = new WhStock();
                    stock.setSku(item.getProductSku());
                    whStockService.createWhStock(stock);
                    stockId = stock.getId();
                }
                else {
                    stockId = whStockList.get(0).getId();
                    locationNumber = whStockList.get(0).getLocationNumber();
                }
                TransferStock transferStock = new TransferStock();
                transferStock.setSku(item.getProductSku());
                transferStock.setSite(item.getSite());
                transferStock.setSurplusQuantity(0);
                transferStock.setStore(whFbaAllocation.getUnprefixedAccountNumber());
                transferStock.setStockId(stockId);
                transferStock.setLocationNumber(locationNumber);
                transferStock.setLastUpdatedBy(DataContextHolder.getUserId());
                transferStock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                if (isFba) {
                    transferStock.setRemark(SaleChannel.CHANNEL_AMAZON);
                } else {
                    transferStock.setRemark(whFbaAllocation.getPurposeHouse());
                }
                createTransferStockList.add(transferStock);
                if (stockMap == null)
                    stockMap = new HashMap<>();
                stockMap.put(whFbaAllocation.getAccountNumber() + whFbaAllocation.getSite() + item.getProductSku(),
                        transferStock);
            }
        }
        // 生成套装关系
        transferStockRelationService.batchCreateTransferStockRelation(createList);
        // 生成库存统计
        transferStockCountService.batchCreateTransferStockCount(stockCounts);
        //生成库存明细记录
        transferStockService.batchCreateTransferStock(createTransferStockList);

        // 单据分配失败，后续流程不走
        if (StringUtils.isNotBlank(allotError)) {
            return allotError;
        }
        if(StringUtils.equalsIgnoreCase(whFbaAllocation.getPurposeHouse(), "TEMU")){
            whFbaAllocation.setShippingMethod(null);
        }
        if (StringUtils.isBlank(allotError)) {
            whFbaAllocation.setStatus(AsnPrepareStatus.WAITING_GEN.intCode());
        } else {
            whFbaAllocation.setStatus(AsnPrepareStatus.WAITING_ALLOT.intCode());
        }
        whFbaAllocation.setSplitRegionFlag(whApvOutStockChainService.checkSplitRegion(whFbaAllocation.getFbaNo(), isFba ?
                AssetOrderType.ASN_PREPARE_ORDER.intCode() : AssetOrderType.ASN_ORDER.intCode()));
        if (whFbaAllocation.getId() == null) {
            whFbaAllocation.setApvType(whFbaAllocation.judgeApvTypeByItems());
            if (isFba) {
                whFbaAllocation.setSite(amazonSite);
            }
            whFbaAllocationDao.createWhFbaAllocation(whFbaAllocation);
            SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), "创建FBA海外仓出库单");
        } else {
            whFbaAllocationDao.updateWhFbaAllocation(whFbaAllocation);
        }

        for (WhFbaAllocationItem item : items) {
            item.setFbaId(whFbaAllocation.getId());
            item.setStore(whFbaAllocation.getAccountNumber());
            item.setSite(whFbaAllocation.getSite());
            String productBarcode = item.getProductBarcode();
            if (StringUtils.isNotBlank(productBarcode) && productBarcode.matches("\\d+")) {
                item.setScItemId(Long.valueOf(productBarcode));
            }
            if (!isFba && StringUtils.isBlank(item.getFnSku()))
                item.setFnSku(item.getProductSku());

            if (whFbaAllocation.getIsAsn() != null && whFbaAllocation.getIsAsn()
                    && StringUtils.isNotBlank(item.getSaleSuiteArticleNumber())) {
                item.setFnSku(item.getSaleSuiteArticleNumber());
            }

            if (whFbaAllocation.getIsAsn() != null && whFbaAllocation.getIsAsn())
                item.setProductBarcode(null);

            if (isTransferOrder && whFbaAllocation.containSuitSku()
                    && StringUtils.isNotBlank(item.getSaleSuiteArticleNumber()))
                // 套装SKU
                item.setProductBarcode(item.getSaleSuiteArticleNumber());

            if (isTransferOrder && item.getQuantity() == null)
                item.setQuantity(item.getSkuQuantity());
            
            item.setProductWeight(null);
            item.setProductWidth(null);
            item.setProductLength(null);
            item.setProductHeight(null);
            // 套装计算sku配套数量
            if (item.getSuitFlag() != null && item.getSuitFlag() == 1) {
                item.setSkuSuitNum(item.getSkuQuantity() / item.getQuantity());
                // jit套装推过来quantity是几套
                Integer qty = item.getQuantity();
                if (isTransferOrder && (whFbaAllocation.getIsAsn() == null || !whFbaAllocation.getIsAsn())) {
                    item.setSkuSuitNum(qty);
                    item.setQuantity(item.getSkuQuantity() / qty);
                }
            }
            if (item.getId() == null) {
                whFbaAllocationItemService.createWhFbaAllocationItem(item);
            } else {
                whFbaAllocationItemService.updateWhFbaAllocationItem(item);
            }
        }

        //更新标签属性
        Map<String, String> tagInfoMap = items.stream().filter(i -> StringUtils.isNotBlank(i.getTagJson()))
                .collect(Collectors.toMap(WhFbaAllocationItem::getProductSku, WhFbaAllocationItem::getTagJson));
        if (MapUtils.isNotEmpty(tagInfoMap) && tagInfoMap.keySet().size() > 0) {
            skuTagInfoService.batchSaveTagInfo(tagInfoMap);
        }

        WhAsnExtra whAsnExtra = whFbaAllocation.getWhAsnExtra();
        boolean downloadMd = true;
        if (whAsnExtra != null) {
            warehouseSelfDeliveryConfigService.generateWarehouseSelfDeliveryConfig(whAsnExtra);
            whAsnExtra.setWhAsnId(whFbaAllocation.getId());
            whAsnExtra.setType(OrderType.FBA_ORDER.intCode());
            // 半托管
            if (whAsnExtra.getBizType() != null && whAsnExtra.getBizType().equals(288000)) {
                whAsnExtra.setPackageMethod(AsnPackageMethodEnum.JIT_HALF.getCode());
            }
            if (whAsnExtra.getId() == null) {
                whAsnExtraService.createWhAsnExtra(whAsnExtra);
            }else{
                WhAsnExtra receivedWhAsnExtra = receivedWhFbaAllocation.getWhAsnExtra();
                if(Objects.nonNull(receivedWhAsnExtra)) {
                    receivedWhAsnExtra.setId(whAsnExtra.getId());
                    receivedWhAsnExtra.setPackageMethod(whAsnExtra.getPackageMethod());
                    receivedWhAsnExtra.setType(whAsnExtra.getType());
                    whAsnExtraService.updateWhAsnExtra(receivedWhAsnExtra);
                }
            }
            downloadMd = !ArraysUtil.contains(
                    ArraysUtil.array(AsnPackageMethodEnum.JIT.getCode(), AsnPackageMethodEnum.JIT_HALF.getCode()),
                    whAsnExtra.getPackageMethod());
        }
        SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), "执行本仓分配");
        
        // jit下载解析标签
        downloadJitSkuPdf(whAsnExtra, whFbaAllocation,downloadMd);
        return allotError;
    }
     // 校验单据库存记录是否都存在
    private String transferStockCheck(WhFbaAllocation whFbaAllocation, Map<String, TransferStock> stockMap) {
        List<WhFbaAllocationItem> items = whFbaAllocation.getItems();
        if (MapUtils.isEmpty(stockMap))
            return String.format("[%s]sku中转仓库存记录为空！",items.stream().map(i -> i.getProductSku()).collect(Collectors.joining(",")));
        List<WhFbaAllocationItem> notExistItem = items.stream().filter(i -> !stockMap.containsKey(whFbaAllocation.getUnprefixedAccountNumber() + i.getSite() + i.getProductSku()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notExistItem))
            return String.format("[%s]sku中转仓库存记录为空！",notExistItem.stream().map(i -> i.getProductSku()).collect(Collectors.joining(",")));
        return null;

    }

    /**
     * 预下载jitsku标签
     *
     * @param whAsnExtra
     * @param whFbaAllocation
     */
    @Override
    public void downloadJitSkuPdf(WhAsnExtra whAsnExtra, WhFbaAllocation whFbaAllocation, boolean downloadMd) {
        if (whFbaAllocation == null || StringUtils.isBlank(whFbaAllocation.getFbaNo()) || whAsnExtra == null)
            return;
        String fbaNo = whFbaAllocation.getFbaNo();
        executors.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    //Shein同步GPSR警告信息
                    if (Objects.equals(SaleChannel.CHANNEL_SHEIN, whFbaAllocation.getPurposeHouse())) {
                        List<String> skuList = whFbaAllocation.getItems().stream()
                                .map(WhFbaAllocationItem::getProductSku).collect(Collectors.toList());
                        apvWaybillService.updateGpsrWaring(skuList,
                                Optional.ofNullable(whAsnExtra.getReceiptCountry()).orElse(CountryEnum.DE.getSite()));
                    }

                    Integer[] packageMethods = new Integer[] { AsnPackageMethodEnum.JIT.getCode(),
                            AsnPackageMethodEnum.JIT_HALF.getCode(),
                            AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode() };
                    if (whAsnExtra.getPackageMethod() != null
                            && ArraysUtil.contains(ArraysUtil.array(AsnPackageMethodEnum.JIT.getCode(),
                                    AsnPackageMethodEnum.JIT_HALF.getCode()), whAsnExtra.getPackageMethod())) {
                        try {
                            String mergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + fbaNo + ".pdf";
                            File file = new File(mergePdfPath);
                            if (file.exists()) {
                                return;
                            }
                            whFbaAllocation.setWhAsnExtra(whAsnExtra);
                            String jitPdfUrl = jitPickupOrderService.createScItemBarcodePdf(whFbaAllocation);
                            // String jitPdfUrl = whFbaAllocationHandleService.getJitPdfUrl(fbaNo, 1,null,null);
                            if (StringUtils.isNotBlank(jitPdfUrl)) {
                                PdfUtils.parseAndSplitToSave(jitPdfUrl, fbaNo, whFbaAllocation.getAllotNum());
                            }
                        }
                        catch (Exception e) {
                            if (whFbaAllocation.getIsAsn() != null && whFbaAllocation.getIsAsn()) {
                                whFbaAllocation.getItems().forEach(item -> {
                                    String temuUrl = PdfUtils.STATIC_FILE_PATH + "/" + whFbaAllocation.getFbaNo() + "_"
                                            + item.getScItemId() + ".pdf";
                                    File skuFile = new File(temuUrl);
                                    if (skuFile.exists()) {
                                        return;
                                    }
                                    if (item.getScItemId() != null) {
                                        String pdfUrl = jitPickupOrderService.tagPrint(
                                                whFbaAllocation.getAccountNumber(),
                                                whFbaAllocation.getWhAsnExtra().getBizType(),
                                                String.valueOf(item.getScItemId()));
                                        PdfUtils.convertPdfFromHttpToPdf(pdfUrl, temuUrl);
                                    }
                                });
                            }
                        }
                    }
                    if (!downloadMd)
                        return;
                    if (!Objects.equals(SaleChannel.CHANNEL_SHEIN, whFbaAllocation.getPurposeHouse())
                            && ArraysUtil.contains(packageMethods, whAsnExtra.getPackageMethod())) {
                        String pdfUrl = whFbaAllocationHandleService.getJitPdfUrl(fbaNo, 2, null, null);
                        String filePath = PdfUtils.STATIC_FILE_JIT_MD_PATH + "/" + fbaNo + ".pdf";
                        PdfUtils.convertPdfFromHttpToPdf(pdfUrl, filePath);
                    }
                    else if (whFbaAllocation.isTransfer()){
                        String pdfUrl = whFbaAllocationHandleService.printXiangmaiFBA(whFbaAllocation);
                        String filePath = PdfUtils.STATIC_FILE_JIT_MD_PATH + "/" + fbaNo + ".pdf";
                        PdfUtils.convertPdfFromHttpToPdf(pdfUrl, filePath);
                    }
                }
                catch (Exception e) {
                    log.error("预下载jitsku标签失败：" + e.getMessage(), e);
                }
            }
        });
    }

    private void downloadSheinSkuPdf(WhAsnExtra whAsnExtra, WhFbaAllocation whFbaAllocation) {
        if (whFbaAllocation == null || Objects.isNull(whFbaAllocation.getId()) || whAsnExtra == null) {
            return;
        }
        Integer id = whFbaAllocation.getId();
        executors.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    if (Objects.equals(SaleChannel.CHANNEL_SHEIN, whFbaAllocation.getPurposeHouse()) && whFbaAllocation.isTransfer()) {
                        File pathFile = new File(PdfUtils.STATIC_FILE_SHEIN_SKU_MERGE_PATH);
                        if (!pathFile.exists()){
                            pathFile.mkdirs();
                        }
                        String saveUrl = PdfUtils.STATIC_FILE_SHEIN_SKU_MERGE_PATH+File.separator+id+".pdf";
                        File file = new File(saveUrl);
                        if (file.exists()){
                            return;
                        }
                        Set<String> mergeTagCode = Arrays.asList(ProductType.BOX,ProductType.BAG,ProductType.PREVENT_SUFFOCATION).stream()
                                .map(ProductType::getCode)
                                .collect(Collectors.toSet());
                        whFbaAllocation.getItems().forEach(item -> {
                            String tagSplit = item.getTag();
                            if (StringUtils.isBlank(tagSplit)) {
                                return;
                            }
                            List<String> tagList = CommonUtils.splitList(tagSplit,",").stream().filter(tag -> mergeTagCode.contains(tag)).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(tagList)){
                                return;
                            }
                            List<String> needMergeFile = new ArrayList<>();
                            for (String tag : tagList){
                                String tagFilePathName = PdfUtils.STATIC_FILE_SHEIN_SKU_MERGE_PATH+File.separator+tag+".pdf";
                                File tagFile = new File(tagFilePathName);
                                if (!tagFile.exists()){
                                    String pdfString = StringRedisUtils.get(RedisConstant.TRANSFER_SHEIN_KEY + tag);
                                    try {
                                        PdfUtils.base64StringToPDF(pdfString, PdfUtils.STATIC_FILE_SHEIN_SKU_MERGE_PATH+File.separator,tag+".pdf");
                                    } catch (IOException e) {
                                        throw new RuntimeException(e);
                                    }
                                }
                                needMergeFile.add(tagFilePathName);
                            }
                            if (StringUtils.isBlank(item.getTemuCodeUrl())){
                                return;
                            }
                            String skuFileName = PdfUtils.STATIC_FILE_SHEIN_SKU_MERGE_PATH+File.separator+id+"_sku_"+item.getProductSku()+".pdf";
                            File skuFile = new File(skuFileName);
                            if (!skuFile.exists()){
                                PdfUtils.convertPdfFromHttpToPdf(item.getTemuCodeUrl(), skuFileName);
                            }
                            needMergeFile.add(skuFileName);
                            String saveFileName =  PdfUtils.STATIC_FILE_SHEIN_SKU_MERGE_PATH+File.separator+id+"_"+item.getProductSku()+".pdf";
                            File saveFile = new File(saveFileName);
                            if (!saveFile.exists()){
                                try {
                                    PdfUtils.merge2OnePdfPage(needMergeFile, PdfUtils.STATIC_FILE_SHEIN_SKU_MERGE_PATH, id+"_"+item.getProductSku()+".pdf");
                                } catch (Exception e) {
                                    log.error("预下载及合并id="+id+"SheinSku标签失败：" + e.getMessage(), e);
                                }
                            }
                        });
                    }
                }
                catch (Exception e) {
                    log.error("预下载及合并SheinSku标签失败：" + e.getMessage(), e);
                }
            }
        });
    }

    private void downloadSmtSkuPdf(WhAsnExtra whAsnExtra, WhFbaAllocation whFbaAllocation) {
        if (whFbaAllocation == null || Objects.isNull(whFbaAllocation.getId()) || whAsnExtra == null) {
            return;
        }
        Integer id = whFbaAllocation.getId();
        executors.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    List<Integer> packageMethods = AsnPackageMethodEnum.getJitMergeSkuEnumCodeList();
                    if (Objects.equals(SaleChannel.CHANNEL_SMT, whFbaAllocation.getPurposeHouse())
                            && !whFbaAllocation.isTransfer()
                            && packageMethods.contains(whAsnExtra.getPackageMethod())) {
                        Set<String> mergeTagCode = Arrays.asList(ProductType.values()).stream()
                                .map(ProductType::getCode)
                                .collect(Collectors.toSet());
                        File pathFile = new File(PdfUtils.STATIC_FILE_SMT_SKU_MERGE_PATH);
                        if (!pathFile.exists()) {
                            pathFile.mkdirs();
                        }
                        whFbaAllocation.getItems().forEach(item -> {
                            String tagSplit = item.getTag();
                            if (StringUtils.isBlank(tagSplit)) {
                                return;
                            }
                            List<String> tagList = CommonUtils.splitList(tagSplit,",").stream().filter(tag -> mergeTagCode.contains(tag)).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(tagList)){
                                return;
                            }
                            List<String> needMergeFile = new ArrayList<>();
                            for (String tag : tagList){
                                String tagFilePathName = PdfUtils.STATIC_FILE_SMT_SKU_MERGE_PATH+File.separator+tag+".pdf";
                                File tagFile = new File(tagFilePathName);
                                if (!tagFile.exists()){
                                    String pdfString = StringRedisUtils.get(RedisConstant.TRANSFER_SHEIN_KEY + tag);
                                    try {
                                        PdfUtils.base64StringToPDF(pdfString, PdfUtils.STATIC_FILE_SMT_SKU_MERGE_PATH+File.separator,tag+".pdf");
                                    } catch (IOException e) {
                                        throw new RuntimeException(e);
                                    }
                                }
                                needMergeFile.add(tagFilePathName);
                            }
                            if (StringUtils.isBlank(item.getTemuCodeUrl())){
                                return;
                            }
                            String skuFileName = PdfUtils.STATIC_FILE_SMT_SKU_MERGE_PATH+File.separator+id+"_sku_"+item.getProductSku()+".pdf";
                            File skuFile = new File(skuFileName);
                            if (!skuFile.exists()){
                                PdfUtils.convertPdfFromHttpToPdf(item.getTemuCodeUrl(), skuFileName);
                            }
                            needMergeFile.add(skuFileName);
                            String saveFileName =  PdfUtils.STATIC_FILE_SMT_SKU_MERGE_PATH+File.separator+id+"_"+item.getProductSku()+".pdf";
                            File saveFile = new File(saveFileName);
                            if (!saveFile.exists()){
                                try {
                                    PdfUtils.merge2OnePdfPage(needMergeFile, PdfUtils.STATIC_FILE_SMT_SKU_MERGE_PATH, id+"_"+item.getProductSku()+".pdf");
                                } catch (Exception e) {
                                    log.error("预下载及合并id="+id+"SmtSku标签失败：" + e.getMessage(), e);
                                }
                            }
                        });
                    }
                }
                catch (Exception e) {
                    log.error("预下载及合并SmtSku标签失败：" + e.getMessage(), e);
                }
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String doUpdateFbaAllocation(WhFbaAllocation whFbaAllocation) {
        log.info("doUpdateFbaAllocation object JsonString:" + JSONObject.toJSONString(whFbaAllocation));
        String fbaNo = whFbaAllocation.getFbaNo();
        WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
        queryCondition.setFbaNo(fbaNo);
        queryCondition.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> whFbaAllocationList = this.queryWhFbaAllocationAndItems(queryCondition, null);
        if (CollectionUtils.isEmpty(whFbaAllocationList)) {
            log.info("根据fbaNo-"+fbaNo+"无法找到订单对象!");
            return "根据fbaNo-"+fbaNo+"无法找到订单对象!";
        }
        WhFbaAllocation dbWhFbaAllocation = whFbaAllocationList.get(0);
        if (!dbWhFbaAllocation.isTransfer()){
            log.info("非中转仓订单，无法进行修改!");
            return "非中转仓订单，无法进行修改!";
        }
        WhFbaAllocation updateWhFbaAllocation = new WhFbaAllocation();
        updateWhFbaAllocation.setId(dbWhFbaAllocation.getId());
        updateWhFbaAllocation.setTrackingNumber(whFbaAllocation.getTrackingNumber());
        this.updateWhFbaAllocation(updateWhFbaAllocation);
        SystemLogUtils.FBAALLOCATIONLOG.log(dbWhFbaAllocation.getId(), "更新订单追踪号-"+whFbaAllocation.getTrackingNumber());
        return null;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String doUpdateTags(WhFbaAllocation whFbaAllocation) {
        log.info("doUpdateTags object JsonString:" + JSONObject.toJSONString(whFbaAllocation));
        String fbaNo = whFbaAllocation.getFbaNo();
        WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
        queryCondition.setFbaNo(fbaNo);
        queryCondition.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> whFbaAllocationList = this.queryWhFbaAllocationAndItems(queryCondition, null);
        if (CollectionUtils.isEmpty(whFbaAllocationList)) {
            log.info("根据fbaNo-" + fbaNo + "无法找到订单对象!");
            return "根据fbaNo-" + fbaNo + "无法找到订单对象!";
        }

        WhFbaAllocation dbWhFbaAllocation = whFbaAllocationList.get(0);
        WhAsnExtra whAsnExtra = whFbaAllocation.getWhAsnExtra();
        boolean lackAsnExtraMessage = Objects.isNull(whAsnExtra)
                || (StringUtils.isBlank(whAsnExtra.getBoxMarkUrl()) && StringUtils.isBlank(whAsnExtra.getCollectLabelUrl()));
        if (!lackAsnExtraMessage) {
            WhAsnExtra dbWhAsnExtra = dbWhFbaAllocation.getWhAsnExtra();

            WhAsnExtra updateWhAsnExtra = new WhAsnExtra();
            updateWhAsnExtra.setId(dbWhAsnExtra.getId());
            updateWhAsnExtra.setBoxMarkUrl(whAsnExtra.getBoxMarkUrl());
            updateWhAsnExtra.setCollectLabelUrl(whAsnExtra.getCollectLabelUrl());
            whAsnExtraService.updateWhAsnExtra(updateWhAsnExtra);
            SystemLogUtils.FBAALLOCATIONLOG.log(dbWhFbaAllocation.getId(), "更新订单打印链接-" + JSON.toJSONString(updateWhAsnExtra));
        }

        List<WhFbaAllocationItem> items = whFbaAllocation.getItems();
        boolean lackItemMessage = Optional.ofNullable(items)
                .orElse(new ArrayList<>())
                .stream()
                .allMatch(item -> StringUtils.isBlank(item.getProductSku()) || StringUtils.isBlank(item.getTemuCodeUrl()));
        if (!lackItemMessage) {
            Map<String, List<WhFbaAllocationItem>> dbItemSkuMap = Optional.ofNullable(dbWhFbaAllocation.getItems())
                    .orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.groupingBy(WhFbaAllocationItem::getProductSku));
            List<WhFbaAllocationItem> updateItems = items.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getProductSku()) && StringUtils.isNotBlank(item.getTemuCodeUrl()))
                    .map(v -> {
                        List<WhFbaAllocationItem> dbItems = dbItemSkuMap.get(v.getProductSku());
                        List<WhFbaAllocationItem> returnItems = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(dbItems)) {
                            for(WhFbaAllocationItem dbItem : dbItems) {
                                WhFbaAllocationItem updateItem = new WhFbaAllocationItem();
                                updateItem.setId(dbItem.getId());
                                updateItem.setProductSku(v.getProductSku());
                                updateItem.setTemuCodeUrl(v.getTemuCodeUrl());
                                returnItems.add(updateItem);
                            }
                        }
                        return returnItems;
                    })
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(updateItems)) {
                whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(updateItems);
                SystemLogUtils.FBAALLOCATIONLOG.log(dbWhFbaAllocation.getId(), "更新订单SKU打印链接-" + JSON.toJSONString(updateItems));
            }
        }
        return null;
    }

    @Override
    @StockServicelock
    public void doGenAllocationDemand(List<String> lockList,List<WhFbaAllocationData> allocationDatas) throws Exception {
        log.info("doGenAllocationDemand size:" + allocationDatas.size());
        String accountNumber = allocationDatas.get(0).getNoPrefixAccountNumber();

        String allocateNumber = CreateTaskNoUtils.createWhAllocateNo();

        WhAllocateRecord whAllocateRecord = new WhAllocateRecord();
        whAllocateRecord.setAllocateNumber(allocateNumber);
        whAllocateRecord.setOutWhType(WhAllocateTypeEnum.LOCAL.intCode());
        whAllocateRecord.setInWhType(WhAllocateTypeEnum.FBA.intCode());
        whAllocateRecordService.createWhAllocateRecord(whAllocateRecord);
        //生成调拨记录
        List<WhAllocateRecordItem> itemList = allocationDatas.stream().map(item -> new WhAllocateRecordItem(item)).collect(Collectors.toList());
        whAllocateRecordService.createAllocateRecord(itemList, accountNumber, WhAllocateInputTypeEnum.LOCAL_TO_FBA, allocateNumber, whAllocateRecord);

        Map<String, WhFbaAllocationData> itemMap = new HashMap<>();
        for (WhFbaAllocationData allocationData : allocationDatas) {
            itemMap.put(allocationData.getSku(), allocationData);
        }
        // 从本仓可用到中转仓可用  sellerSku + sku 集合 同时加锁
        List<String> skus = allocationDatas.stream().map(a -> a.getSku()).collect(Collectors.toList());
        List<String> allSkuList = new ArrayList<>(skus);
        for (String sku : skus) {
            if (itemMap.get(sku) != null) allSkuList.add(itemMap.get(sku).getItemLockKeys(accountNumber));
        }
        whFbaUpdateStockService.doAllot(allSkuList, skus, allocationDatas, allocateNumber, itemMap, whAllocateRecord.getId(), whAllocateRecord.getAllocateNumber());

        List<TakeStockDTO> takeStockDTOList = new ArrayList<>();

        // 保存OMS推送调拨需求  更新已调拨数量
        allocationDatas.forEach(a -> {
            a.setOrderQuantity(a.getAllocationQuantity());
            Integer orElse = Optional.ofNullable(a.getAllocationQuantity()).orElse(0);
            takeStockDTOList.add(new TakeStockDTO(a.getSku(), TakeStockReasonEnum.STOCK_TRANSFER.intCode(), allocateNumber, -orElse, new Timestamp(System.currentTimeMillis()), RecordSourceEnum.SYSTEM_ADD.intCode(), a.getSaleChannel()));
        });
        whFbaAllocationDataService.batchCreateWhFbaAllocationData(allocationDatas);

        //库存调拨  本地->中转仓  1.生成盘点记录 2.实时同步wms修改后的可用库存到pms
        takeStockRecordService.batchCreateRecordAndSyncStock(takeStockDTOList);
        // 推送中转仓库存变更到redis
        List<AmqMessage> amqMessageList=new ArrayList<>();
        for (TakeStockDTO takeStockDTO : takeStockDTOList) {
            if (org.apache.commons.lang.StringUtils.isBlank(takeStockDTO.getSku()) || takeStockDTO.getTakeStockReason() == null || takeStockDTO.getTakeStockNumber() == null) {
                continue;
            }
            amqMessageList.add(AssembleMessageDataUtils.assembleDataToFinance(AmqMessageModuleName.PUSH_TRANSFER_STOCK_INFO.getCode(),
                    amqMessageTransfer -> {
                        amqMessageTransfer.setRelevantParam(takeStockDTO.getSku());
                        Map<String, Object> map = new HashMap<>();
                        map.put("sku", takeStockDTO.getSku());
                        map.put("count_surplus", (-takeStockDTO.getTakeStockNumber()));
                        map.put("saleChannel",(takeStockDTO.getRemark()));
                        // 消息体
                        String messageBody = JSONObject.toJSONString(map);
                        return messageBody;
                    }));
        }
        amqMessageService.batchCreateAmqMessage(amqMessageList);
        // 售后结算
       // orderRequestService.getAfterSaleSkuStock(takeStockDTOList.stream().map(t -> t.getSku()).collect(Collectors.toList()));

    }


    // 取消货件
    @Override
    public void doCancelFbaAllocation(WhFbaAllocation whFbaAllocation) throws Exception {

        // 已取消的直接返回成功
        if (AsnPrepareStatus.CANCEL.intCode().equals(whFbaAllocation.getStatus())) {
            return;
        }

        List<WhFbaAllocation> updateList = new ArrayList<>();
        List<WhFbaAllocationItem> updateItemList = new ArrayList<>();
        // 生成拣货任务 非 待分配、已交运、已装车
        if (whFbaAllocation.getStatus() != null
                && !AsnPrepareStatus.DELIVER.intCode().equals(whFbaAllocation.getStatus())
                && !AsnPrepareStatus.LOADED.intCode().equals(whFbaAllocation.getStatus())
                && !AsnPrepareStatus.WAITING_ALLOT.intCode().equals(whFbaAllocation.getStatus())) {
            // 取消本仓库存
            whFbaUpdateStockService.cancelStartPick(whFbaAllocation);
            // 有物流信息
            if (StringUtils.isNotBlank(whFbaAllocation.getShippingOrderNo())
                    && StringUtils.isNotBlank(whFbaAllocation.getBatNo())) {
                WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
                queryCondition.setBatNo(whFbaAllocation.getBatNo());
                List<WhFbaAllocation> whFbaAllocationList = queryWhFbaAllocationAndItems(queryCondition, null);

                for (WhFbaAllocation whFbaAll : whFbaAllocationList) {
                    WhFbaAllocation updateWhFbaAllocation = new WhFbaAllocation();
                    updateWhFbaAllocation.setId(whFbaAll.getId());
                    // 清空物流信息
                    updateWhFbaAllocation.setIsResetShippingInfo(true);
                    for (WhFbaAllocationItem item : whFbaAll.getItems()) {
                        WhFbaAllocationItem updateItem = new WhFbaAllocationItem();
                        updateItem.setId(item.getId());
                        updateItem.setIsResetCost(true);
                        updateItemList.add(updateItem);
                    }
                    updateList.add(updateWhFbaAllocation);
                }
            }
            if (whFbaAllocation.isFba()){
                // FBA单更新货件单状态
                WhFbaShipmentQueryCondition shipmentQUery = new WhFbaShipmentQueryCondition();
                shipmentQUery.setFbaNo(whFbaAllocation.getFbaNo());
                List<WhFbaShipment> whFbaShipments = whFbaShipmentService.queryWhFbaShipments(shipmentQUery, null);
                if (CollectionUtils.isNotEmpty(whFbaShipments)) {
                    List<WhFbaShipment> updateShipments = new ArrayList<>();
                    for (WhFbaShipment whFbaShipment : whFbaShipments) {
                        WhFbaShipment update = new WhFbaShipment();
                        update.setId(whFbaShipment.getId());
                        update.setStatus(AsnPrepareStatus.CANCEL.intCode());
                        update.setCancelTime(new Timestamp(System.currentTimeMillis()));
                        updateShipments.add(update);
                    }
                    whFbaShipmentService.batchUpdateWhFbaShipment(updateShipments);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            WhFbaAllocation update = updateList.stream().filter(w -> w.getId().equals(whFbaAllocation.getId())).findFirst().get();
            update.setStatus(AsnPrepareStatus.CANCEL.intCode());
            update.setCancelTime(new Timestamp(System.currentTimeMillis()));
            whFbaAllocation.addCancelSuffix(update);
        } else {
            WhFbaAllocation update = new WhFbaAllocation();
            // 添加 出库取消 标记
            if (AsnPrepareStatus.LOADED.intCode().equals(whFbaAllocation.getStatus()) ||
                    AsnPrepareStatus.DELIVER.intCode().equals(whFbaAllocation.getStatus())) {
                update.addTag(AsnTagStatus.DELIVER_CANCEL.getCode());
                update.setStatus(whFbaAllocation.getStatus());
            } else {
                update.setStatus(AsnPrepareStatus.CANCEL.intCode());
                whFbaAllocation.addCancelSuffix(update);
            }
            update.setCancelTime(new Timestamp(System.currentTimeMillis()));
            update.setId(whFbaAllocation.getId());
            updateList.add(update);
        }

        // 保存明细
        for (WhFbaAllocationItem item : updateItemList) {
            whFbaAllocationItemService.updateWhFbaAllocationItem(item);
        }

        //保存海外仓出库单状态
        for (WhFbaAllocation whFba : updateList) {
            updateWhFbaAllocation(whFba);
            String content;
            if (whFba.getId().equals(whFbaAllocation.getId())) {
                content = "取消海外仓出库单";
                whFbaAllocation.setTags(whFba.getTags());
                whFbaAllocation.setStatus(whFba.getStatus());
            } else {
                content = "清空出库单物流信息";
            }
            if (whFba.getStatus().equals(AsnPrepareStatus.CANCEL.intCode()))
                // 取消唯一码绑定（已交运已装车的不取消）
                whUniqueSkuService.cancelApvOrder(whFbaAllocation.getFbaNo());
            
            SystemLogUtils.FBAALLOCATIONLOG.log(whFba.getId(), content);
        }
        if (whFbaAllocation.isFba()){
            // FBA单更新货件单状态
            WhFbaShipmentQueryCondition shipmentQuery = new WhFbaShipmentQueryCondition();
            shipmentQuery.setFbaNo(whFbaAllocation.getFbaNo());
            List<WhFbaShipment> whFbaShipments = whFbaShipmentService.queryWhFbaShipments(shipmentQuery, null);
            if (CollectionUtils.isNotEmpty(whFbaShipments)) {
                for (WhFbaShipment whFbaShipment : whFbaShipments) {
                    WhFbaAllocation fbaAllocation = whFbaShipment.transformToWhFbaAllocation();
                    fbaAllocation.setLogisticsAging(whFbaAllocation.getLogisticsAging());
                    fbaAllocation.setSalesperson(whFbaAllocation.getSalesperson());
                    sendMsgToTms(fbaAllocation, TmsSendMsgType.SEND_ORDER_STATUS.intCode());
                }
            }
            return;
        }
        // 调物流接口取消
        sendMsgToTms(whFbaAllocation, TmsSendMsgType.SEND_ORDER_STATUS.intCode());
    }


    /**
     * fba部分sku取消
     *
     * @param cancelFbaAllocation
     */
    @Override
    public ResponseJson doCancelPortionFbaSku(WhFbaAllocation cancelFbaAllocation) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        boolean bool = false;
        try {
            WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
            queryCondition.setFbaNo(cancelFbaAllocation.getFbaNo());
            List<WhFbaAllocation> whFbaAllocationList = queryWhFbaAllocationAndItems(queryCondition, null);
            if (CollectionUtils.isEmpty(whFbaAllocationList)) {
                response.setMessage("未查询到相关fba单号" + cancelFbaAllocation.getFbaNo());
                log.info("未查询到相关fba单号" + cancelFbaAllocation.getFbaNo());
                return response;
            }

            WhFbaAllocation whFbaAllocation = whFbaAllocationList.get(0);

            //fba明细
            List<WhFbaAllocationItem> itemList = whFbaAllocation.getItems();
            if (CollectionUtils.isEmpty(itemList)) {
                response.setMessage(whFbaAllocation.getFbaNo() + "发货单明细不存在!");
                return response;
            }
            Integer status = whFbaAllocation.getStatus();
            if (status == null) {
                response.setMessage(whFbaAllocation.getFbaNo() + "发货单类型状态异常！");
                return response;
            }
            if (AsnPrepareStatus.DELIVER.intCode() <= status && !Objects.equals(AsnPrepareStatus.WAITING_GRID_CONFIRM.intCode(),status)) {
                response.setMessage(whFbaAllocation.getFbaNo() + "发货单为" + AsnPrepareStatus.getNameByCode(status + "") + "状态不能取消！");
                return response;
            }

            //取消的fba明细
            List<WhFbaAllocationItem> cancelItemList = cancelFbaAllocation.getItems();

            List<String> skuList = cancelItemList.stream().map(WhFbaAllocationItem::getProductSku).collect(Collectors.toList());
            WhApvOutStockChainQueryCondition chainQueryCondition=new WhApvOutStockChainQueryCondition();
            chainQueryCondition.setRelevantNo(cancelFbaAllocation.getFbaNo());
            chainQueryCondition.setSkus(skuList);
            List<WhApvOutStockChain> whApvOutStockChains = whApvOutStockChainService.queryWhApvOutStockChains(chainQueryCondition, null);
            if (CollectionUtils.isEmpty(whApvOutStockChains)){
                response.setMessage(whFbaAllocation.getFbaNo() + "发货单一品多位关联数据不存在,取消失败！");
                return response;
            }
            whFbaAllocation.setWhApvOutStockChainList(whApvOutStockChains);

            //1、FBA单分配库存之前
            if (AsnPrepareStatus.WAITING_ALLOT.intCode().equals(status)) {
                bool = allotBefore(cancelItemList, whFbaAllocation);
            }
            //2、FBA单分配库存之后，SKU拣货前
            if (AsnPrepareStatus.WAITING_GEN.intCode().equals(status) || AsnPrepareStatus.WAITING_PICK.intCode().equals(status)) {
                bool = pickingBefore(cancelItemList, whFbaAllocation);
            }
            //3、FBA单生成拣货任务，拣货拣货后，播种完成前
            if ((AsnPrepareStatus.WAITING_PICK.intCode().equals(status) || AsnPrepareStatus.WAITING_GRID.intCode().equals(status)
                    || AsnPrepareStatus.WAITING_GRID_CONFIRM.intCode().equals(status)) && !bool) {
                bool = pickingAfter(cancelItemList, whFbaAllocation);
            }
            //4、播种完成后装箱前
            if ((AsnPrepareStatus.WAITING_BOX.intCode().equals(status)
                    || AsnPrepareStatus.WAITING_LABEL.intCode().equals(status)
                    || AsnPrepareStatus.WAITING_CHECK.intCode().equals(status)
                    || AsnPrepareStatus.PICK_STOCK_OUT.intCode().equals(status)) && !bool) {
                bool = waitingBoxAfter(cancelItemList, whFbaAllocation);
            }

            //5、装箱后，交运前
            if ((AsnPrepareStatus.WAITING_BOX.intCode().equals(status)
                    || AsnPrepareStatus.WAITING_CONFIRM.intCode().equals(status)
                    || AsnPrepareStatus.WAITING_DELIVER.intCode().equals(status)) && !bool) {
                bool = loadingAfter(cancelItemList, whFbaAllocation);
                if (bool && whFbaAllocation.isFba()){
                    whFbaAllocation.setStatus(AsnPrepareStatus.WAITING_BOX.intCode());
                    sendMsg(whFbaAllocation);
                }
            }
            if (!bool) {
                log.info("FBA单号未查到满足条件的单号:" + cancelFbaAllocation);
                response.setMessage(cancelFbaAllocation.getFbaNo() + "单号未查到满足条件的单号或该单据sku部分库存不足已退回可用，无需取消或联系开发人员");
            }

        } catch (Exception e) {
            log.error("fba部分sku取消失败！" + e.getMessage());
            throw new BusinessException(e.getMessage());
        }
        if (bool) {
            response.setStatus(StatusCode.SUCCESS);
        }
        return response;
    }

    @Override
    public String notifyFbaToGrid(List<WhFbaAllocation> allocations) {
        if (CollectionUtils.isEmpty(allocations)){
            return "订单为空!";
        }
        try {
            List<Integer> ids = allocations.stream().map(WhFbaAllocation::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ids)){
                return "订单为空";
            }
            WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
            queryCondition.setIds(ids);
            List<WhFbaAllocation> whFbaAllocations = this.queryWhFbaAllocationAndItems(queryCondition, null);
            if (CollectionUtils.isEmpty(whFbaAllocations)) {
                log.info("根据id={}查询fba发货单，其不存在",JSON.toJSONString(ids));
                return String.format("根据id={}查询fba发货单，其不存在",JSON.toJSONString(ids));
            }
            whFbaAllocations = whFbaAllocations.stream()
                    .filter(f -> Objects.equals(AsnPrepareStatus.WAITING_GRID_CONFIRM.intCode(),f.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(whFbaAllocations)) {
                log.info("不存在待播种确认状态的fba订单!");
                return "不存在待播种确认状态的fba订单!";
            }
            List<WhFbaAllocation> updateWhFbaAllocations = new ArrayList<>();
            for (WhFbaAllocation whFbaAllocation : whFbaAllocations){
                WhFbaAllocation updateWhFbaAllocation = new WhFbaAllocation();
                updateWhFbaAllocation.setId(whFbaAllocation.getId());
                updateWhFbaAllocation.setStatus(AsnPrepareStatus.WAITING_GRID.intCode());
                updateWhFbaAllocations.add(updateWhFbaAllocation);
            }
            if (CollectionUtils.isNotEmpty(updateWhFbaAllocations)){
                this.batchUpdateWhFbaAllocation(updateWhFbaAllocations);
                updateWhFbaAllocations.forEach(f -> SystemLogUtils.FBAALLOCATIONLOG.log(f.getId(), "OMS通知进行播种",
                        new String[][] { { "状态变更", AsnPrepareStatus.getNameByCode(f.getStatus() + "") } }));
            }
            for (WhFbaAllocation whFbaAllocation : whFbaAllocations) {
                whFbaAllocation.setStatus(AsnPrepareStatus.WAITING_GRID.intCode());
                sendMsg(whFbaAllocation);
            }
            return null;
        }catch (Exception e){
            log.error("fba确认播种失败！" + e.getMessage());
        }
        return null;
    }


    //5、装箱后，交运前
    public boolean loadingAfter(List<WhFbaAllocationItem> cancelItemList, WhFbaAllocation fbaAllocation) {
        List<WhFbaAllocationItem> items = fbaAllocation.getItems();
        if (AsnPrepareStatus.WAITING_BOX.intCode().equals(fbaAllocation.getStatus())) {
            //筛选已装箱
            items = items.stream().filter(item -> item.getLoadingQuantity() != null && item.getLoadingQuantity() > 0).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(items)) return false;

        //fba sku数件取消    中转仓库  减 已拣库存   加 已拣返架库存
        boolean bool = pickingStockAlteration(cancelItemList, fbaAllocation);
        if (!bool) return false;

        //单据退回到待装箱状态
        WhFbaAllocation updateWhFbaAllocation = new WhFbaAllocation();
        updateWhFbaAllocation.setId(fbaAllocation.getId());
        updateWhFbaAllocation.setStatus(AsnPrepareStatus.WAITING_BOX.intCode());
        updateWhFbaAllocation(updateWhFbaAllocation);

        // 通知物流装箱信息作废 并且物流返回批次号
        String url = CacheUtils.SystemParamGet("TMS_PARAM.PUSH_CANCEL_ORDER").getParamValue();
        if (StringUtils.isBlank(url)) {
            throw new BusinessException("WMS获取物流装箱信息作废URL路径为空！");
        }
        //String url="http://192.168.8.237:8080/firstBatOrder/cancelOrder/";

        ApiResult apiResult = null;
        try {
            apiResult = HttpUtils.get(url + fbaAllocation.getFbaNo(), HttpUtils.ACCESS_TOKEN, ApiResult.class);
        } catch (Exception e) {
            throw new BusinessException("调用物流装箱信息作废接口失败！" + e);
        }
        if (apiResult != null && !apiResult.isSuccess()) {
            throw new BusinessException("通知物流装箱信息作废失败！" + apiResult.getErrorMsg());
        }
        String batNo = fbaAllocation.getBatNo();
        List<WhFbaAllocation> whFbaAllocations = new ArrayList<>();
        //不存在批次号 清空当前FBA发货单号装箱数量
        if (StringUtils.isBlank(batNo)) {
            whFbaAllocations.add(fbaAllocation);
        } else {
            //存在 清空同批次FBA发货单号
            WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
            queryCondition.setBatNo(batNo);
            whFbaAllocations = queryWhFbaAllocationAndItems(queryCondition, null);
        }
        //清空装箱数量
        List<WhFbaAllocationItem> updateAllocationItemList = new ArrayList<>();
        //清空物流方式
        List<WhFbaAllocation> updateWhFbaAllocations = new ArrayList<>();
        for (WhFbaAllocation whFbaAllocation : whFbaAllocations) {
            WhFbaAllocation allocation = new WhFbaAllocation();
            allocation.setId(whFbaAllocation.getId());
            allocation.setStatus(AsnPrepareStatus.WAITING_BOX.intCode());
            if (SaleChannel.saleChannels.contains(fbaAllocation.getPurposeHouse())) {
                //海外仓清空
                allocation.setShippingMethodByTms("");
                allocation.setTrackingNumberByTms("");
            } else {
                //fba仓清空
                allocation.setShippingMethod("");
                allocation.setTrackingNumber("");
            }
            allocation.setShippingOrderNo("");
            allocation.setBatNo("");
            updateWhFbaAllocations.add(allocation);

            List<WhFbaAllocationItem> allocationItem = whFbaAllocation.getItems();
            if (CollectionUtils.isEmpty(allocationItem)) continue;
            List<String> productSkuList = cancelItemList.stream().map(WhFbaAllocationItem::getProductSku).collect(Collectors.toList());
            List<Integer> boxNoList = allocationItem.stream().filter(a ->Optional.ofNullable(a.getLoadNum()).orElse(0)>0 && productSkuList.contains(a.getProductSku())).map(i->Optional.ofNullable(i.getBoxNo()).orElse(1)).distinct().collect(Collectors.toList());
            for (WhFbaAllocationItem whFbaAllocationItem : allocationItem) {
                WhFbaAllocationItem updateAllocationItem = new WhFbaAllocationItem();
                updateAllocationItem.setId(whFbaAllocationItem.getId());
                if (boxNoList.contains(whFbaAllocationItem.getBoxNo())){
                    updateAllocationItem.setProductLength(0d);
                    updateAllocationItem.setProductWeight(0d);
                    updateAllocationItem.setProductWidth(0d);
                    updateAllocationItem.setProductHeight(0d);
                    updateAllocationItem.setTollWeight(0d);
                    updateAllocationItem.setFreight(0d);
                    updateAllocationItem.setOtherPrice(0d);
                    updateAllocationItem.setBoxCost(0d);
                    updateAllocationItem.setProductPrice(0d);
                }
                if (productSkuList.contains(whFbaAllocationItem.getProductSku())) {
                    updateAllocationItem.setLoadingQuantity(0);
                    updateAllocationItem.setLoadNum(0);
                }else{
                    updateAllocationItem.setLoadingQuantity(whFbaAllocationItem.getLoadingQuantity());
                    updateAllocationItem.setLoadNum(whFbaAllocationItem.getLoadNum());
                }
                if (boxNoList.contains(whFbaAllocationItem.getBoxNo()) || productSkuList.contains(whFbaAllocationItem.getProductSku())){
                    updateAllocationItemList.add(updateAllocationItem);
                }
            }
        }


        if (CollectionUtils.isNotEmpty(updateAllocationItemList)) {
            whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(updateAllocationItemList);
        }

        if (CollectionUtils.isNotEmpty(updateWhFbaAllocations)) {
            batchUpdateWhFbaAllocation(updateWhFbaAllocations);
            return true;
        }
        return false;
    }


    //播种完成后装箱前
    public boolean waitingBoxAfter(List<WhFbaAllocationItem> cancelItemList, WhFbaAllocation fbaAllocation) {
        List<WhFbaAllocationItem> items = fbaAllocation.getItems();
        //过滤已装箱未推送
        boolean allMatch = items.stream().allMatch(item -> item.getLoadingQuantity() == null || item.getLoadingQuantity() == 0);
        if (!allMatch) return false;
        //fba sku数件取消    中转仓库  减 已拣库存   加 已拣返架库存
        return pickingStockAlteration(cancelItemList, fbaAllocation);
    }

    //FBA单生成拣货任务，拣货拣货后，播种完成前
    public boolean pickingAfter(List<WhFbaAllocationItem> cancelItemList, WhFbaAllocation fbaAllocation) {
        //库存从已拣退回到已拣返架
        WhPickingTaskQueryCondition whPickingTaskQueryCondition = new WhPickingTaskQueryCondition();
        whPickingTaskQueryCondition.setApvNo(fbaAllocation.getFbaNo());
        whPickingTaskQueryCondition.setTaskStatus(PickingTaskStatus.COMPLETED.intCode());
        whPickingTaskQueryCondition.setQueryGridExpand(true);
        List<WhPickingTask> whPickingTasks = whPickingTaskService.queryWhPickingTasks(whPickingTaskQueryCondition, null);
        if (CollectionUtils.isEmpty(whPickingTasks)) return false;

        //取播种中的拣货任务号
        List<WhPickingTask> whPickingTaskList = whPickingTasks.stream().filter(a -> a.getPickTaskExpand() != null &&
                (PickTaskGridStatus.GRIDING.intCode().equals(a.getPickTaskExpand().getGridStatus()) || PickTaskGridStatus.UN_GRID.intCode().equals(a.getPickTaskExpand().getGridStatus()))).collect(Collectors.toList());

        List<WhApvGridItem> gridItems = new ArrayList<>();
        //存在正在播种的任务号
        if (CollectionUtils.isNotEmpty(whPickingTaskList)) {
            WhApvGridQueryCondition whApvGridQueryCondition = new WhApvGridQueryCondition();
            whApvGridQueryCondition.setApvId(fbaAllocation.getId());
            List<WhApvGrid> whApvGrids = whApvGridService.searchWhApvGrids(whApvGridQueryCondition, null);
            Optional.ofNullable(whApvGrids).orElse(new ArrayList<>()).stream().forEach(a-> gridItems.addAll(a.getGridItems()));
        }
        //取消的sku存在状态为 播种中 的任务
        if (CollectionUtils.isNotEmpty(gridItems)) {
            Map<String, List<WhApvGridItem>> apvGridItemMap = gridItems.stream().collect(Collectors.groupingBy(WhApvGridItem::getItemSku));
            fbaAllocation.setApvGridItemMap(apvGridItemMap);
        }

        //fba sku数件取消    中转仓库  减 已拣库存   加 已拣返架库存
        return pickingStockAlteration(cancelItemList, fbaAllocation);
    }

    //FBA单分配库存之后，SKU拣货前
    @StockServicelock
    public boolean pickingBefore(List<WhFbaAllocationItem> cancelItemList, WhFbaAllocation fbaAllocation) {
        List<WhPickingTaskSku> updateTaskSkuList = new ArrayList<>();
        Map<String, WhPickingTaskSku> taskSkuMap = new HashMap<>();
        //已生成拣货任务并且未拣货
        boolean bool = false;
        if (AsnPrepareStatus.WAITING_PICK.intCode().equals(fbaAllocation.getStatus())) {
            WhPickingTaskQueryCondition whPickingTaskQueryCondition = new WhPickingTaskQueryCondition();
            List<Integer> intList = new ArrayList<Integer>();
            intList.add(PickingTaskStatus.UNRECEIVED.intCode());
            intList.add(PickingTaskStatus.RECEIVED.intCode());
            whPickingTaskQueryCondition.setTaskStatuss(intList);
            whPickingTaskQueryCondition.setApvNo(fbaAllocation.getFbaNo());
            List<WhPickingTask> whPickingTasks = whPickingTaskService.queryWhPickingTasks(whPickingTaskQueryCondition,
                    null);
            if (CollectionUtils.isNotEmpty(whPickingTasks)) {
                WhPickingTaskSkuQueryCondition whPickingTaskSkuQueryCondition = new WhPickingTaskSkuQueryCondition();
                whPickingTaskSkuQueryCondition.setTaskId(whPickingTasks.get(0).getId());
                List<WhPickingTaskSku> whPickingTaskSkus = whPickingTaskSkuService
                        .queryWhPickingTaskSkus(whPickingTaskSkuQueryCondition, null);
                if (CollectionUtils.isNotEmpty(whPickingTasks)) {
                    taskSkuMap = whPickingTaskSkus.stream().collect(Collectors.toMap(WhPickingTaskSku::getSku, a -> a));
                }
            }
            bool = true;
        }
        //任务状态为待拣货并且不存在拣货任务对应的sku
        if (bool && MapUtils.isEmpty(taskSkuMap)) {
            return false;
        }

        //存在任务号状态为拣货中且当前sku明细已拣 、 修改拣货数量是否成功
        boolean pickingBool = false;

        List<WhFbaAllocationItem> whFbaAllocationItems = new ArrayList<>();
        Map<String, Integer> skuMap = new HashMap<>();

        for (WhFbaAllocationItem item : cancelItemList) {

            //已分配未拣货 根据分配数量排序
            List<WhApvOutStockChain> whApvOutStockChainList = fbaAllocation.getWhApvOutStockChainList().stream().filter(a->item.getProductSku().equals(a.getSku()) && (a.getQuantity()!=null && a.getQuantity()>0))
                    .sorted(Comparator.comparing(WhApvOutStockChain::getQuantity).reversed()).collect(Collectors.toList());

            int pickQuantitySum=0;

            int skuQuantity = item.getSkuQuantity() == null ? 0 : item.getSkuQuantity();
            int quantity = item.getQuantity() == null ? 0 : item.getQuantity();
            List<WhFbaAllocationItem> whFbaAllocationItemList = new ArrayList<>();
            //待拣货状态
            if (MapUtils.isNotEmpty(taskSkuMap)) {
                //正在拣货 并且当前取消的sku已拣
                whFbaAllocationItemList=filterFbaItemData(fbaAllocation,item,2);

                //一品多位 ,过滤正在拣货中sku
                List<WhApvOutStockChain> apvOutStockChainList = fbaAllocation.getWhApvOutStockChainList().stream().filter(a->item.getProductSku().equals(a.getSku()) && (a.getQuantity()!=null && a.getQuantity()>0) && (a.getPickQuantity()==null || a.getPickQuantity()<=0))
                        .sorted(Comparator.comparing(WhApvOutStockChain::getQuantity).reversed()).collect(Collectors.toList());

                //sku正在拣货优先退已分配数量，再退已拣数量
                int sum = Optional.ofNullable(apvOutStockChainList).orElse(new ArrayList<>()).stream().mapToInt(WhApvOutStockChain::getQuantity).sum();

                //未拣货数量小于取消数量
                if (CollectionUtils.isNotEmpty(whFbaAllocationItemList) && sum<skuQuantity) {
                    pickQuantitySum=skuQuantity-sum;
                    item.setSkuQuantity(skuQuantity-sum);
                    item.setQuantity(skuQuantity-sum);
                    //任务号状态为拣货中且当前sku明细已拣
                    fbaAllocation.setItems(whFbaAllocationItemList);
                    //修改拣货数量，库存从已拣退回到已拣返架
                    pickingBool = pickingStockAlteration(Arrays.asList(item), fbaAllocation);

                    skuQuantity=sum;
                    quantity=sum;
                }
                if (sum==0){
                    continue;
                }

                if (CollectionUtils.isNotEmpty(apvOutStockChainList)) {
                    whApvOutStockChainList=apvOutStockChainList;
                }

            }
            //待生成状态
            whFbaAllocationItemList=filterFbaItemData(fbaAllocation,item,1);


            if (CollectionUtils.isEmpty(whFbaAllocationItemList)) continue;

            WhFbaAllocationItem whFbaAllocationItem = whFbaAllocationItemList.get(0);
            int nowSkuQuantity = whFbaAllocationItem.getSkuQuantity() == null ? 0 : whFbaAllocationItem.getSkuQuantity()-pickQuantitySum;
            int nowQuantity = whFbaAllocationItem.getQuantity() == null ? 0 : whFbaAllocationItem.getQuantity()-pickQuantitySum;
            int nowAllotQuantity = whFbaAllocationItem.getAllotQuantity() == null ? 0 : whFbaAllocationItem.getAllotQuantity()-pickQuantitySum;
            if (nowSkuQuantity < skuQuantity || nowQuantity < quantity || nowAllotQuantity < skuQuantity) {
                log.error("单号:" +fbaAllocation.getFbaNo() + "sku:" + item.getProductSku() + "取消数件：" + skuQuantity);
                throw new BusinessException("单号:" +fbaAllocation.getFbaNo() + "sku:" + item.getProductSku() + "取消数量不能大于现有数件！");
            }
            int sum = Optional.ofNullable(whApvOutStockChainList).orElse(new ArrayList<>()).stream().mapToInt(WhApvOutStockChain::getQuantity).sum();
            if (sum<skuQuantity) {
                throw new BusinessException("单号:" +fbaAllocation.getFbaNo() + "sku:" + item.getProductSku() + "关联表WhApvOutStockChain已分配数量不能小于取消数量！");
            }

            //修改库存
            this.updateTransferStockList(whApvOutStockChainList,skuQuantity,TransferStockLogType.ALLOCATED.intCode());


            for (WhFbaAllocationItem fbaAllocationItem : whFbaAllocationItemList) {
                //修改fba明细FNSKU、SKU、已分配的数量
                WhFbaAllocationItem updateFbaAllocationItem = new WhFbaAllocationItem();
                updateFbaAllocationItem.setId(fbaAllocationItem.getId());
                updateFbaAllocationItem.setQuantity(nowQuantity - quantity);
                updateFbaAllocationItem.setSkuQuantity(nowSkuQuantity - skuQuantity);
                updateFbaAllocationItem.setAllotQuantity(nowAllotQuantity - skuQuantity);
                whFbaAllocationItems.add(updateFbaAllocationItem);
            }
            //修改拣货任务sku关联表需拣数量
            if (taskSkuMap.containsKey(item.getProductSku())) {
                WhPickingTaskSku whPickingTaskSku = taskSkuMap.get(item.getProductSku());
                int pickingQuantity = whPickingTaskSku.getQuantity() == null ? 0 : whPickingTaskSku.getQuantity();
                whPickingTaskSku.setQuantity(pickingQuantity - skuQuantity);
                updateTaskSkuList.add(whPickingTaskSku);
            }
            //库存变动的fba明细
            skuMap.put(item.getProductSku(), skuQuantity);
        }

        //修改fba明细sku、fnSku数件
        if (CollectionUtils.isNotEmpty(whFbaAllocationItems)) {
            whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(whFbaAllocationItems);
        }
        //修改拣货任务sku关联表需拣数量
        if (CollectionUtils.isNotEmpty(updateTaskSkuList)) {
            whPickingTaskSkuService.batchUpdateWhPickingTaskSku(updateTaskSkuList);
        }
        //添加FBA取消部分sku日志
        if (MapUtils.isNotEmpty(skuMap)) {
            SystemLogUtils.FBAALLOCATIONLOG.log(fbaAllocation.getId(), "部分sku取消：" + JSONObject.toJSONString(skuMap));
            return true;
        }
        //修改拣货数量成功
        if (pickingBool) {
            return true;
        }
        return false;
    }


    public Integer updateTransferStockList( List<WhApvOutStockChain> whApvOutStockChainList, Integer cancelQuantity,Integer type){
        Integer pickReturnQuantitySum=0;

        List<TransferStock> updateStockList = new ArrayList<>();
        List<WhTransitStockLog> stockLogs = new ArrayList<>();
        //需要取消的数量
        Integer quantity = cancelQuantity;
        List<WhApvOutStockChain> whApvOutStockChains=new ArrayList<>();
        for (WhApvOutStockChain whApvOutStockChain : whApvOutStockChainList) {
            if (quantity==0){
                break;
            }
            if (whApvOutStockChain.getStockId()==null){
                throw new BusinessException(String.format("中转仓库存id为空,fba[%s],sku[%s]",whApvOutStockChain.getRelevantNo(),whApvOutStockChain.getSku()) );
            }
            //需要操作的库存数量
            Integer stockQuantity=0;

            //中转仓库存
            TransferStockQueryCondition stockQuery = new TransferStockQueryCondition();
            stockQuery.setId(whApvOutStockChain.getStockId());
            TransferStock whStock = transferStockService.queryTransferStock(stockQuery);
            if (whStock==null){
                throw new BusinessException("库存Id: " + whApvOutStockChain.getStockId() + "库存记录为空");
            }
            if (TransferStockLogType.ALLOCATED.intCode().equals(type) && quantity!=null) {
                //当前库位sku分配的数量
                Integer allQuantity = Optional.ofNullable(whApvOutStockChain.getQuantity()).orElse(0);

                //取消数量大于 分配的数量
                if (quantity>allQuantity) {
                    whApvOutStockChain.setQuantity(0);
                    stockQuantity=allQuantity;

                    quantity-= allQuantity;
                }else{
                    //取消数量小于分配的数量
                    stockQuantity=quantity;

                    whApvOutStockChain.setQuantity(allQuantity-quantity);
                    quantity=0;
                }


                //减 已分配   加 可用
                TransferStock updateStock = TransferStock.buildUpdateStock(whStock);
                //已分配数量
                int allotQuantity = whStock.getAllotQuantity() == null ? 0 : whStock.getAllotQuantity();
                //可用数量
                int surplusQuantity = whStock.getSurplusQuantity() == null ? 0 : whStock.getSurplusQuantity();

                if (allotQuantity < stockQuantity) {
                    log.error("库存Id:"+whApvOutStockChain.getStockId()+" 库存不足：" + whApvOutStockChain.getSku() + "已分配库存" + allotQuantity + "小于取消数量：" + stockQuantity);
                    throw new BusinessException("库存Id:"+whApvOutStockChain.getStockId()+"库存不足：" + whApvOutStockChain.getSku() + "已分配库存" + allotQuantity + "小于取消数量：" + stockQuantity);
                }
                //减 已分配
                updateStock.setAllotQuantity(allotQuantity - stockQuantity);
                //加 可用
                updateStock.setSurplusQuantity(surplusQuantity + stockQuantity);
                updateStockList.add(updateStock);

                //添加可用库存日志
                stockLogs.add(new WhTransitStockLog(whStock.getSku(), updateStock.getStore(), updateStock.getSite(), TransferStockLogType.USABLE_STOCK,
                        whStock.getId(), whStock.getLocationNumber(), StockLogStep.FBA_CANCEL, stockQuantity, surplusQuantity, whApvOutStockChain.getRelevantNo()));
                //添加已分配库存日志
                stockLogs.add(new WhTransitStockLog(whStock.getSku(), updateStock.getStore(), updateStock.getSite(), TransferStockLogType.ALLOCATED,
                        whStock.getId(), whStock.getLocationNumber(), StockLogStep.FBA_CANCEL, -stockQuantity, allotQuantity, whApvOutStockChain.getRelevantNo()));

            }
            if (TransferStockLogType.PICKED_STOCK.intCode().equals(type) && quantity!=null) {

                //当前库位sku已拣货的数量
                Integer outPickQuantity = Optional.ofNullable(whApvOutStockChain.getPickQuantity()).orElse(0);

                //取消数量大于 已拣的数量
                if (quantity>outPickQuantity) {
                    whApvOutStockChain.setPickQuantity(0);
                    stockQuantity=outPickQuantity;
                    quantity-= outPickQuantity;
                }else{
                    //取消数量小于已拣的数量
                    whApvOutStockChain.setPickQuantity(outPickQuantity-quantity);
                    stockQuantity=quantity;
                    quantity=0;
                }
                //已拣数量
                int pickQuantity = whStock.getPickQuantity() == null ? 0 : whStock.getPickQuantity();

                //已拣返架数量
                int pickReturnQuantity = whStock.getPickReturnQuantity() == null ? 0 : whStock.getPickReturnQuantity();
                pickReturnQuantitySum+=pickReturnQuantity;
                if (pickQuantity < stockQuantity) {
                    log.error("库存Id:"+whApvOutStockChain.getStockId()+" 库存不足：" + whApvOutStockChain.getSku() + "已拣库存" + pickQuantity + "小于取消数量：" + stockQuantity);
                    throw new BusinessException("库存Id:"+whApvOutStockChain.getStockId()+"库存不足：" + whApvOutStockChain.getSku() + "已拣库存" + pickQuantity + "小于取消数量：" + stockQuantity);
                }
                //减 已拣库存   加 已拣返架
                TransferStock updateStock = TransferStock.buildUpdateStock(whStock);
                updateStock.setPickQuantity(pickQuantity - stockQuantity);
                updateStock.setPickReturnQuantity(pickReturnQuantity + stockQuantity);
                updateStockList.add(updateStock);

                //添加已拣库存日志
                stockLogs.add(new WhTransitStockLog(whStock.getSku(), updateStock.getStore(), updateStock.getSite(), TransferStockLogType.PICKED_STOCK,
                        whStock.getId(), whStock.getLocationNumber(), StockLogStep.FBA_CANCEL, -stockQuantity, pickQuantity, whApvOutStockChain.getRelevantNo()));
                //添加已拣返架库存日志
                stockLogs.add(new WhTransitStockLog(whStock.getSku(), updateStock.getStore(), updateStock.getSite(), TransferStockLogType.PICKED_RETURN_STOCK,
                        whStock.getId(), whStock.getLocationNumber(), StockLogStep.FBA_CANCEL, stockQuantity, pickReturnQuantity, whApvOutStockChain.getRelevantNo()));



                //当前库位sku已播种数量
                Integer girdQuantity = Optional.ofNullable(whApvOutStockChain.getGirdQuantity()).orElse(0);
                //取消数量大于 已播种数量
                if (quantity>girdQuantity) {
                    whApvOutStockChain.setGirdQuantity(0);
                }else{
                    //取消数量小于已播种数量
                    whApvOutStockChain.setGirdQuantity(girdQuantity-quantity);
                }

            }
            whApvOutStockChains.add(whApvOutStockChain);
        }

        //更新中转仓库存明细
        if (CollectionUtils.isNotEmpty(updateStockList)) {
            transferStockService.batchUpdateTransferStock(updateStockList);
        }
        // 添加库存变动日志
        if (CollectionUtils.isNotEmpty(stockLogs)) {
            stockLogService.batchAddWhStockLog(stockLogs);
        }
        // 添加库存变动批次明细
        transitBatchHandleService.createTransitBatchDetail(stockLogs, QuantityType.CHECK_IN,
                TransitBatchOrderType.CANCEL_ORDER, TransitBatchStockType.FBA, null);
        //更新关联表
        if (CollectionUtils.isNotEmpty(whApvOutStockChains)) {
            whApvOutStockChainService.batchUpdateWhApvOutStockChain(whApvOutStockChains);
        }
        return pickReturnQuantitySum;
    }

    //FBA单分配库存之前
    public boolean allotBefore(List<WhFbaAllocationItem> cancelItemList, WhFbaAllocation fbaAllocation) {
        List<WhFbaAllocationItem> whFbaAllocationItems = new ArrayList<>();
        Map<String, Integer> skuMap = new HashMap<>();
        for (WhFbaAllocationItem item : cancelItemList) {
            int skuQuantity = item.getSkuQuantity() == null ? 0 : item.getSkuQuantity();
            int quantity = item.getQuantity() == null ? 0 : item.getQuantity();
            List<WhFbaAllocationItem> whFbaAllocationItemList =filterFbaItemData(fbaAllocation,item,1);
            if (CollectionUtils.isEmpty(whFbaAllocationItemList)) continue;
            for (WhFbaAllocationItem whFbaAllocationItem : whFbaAllocationItemList) {
                int nowSkuQuantity = whFbaAllocationItem.getSkuQuantity() == null ? 0 : whFbaAllocationItem.getSkuQuantity();
                int nowQuantity = whFbaAllocationItem.getQuantity() == null ? 0 : whFbaAllocationItem.getQuantity();

                if (nowSkuQuantity < skuQuantity || nowQuantity < quantity) {
                    log.error("单号:" +fbaAllocation.getFbaNo() + "sku:" + item.getProductSku() + "取消数件：" + skuQuantity);
                    throw new BusinessException("单号:" +fbaAllocation.getFbaNo() + "sku:" + item.getProductSku() + "取消数量不能大于现有数件！");
                }
                //修改fba明细FNSKU、SKU的数量
                WhFbaAllocationItem updateFbaAllocationItem = new WhFbaAllocationItem();
                updateFbaAllocationItem.setId(whFbaAllocationItem.getId());
                updateFbaAllocationItem.setQuantity(nowQuantity - quantity);
                updateFbaAllocationItem.setSkuQuantity(nowSkuQuantity - skuQuantity);
                whFbaAllocationItems.add(updateFbaAllocationItem);
            }
            skuMap.put(item.getProductSku(), skuQuantity);
        }
        if (CollectionUtils.isNotEmpty(whFbaAllocationItems)) {
            whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(whFbaAllocationItems);
        }
        if (MapUtils.isNotEmpty(skuMap)) {
            SystemLogUtils.FBAALLOCATIONLOG.log(fbaAllocation.getId(), "部分sku取消：" + JSONObject.toJSONString(skuMap));
            return true;
        }
        return false;
    }

    @StockServicelock
    public boolean pickingStockAlteration(List<WhFbaAllocationItem> cancelItemList, WhFbaAllocation fbaAllocation) {
        //sku取消的数件
        Map<String, Integer> skuMap = new HashMap<>();

        //fba明细
        List<WhFbaAllocationItem> whFbaAllocationItems = new ArrayList<>();

        //生成提示
        List<WhFbaChange> whFbaChangeList = new ArrayList<>();

        //播种中的条目
        Map<String, List<WhApvGridItem>> apvGridItemMap = fbaAllocation.getApvGridItemMap();

        //是否存在播种中,取消的sku
        boolean bool = false;
        if (MapUtils.isNotEmpty(apvGridItemMap)) {
            bool = true;
        }
        //需要修改的格子应播数量
        List<WhApvGridItem> updateApvGridItems = new ArrayList<>();

        //修改拣货任务sku关联表需拣数量
        List<WhPickingTaskSku> updateTaskSkuList=new ArrayList<>();
        Map<String, WhPickingTaskSku> taskSkuMap = getPickingTaskSkus(fbaAllocation);
        Integer status = fbaAllocation.getStatus();
        for (WhFbaAllocationItem item : cancelItemList) {
            int skuQuantity = item.getSkuQuantity() == null ? 0 : item.getSkuQuantity();
            int quantity = item.getQuantity() == null ? 0 : item.getQuantity();
            int oldCancelQuantity = Optional.ofNullable(item.getSkuQuantity()).orElse(0);

            List<WhFbaAllocationItem> whFbaAllocationItemList=filterFbaItemData(fbaAllocation,item,1);
            if (CollectionUtils.isEmpty(whFbaAllocationItemList)) continue;

            WhFbaAllocationItem whFbaAllocationItem = whFbaAllocationItemList.get(0);
            int nowSkuQuantity = whFbaAllocationItem.getSkuQuantity() == null ? 0 : whFbaAllocationItem.getSkuQuantity();
            int nowQuantity = whFbaAllocationItem.getQuantity() == null ? 0 : whFbaAllocationItem.getQuantity();
            int nowAllotQuantity = whFbaAllocationItem.getAllotQuantity() == null ? 0 : whFbaAllocationItem.getAllotQuantity();
            int nowPickQuantity = whFbaAllocationItem.getPickQuantity() == null ? 0 : whFbaAllocationItem.getPickQuantity();
            int gridQuantity = Optional.ofNullable(whFbaAllocationItem.getGridQuantity()).orElse(0);

            if (nowSkuQuantity < skuQuantity || nowQuantity < quantity) {
                log.error("单号:" +fbaAllocation.getFbaNo() + "sku:" + item.getProductSku() + "取消数件：" + skuQuantity);
                throw new BusinessException("单号:" +fbaAllocation.getFbaNo() + "sku:" + item.getProductSku() + "取消数量不能大于现有数件！");
            }

            //需返架数量=原拣货数量-部分取消后的发货数量
            //部分取消后的发货数量=原发货数量-部分取消数量
            //部分取消后的拣货数量（新）=原拣货数量-需返架数量
            //当需返架数量小于等于0时，不处理拣货库存
            //需返架数量
            int pickReturnQuantity = nowPickQuantity - (nowAllotQuantity-skuQuantity);

            //当播种差异完成时库存已从已拣移到可用，所以播种完成时需要将原拣货数量换成播种数量
            if (!AsnPrepareStatus.WAITING_GRID.intCode().equals(status)
                && gridQuantity>0) {
                int nowGridQuantity = Math.min(gridQuantity, nowPickQuantity);
                pickReturnQuantity= nowGridQuantity - (nowAllotQuantity-skuQuantity);
            }


            if (pickReturnQuantity<=0){
                continue;
            }
            skuQuantity=pickReturnQuantity;
            quantity=pickReturnQuantity;

            List<WhApvOutStockChain> whApvOutStockChainList = fbaAllocation.getWhApvOutStockChainList().stream().filter(a->item.getProductSku().equals(a.getSku()) && (a.getPickQuantity()!=null && a.getPickQuantity()>0))
                    .sorted(Comparator.comparing(WhApvOutStockChain::getQuantity).reversed()).collect(Collectors.toList());

            int sum = Optional.ofNullable(whApvOutStockChainList).orElse(new ArrayList<>()).stream().mapToInt(WhApvOutStockChain::getPickQuantity).sum();

            if (sum<skuQuantity) {
                throw new BusinessException("单号:" +fbaAllocation.getFbaNo() + "sku:" + item.getProductSku() + "关联表WhApvOutStockChain已拣数量不能小于取消数量！");
            }
            //修改库存
            Integer pickReturnQuantitySum = this.updateTransferStockList(whApvOutStockChainList, skuQuantity, TransferStockLogType.PICKED_STOCK.intCode());

            for (WhFbaAllocationItem fbaAllocationItem : whFbaAllocationItemList) {
                //修改fba明细FNSKU、SKU的数量
                WhFbaAllocationItem updateFbaAllocationItem = new WhFbaAllocationItem();
                updateFbaAllocationItem.setId(fbaAllocationItem.getId());
                updateFbaAllocationItem.setQuantity(Math.max(nowQuantity - oldCancelQuantity, 0));
                updateFbaAllocationItem.setAllotQuantity(Math.max(nowQuantity - oldCancelQuantity, 0));
                updateFbaAllocationItem.setSkuQuantity(Math.max(nowSkuQuantity - oldCancelQuantity, 0));
                updateFbaAllocationItem.setPickQuantity(Math.max(nowPickQuantity - skuQuantity, 0));
                updateFbaAllocationItem.setGridQuantity(Math.max(gridQuantity - skuQuantity, 0));
                whFbaAllocationItems.add(updateFbaAllocationItem);
            }

            //修改播种中的条目
            if (bool && apvGridItemMap!=null) {
                List<WhApvGridItem> whApvGridItemList=null;
                if (fbaAllocation.isFba()) {
                    whApvGridItemList = apvGridItemMap.get(item.getFnSku());
                }else{
                    whApvGridItemList = apvGridItemMap.get(item.getProductSku());
                }
                int finalSkuQuantity = skuQuantity;
                Optional.ofNullable(whApvGridItemList).orElse(new ArrayList<>()).stream().forEach(whApvGridItem->{
                    WhApvGridItem updateApvGridItem = new WhApvGridItem();
                    updateApvGridItem.setId(whApvGridItem.getId());
                    updateApvGridItem.setItemQuantity(nowSkuQuantity - finalSkuQuantity);
                    updateApvGridItem.setPickQuantity(nowPickQuantity - finalSkuQuantity);
                    updateApvGridItems.add(updateApvGridItem);
                });

            }

            //修改拣货任务sku关联表需拣数量
            if (MapUtils.isNotEmpty(taskSkuMap) && taskSkuMap.containsKey(item.getProductSku())) {
                WhPickingTaskSku whPickingTaskSku = taskSkuMap.get(item.getProductSku());
                int needQuantity = whPickingTaskSku.getQuantity() == null ? 0 : whPickingTaskSku.getQuantity();
                int pickingQuantity = whPickingTaskSku.getPickQuantity() == null ? 0 : whPickingTaskSku.getPickQuantity();
                whPickingTaskSku.setQuantity(Math.max(needQuantity - oldCancelQuantity, 0));
                if (PickingTaskSkuStatus.COMPLETED.intCode().equals( whPickingTaskSku.getStatus())) {
                    whPickingTaskSku.setPickQuantity(Math.max(pickingQuantity - oldCancelQuantity, 0));
                }
                updateTaskSkuList.add(whPickingTaskSku);
            }


            //状态为拣货缺货 待装箱 待发货的发货单 生成提醒数据
            if (!AsnPrepareStatus.WAITING_PICK.intCode().equals(fbaAllocation.getStatus())) {
                WhFbaChange whFbaChange = new WhFbaChange();
                List<String> boxNoList = whFbaAllocationItemList.stream().filter(i -> i.getBoxNo()!=null && Optional.ofNullable(i.getLoadingQuantity()).orElse(0) > 0).map(i->i.getBoxNo()+"").collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(boxNoList)) {
                    String boxNo = String.join( "," , boxNoList);
                    whFbaChange.setBoxNo(boxNo);
                }
                whFbaChange.setFbaNo(fbaAllocation.getFbaNo());
                whFbaChange.setShipmentId(fbaAllocation.getShipmentId());
                if (StringUtils.isNotBlank(item.getFnSku())) {
                    whFbaChange.setFnSku(item.getFnSku());
                    whFbaChange.setFnQuantity(quantity);
                }
                whFbaChange.setSku(item.getProductSku());
                whFbaChange.setQuantity(skuQuantity);
                whFbaChange.setReturnQuantity(skuQuantity);
                whFbaChange.setPickReturnQuantity(pickReturnQuantitySum + skuQuantity);
                whFbaChange.setChangeStatus(fbaAllocation.getStatus());
                //发货单状态为待发货状态退回到带装箱状态
                if (AsnPrepareStatus.WAITING_DELIVER.intCode().equals(fbaAllocation.getStatus())) {
                    whFbaChange.setStatus(AsnPrepareStatus.WAITING_BOX.intCode());
                } else {
                    whFbaChange.setStatus(fbaAllocation.getStatus());
                }
                whFbaChange.setChangeTime(new Timestamp(System.currentTimeMillis()));
                FBAChangeDeliveryTypeEnum deliveryTypeEnum = fbaAllocation.isFba() ? FBAChangeDeliveryTypeEnum.FBA_OUT_STOCK
                        : FBAChangeDeliveryTypeEnum.OVERSEA_WAREHOUSE_STOCK;
                whFbaChange.setDeliveryType(deliveryTypeEnum.getCode());
                whFbaChange.setOrderItemStatus(FBAChangeOrderItemStatusEnum.WAITING_DEAL.getCode());
                whFbaChange.setFbaAllocationItemId(whFbaAllocationItem.getId());
                if (Objects.nonNull(whFbaChange.getReturnQuantity()) && !Objects.equals(0, whFbaChange.getReturnQuantity())) {
                    whFbaChange.setCancelType(CancelTypeEnum.SKU_CANCEL.intCode());
                    whFbaChangeList.add(whFbaChange);
                }else{
                    log.info("FbaAllocation_item.id=" + whFbaChange.getFbaAllocationItemId() + ",sku=" + whFbaChange.getSku() + ",应返架数量为0，为此不生成返架记录!");
                }
            }
            skuMap.put(item.getProductSku(), oldCancelQuantity);
        }

        //修改fba sku、fnSku数件
        if (CollectionUtils.isNotEmpty(whFbaAllocationItems)) {
            whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(whFbaAllocationItems);
        }

        //生成提醒数据
        if (CollectionUtils.isNotEmpty(whFbaChangeList)) {
            whFbaChangeService.batchCreateWhFbaChange(whFbaChangeList);
        }

        //修改拣货任务sku关联表需拣数量
        if (CollectionUtils.isNotEmpty(updateTaskSkuList)) {
            whPickingTaskSkuService.batchUpdateWhPickingTaskSku(updateTaskSkuList);
        }

        //修改播种中的应播数量
        if (CollectionUtils.isNotEmpty(updateApvGridItems)) {
            whApvGridItemService.batchUpdateWhApvGridItem(updateApvGridItems);
        }

        //添加fba日志
        if (MapUtils.isNotEmpty(skuMap)) {
            SystemLogUtils.FBAALLOCATIONLOG.log(fbaAllocation.getId(), "部分sku取消：" + JSONObject.toJSONString(skuMap));
            return true;
        }
        return false;
    }

    //获取sku拣货明细
    public Map<String, WhPickingTaskSku> getPickingTaskSkus(WhFbaAllocation fbaAllocation){
        WhPickingTaskQueryCondition whPickingTaskQueryCondition = new WhPickingTaskQueryCondition();
        whPickingTaskQueryCondition.setApvNo(fbaAllocation.getFbaNo());
        whPickingTaskQueryCondition.setTaskStatus(PickingTaskStatus.COMPLETED.intCode());
        whPickingTaskQueryCondition.setQueryGridExpand(true);
        List<WhPickingTask> whPickingTasks = whPickingTaskService.queryWhPickingTasks(whPickingTaskQueryCondition, null);
        if (CollectionUtils.isEmpty(whPickingTasks)) {
           return null;
        }
        WhPickingTaskSkuQueryCondition whPickingTaskSkuQueryCondition = new WhPickingTaskSkuQueryCondition();
        whPickingTaskSkuQueryCondition.setTaskId(whPickingTasks.get(0).getId());
        List<WhPickingTaskSku> whPickingTaskSkus = whPickingTaskSkuService
                .queryWhPickingTaskSkus(whPickingTaskSkuQueryCondition, null);
        if (CollectionUtils.isEmpty(whPickingTasks)) {
           return null;
        }
        return whPickingTaskSkus.stream().collect(Collectors.toMap(WhPickingTaskSku::getSku, a -> a));

    }


    @Override
    public void uploadBoxInfoComplete(WhFbaAllocation whFbaAllocation) {
        WhFbaAllocation update = new WhFbaAllocation();
        update.setId(whFbaAllocation.getId());
        update.setTransitType(1);
        update.setConfirmTime(new Timestamp(System.currentTimeMillis()));
        updateWhFbaAllocation(update);
    }

    // 组装调拨需求
    public WhApvAllocationDemand asseAllDemand(List<WhFbaAllocationData> allocationDatas) {
        WhApvAllocationDemand entity = new WhApvAllocationDemand();
        entity.setOriginalTaskNo(CreateTaskNoUtils.createFbaPickingTaskNo());// 原拣货任务号
        entity.setTaskNo("B" + entity.getOriginalTaskNo());// 拣货任务号
        entity.setOutWarehouseId(PickingTaskWarehouseType.OLD.intCode());// 调出仓
        entity.setInWarehouseId(PickingTaskWarehouseType.FBA.intCode());// 调入仓
        entity.setTaskType(AllocationDemandTaskType.FBA.intCode());// 类型,1=订单调拨,2=FBA调拨
        entity.setWaybillType(1);
        entity.setTaskStatus(AllocationDemandTaskStatus.FINISH.intCode());
        entity.setPrintStatus(AllocationDemandTaskStatus.FINISH.intCode());
        whApvAllocationDemandDaoImpl.createWhApvAllocationDemand(entity);
        Integer taskId = entity.getTaskId();

        List<WhApvAllocationDemandItem> itemList = new ArrayList<>();

        WhApvAllocationDemandItem item = new WhApvAllocationDemandItem();
        item.setApvId(taskId);
        item.setApvNo(allocationDatas.get(0).getAccountNumber());
        item.setApvStatus(AllocationDemandApvStatus.PICK_FINISH.intCode());
        item.setCreateBy(DataContextHolder.getUserId());
        item.setCreatedDate(new Timestamp(System.currentTimeMillis()));
        Map<String, Integer> demandSkuMap = allocationDatas.stream().collect(Collectors.toMap(WhFbaAllocationData::getSku, WhFbaAllocationData::getAllocationQuantity));
        item.setApvItem(JSON.toJSONString(demandSkuMap));
        itemList.add(item);


        List<WhApvAllocationDemandSku> demandSkuList = new ArrayList<>();
        for (String key : demandSkuMap.keySet()) {
            WhApvAllocationDemandSku demandSku = new WhApvAllocationDemandSku();
            demandSku.setTaskId(taskId);
            demandSku.setSku(key);
            demandSku.setQuantity(demandSkuMap.get(key));
            demandSku.setPickQuantity(0);
            demandSku.setCreationDate(new Timestamp(System.currentTimeMillis()));
            demandSku.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            demandSku.setStatus(PickingTaskSkuStatus.COMPLETED.intCode());
            demandSkuList.add(demandSku);
        }

        for (WhApvAllocationDemandItem itemEntity : itemList) {
            itemEntity.setTaskId(taskId);
        }
        whApvAllocationDemandDaoImpl.batchCreateWhApvAllocationDemandItem(itemList);
        for (WhApvAllocationDemandSku itemEntity : demandSkuList) {
            itemEntity.setTaskId(taskId);
        }
        whApvAllocationDemandDaoImpl.batchCreateWhApvAllocationDemandSku(demandSkuList);
        return entity;
    }

    // 交运
    @Override
    public ResponseJson doDeliver(String fbaNo) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setFbaNo(fbaNo);
        query.setQueryWhAsnExtra(true);
        query.setStatus(AsnPrepareStatus.WAITING_DELIVER.intCode());
        List<WhFbaAllocation> whFbaAllocations = queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isNotEmpty(whFbaAllocations)) {
            response = this.doDeliver(whFbaAllocations);
            response.setExceptionCode(whFbaAllocations.get(0).getShippingOrderNo());
            if (StatusCode.FAIL.equalsIgnoreCase(response.getStatus()))
                return response;
        }
        // FBA新流程交运
        WhFbaShipmentQueryCondition shipmentQuery = new WhFbaShipmentQueryCondition();
        shipmentQuery.setShipmentId(fbaNo);
        shipmentQuery.setStatus(AsnPrepareStatus.WAITING_DELIVER.intCode());
        List<WhFbaShipment> whFbaShipments = whFbaShipmentService.queryWhFbaShipmentAndItems(shipmentQuery, null);
        if (CollectionUtils.isNotEmpty(whFbaShipments)) {
            WhFbaShipment fbaShipment = whFbaShipments.get(0);
            WhFbaAllocationQueryCondition fbaQuery = new WhFbaAllocationQueryCondition();
            fbaQuery.setId(fbaShipment.getFbaId());
            WhFbaAllocation fbaAllocation = this.getWhFbaAllocation(fbaShipment.getFbaId());
            if (fbaAllocation == null) {
                response.setMessage("未找到FBA调拨单信息，fbaId:" + fbaShipment.getFbaId());
                return response;
            }
            fbaAllocation.setPlanNo(fbaAllocation.getShipmentId());
            fbaAllocation.setShipmentId(fbaShipment.getShipmentId());
            fbaAllocation.setTrackingNumber(fbaShipment.getTrackingNumberByTms());
            fbaAllocation.setShippingMethod(fbaShipment.getShippingMethodByTms());
            fbaAllocation.setBatNo(fbaShipment.getBatNo());
            fbaAllocation.setItems(fbaShipment.getItems());
            WhAsnExtra whAsnExtra = new WhAsnExtra();
            whAsnExtra.setZipcode(fbaShipment.getZipcode());
            whAsnExtra.setReceiptCountry(fbaShipment.getReceiptCountry());
            whAsnExtra.setReceiptArea(fbaShipment.getReceiptArea());
            whAsnExtra.setReceiptCity(fbaShipment.getReceiptCity());
            whAsnExtra.setReceiptAddress(fbaShipment.getReceiptAddress());
            whAsnExtra.setReceiptAddress2(fbaShipment.getReceiptAddress2());
            whAsnExtra.setReceiptPerson(fbaShipment.getReceiptPerson());
            fbaAllocation.setWhAsnExtra(whAsnExtra);
            // 交运
            response = this.doDeliver(Collections.singletonList(fbaAllocation));
            response.setExceptionCode(whFbaShipments.get(0).getShippingOrderNo());
            if (StatusCode.FAIL.equalsIgnoreCase(response.getStatus()))
                return response;
            // 更新货件单交运信息
            WhFbaShipment updateShipment = new WhFbaShipment();
            updateShipment.setId(fbaShipment.getId());
            updateShipment.setStatus(AsnPrepareStatus.DELIVER.intCode());
            updateShipment.setDeliverBy(DataContextHolder.getUserId());
            updateShipment.setDeliverTime(new Timestamp(System.currentTimeMillis()));
            whFbaShipmentService.updateWhFbaShipment(updateShipment);
            SystemLogUtils.FBASHIPMENTLOG.log(fbaShipment.getId(), "海外仓出库单交运",
                    new String[][]{{"追踪号", fbaShipment.getTrackingNumber()}, {"物流公司", fbaShipment.getShippingMethod()}});
        }
        // 匹配大包发货单
        WhApvQueryCondition whApvQueryCondition = new WhApvQueryCondition();
        whApvQueryCondition.setApvNo(fbaNo);
        whApvQueryCondition.setExpress(true);
        List<WhApv> whApvs = whApvService.queryWhApvAndItemList(whApvQueryCondition, null);
        if (CollectionUtils.isNotEmpty(whApvs)) {
            ResponseJson responseJson = this.doBaleDeliver(whApvs);
            if (StatusCode.FAIL.equalsIgnoreCase(responseJson.getStatus()))
                return responseJson;
            // 推送给TMS
            for(WhApv whApv:whApvs){
                apvExpressService.sendMsg(whApv,TmsSendMsgType.SEND_ORDER_STATUS.intCode(), AsnStatus.DELIVER.intCode());
            }
            return responseJson;
        }
        if (CollectionUtils.isEmpty(whFbaShipments) && CollectionUtils.isEmpty(whApvs)) {
            response.setMessage("未找到发货单！");
            return response;
        }
        return response;
    }

    // 大包发货单交运
    public ResponseJson doBaleDeliver(List<WhApv> whApvs) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        for (WhApv whApv:whApvs) {
            if (ApvStatus.WAITING_DELIVER.intCode() < whApv.getStatus()) {
                response.setMessage("重复交运！");
                response.setExceptionCode(PdaExceptionCode.VOICE_ERROR);
                return response;
            }
            if (ApvStatus.CANCEL.equals(whApv.getStatus())) {
                response.setMessage("发货单为已取消发货状态！");
                return response;
            }
            if (!ApvStatus.WAITING_DELIVER.equals(whApv.getStatus())) {
                response.setMessage("发货单不是等待发货状态！");
                return response;
            }

            //有拦截标签
            if (whApv.existBuyerCheckout(ApvTaxTypeEnum.INTERCEPTOR) || whApv.existBuyerCheckout(ApvTaxTypeEnum.QC_INTERCEPTOR)) {
                response.setMessage("发货单["+whApv.getApvNo()+"]存在拦截标签！");
                return response;
            }
            ApvExpress apvExpress = whApv.getApvExpress();
            if (apvExpress == null) {
                response.setMessage("大包发货单数据未初始化！");
                return response;
            }
            if (!ApvExpressStatus.CONFIRM.intCode().equals(apvExpress.getStatus())) {
                if (Objects.equals(ApvExpressStatus.UN_CONFIRM.intCode(), apvExpress.getStatus())) {
                    response.setMessage("快递发货单为待确认状态!");
                    return response;
                }
                response.setMessage("快递发货单不是可发货状态！");
                return response;
            }
            List<WhApvItem> items = whApv.getWhApvItems();
            WhApv apv = new WhApv();
            apv.setApvNo(whApv.getApvNo());
            apv.setId(whApv.getId());
            apv.setStatus(whApv.getStatus());
            apv.setWhApvItems(items);
            //apv.setTrackingNumber(whApv.getTrackingNumber());
            // 快递重量记录的是g
            if (apvExpress.getWeight() != null) {
                apv.setActualWeight(apvExpress.getWeight());
            }
            //先改状态，再进行库存操作
            if (!apvStatusUpdateService.deliverWhApvStatus(apv.getId(), 10, apv.getActualWeight())) {
                response.setMessage("重复交运！");
                response.setExceptionCode(PdaExceptionCode.VOICE_ERROR);
                return response;
            }
            apvStatusUpdateService.deliverByBtn(apv);
        }
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage("交运成功");
        return response;
    }
    // fba交运
    public ResponseJson doDeliver(List<WhFbaAllocation> allocations) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        List<WhFbaAllocation> updateWhFbaAllocations = new ArrayList<>();
        Map<String, Integer> apvIdMap = new HashMap<>();
        Map<String, List<String>> apvSkuMap = new HashMap<>();
        for (WhFbaAllocation allocation : allocations) {
            WhFbaAllocation update = new WhFbaAllocation();
            update.setId(allocation.getId());
            update.setStatus(AsnPrepareStatus.DELIVER.intCode());
            update.setDeliverBy(DataContextHolder.getUserId());
            update.setDeliverTime(new Timestamp(System.currentTimeMillis()));
            if (!allocation.isFba()) {
                update.setTrackingNumber(allocation.getTrackingNumber());
                update.setShippingMethod(allocation.getShippingMethod());
                // 改库存
                //此处一个货件修改库存，防止同批交运中，包含同一店铺中同一sku的不同货件
                whFbaAllocationStockService.deliverBySelf(allocation);
                updateWhFbaAllocations.add(update);
            } else {  // TODO liurui FBA逻辑临时注释
                // fba多个货件若其中一个已装车 则其它货件交运不改主表交运信息
                if (allocation.getStatus() != null && allocation.getStatus() < AsnPrepareStatus.LOADED.intCode()){
                    updateWhFbaAllocations.add(update);
                }
                whFbaAllocationStockService.deliverFBABySelf(allocation);
            }
            // 获取保质期发货单信息
            List<String> skus = allocation.buildGroupItems().stream().map(a -> a.getProductSku()).collect(Collectors.toList());
            List<String> expSkuList = GetSkuQcCategoryDescUtil.getExpSkuList(skus);
            if (CollectionUtils.isNotEmpty(expSkuList)) {
                apvIdMap.put(allocation.getFbaNo(), allocation.getId());
                apvSkuMap.put(allocation.getFbaNo(), skus);
            }
        }
        // 交运时间
        if (CollectionUtils.isNotEmpty(updateWhFbaAllocations)) {
            whFbaAllocationDao.batchUpdateWhFbaAllocation(updateWhFbaAllocations);
        }
        // 生成批次
        if (apvIdMap.size() > 0 && apvSkuMap.size() > 0) {
            expManageService.calcDeliverQty(apvIdMap, apvSkuMap);
        }
        //
        for (WhFbaAllocation allocation : allocations) {
            SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "海外仓出库单交运",
                    new String[][]{{"追踪号", allocation.getTrackingNumber()}, {"物流公司", allocation.getShippingMethod()}});
            //发消息到oms
            allocation.setStatus(AsnPrepareStatus.DELIVER.intCode());
            allocation.setDeliverTime(new Timestamp(System.currentTimeMillis()));
            try {
                sendMsg(allocation);
            } catch (Exception e) {
                log.error("deliver fba sendMsg error:" + e.getMessage(), e);
            }


            // 关联唯一码
            WhUniqueSkuQueryCondition query = new WhUniqueSkuQueryCondition();
            query.setApvNo(allocation.getFbaNo());
            List<WhUniqueSku> uniqueList = whUniqueSkuService.queryWhUniqueSkus(query, null);
            List<WhUniqueSkuLog> whUniqueSkuLogList = new ArrayList<>();
            for (WhUniqueSku unique : uniqueList) {
                WhUniqueSkuLog uniqueSkuLog = new WhUniqueSkuLog();
                uniqueSkuLog.setUniqueId(unique.getId());
                uniqueSkuLog.setStep(UniqueSkuStep.FBA_DELIVER.intCode());
                uniqueSkuLog.setOrderNo(allocation.getFbaNo());
                uniqueSkuLog.setContent("FBA交运： " + allocation.getFbaNo());
                whUniqueSkuLogList.add(uniqueSkuLog);
            }
            // 异步添加日志
            whUniqueSkuLogService.asyncCreateWhUniqueSkuLog(whUniqueSkuLogList);
        }
        // TODO 根据批次号发送交运状态到TMS
        sendMsgToTms(allocations.get(0), TmsSendMsgType.SEND_ORDER_STATUS.intCode());

        if (allocations.get(0).isFba()) {
            response.setMessage("交运成功!");
        }
        else {
            WhAsn asn = BeanConvertUtils.convert(allocations.get(0), WhAsn.class);
            asn.setReceivingCode(allocations.get(0).getFbaNo());
            asn.setTrackingNumber(allocations.get(0).getTrackingNumber());
            asn.setBoxTotal(allocations.get(0).getBoxTotal());
            response.setMessage(JSON.toJSONString(asn));
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    // fba交运
    @Override
    public ResponseJson doDeliverRetry(List<WhFbaAllocation> allocations, Date deliverDate) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        List<WhFbaAllocation> updateWhFbaAllocations = new ArrayList<>();
        Map<String, Integer> apvIdMap = new HashMap<>();
        Map<String, List<String>> apvSkuMap = new HashMap<>();
        for (WhFbaAllocation allocation : allocations) {
            WhFbaAllocation update = new WhFbaAllocation();
            update.setId(allocation.getId());
            update.setStatus(AsnPrepareStatus.DELIVER.intCode());
            update.setDeliverBy(DataContextHolder.getUserId());
            update.setDeliverTime(new Timestamp(deliverDate.getTime()));
            if (!allocation.isFba()) {
                update.setTrackingNumber(allocation.getTrackingNumber());
                update.setShippingMethod(allocation.getShippingMethod());
            }
            // 改库存
            //此处一个货件修改库存，防止同批交运中，包含同一店铺中同一sku的不同货件
            whFbaAllocationStockService.deliverBySelf(allocation);
            updateWhFbaAllocations.add(update);
            // 获取保质期发货单信息
            List<String> skus = allocation.buildGroupItems().stream().map(a -> a.getProductSku()).collect(Collectors.toList());
            List<String> expSkuList = GetSkuQcCategoryDescUtil.getExpSkuList(skus);
            if (CollectionUtils.isNotEmpty(expSkuList)) {
                apvIdMap.put(allocation.getFbaNo(), allocation.getId());
                apvSkuMap.put(allocation.getFbaNo(), skus);
            }
        }
        // 交运时间
        if (CollectionUtils.isNotEmpty(updateWhFbaAllocations)) {
            whFbaAllocationDao.batchUpdateWhFbaAllocation(updateWhFbaAllocations);
        }
        // 生成批次
        if (apvIdMap.size() > 0 && apvSkuMap.size() > 0) {
            expManageService.calcDeliverQty(apvIdMap, apvSkuMap);
        }
        //
        for (WhFbaAllocation allocation : allocations) {
            SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "海外仓出库单交运",
                    new String[][]{{"追踪号", allocation.getTrackingNumber()}, {"物流公司", allocation.getShippingMethod()}});
            //发消息到oms
            allocation.setStatus(AsnPrepareStatus.DELIVER.intCode());
            allocation.setDeliverTime(new Timestamp(deliverDate.getTime()));
            try {
                sendMsg(allocation);
            } catch (Exception e) {
                log.error("deliver fba sendMsg error:" + e.getMessage(), e);
            }

            // 关联唯一码
            WhUniqueSkuQueryCondition query = new WhUniqueSkuQueryCondition();
            query.setApvNo(allocation.getFbaNo());
            List<WhUniqueSku> uniqueList = whUniqueSkuService.queryWhUniqueSkus(query, null);
            List<WhUniqueSkuLog> whUniqueSkuLogList = new ArrayList<>();
            for (WhUniqueSku unique : uniqueList) {
                WhUniqueSkuLog uniqueSkuLog = new WhUniqueSkuLog();
                uniqueSkuLog.setUniqueId(unique.getId());
                uniqueSkuLog.setStep(UniqueSkuStep.FBA_DELIVER.intCode());
                uniqueSkuLog.setOrderNo(allocation.getFbaNo());
                uniqueSkuLog.setContent("FBA交运： " + allocation.getFbaNo());
                whUniqueSkuLogList.add(uniqueSkuLog);
            }
            // 异步添加日志
            whUniqueSkuLogService.asyncCreateWhUniqueSkuLog(whUniqueSkuLogList);
        }
        // TODO 根据批次号发送交运状态到TMS
        sendMsgToTms(allocations.get(0), TmsSendMsgType.SEND_ORDER_STATUS.intCode());

        if (allocations.get(0).isFba()) {
            response.setMessage("交运成功!");
        }
        else {
            WhAsn asn = BeanConvertUtils.convert(allocations.get(0), WhAsn.class);
            asn.setReceivingCode(allocations.get(0).getFbaNo());
            asn.setTrackingNumber(allocations.get(0).getTrackingNumber());
            asn.setBoxTotal(allocations.get(0).getBoxTotal());
            response.setMessage(JSON.toJSONString(asn));
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @Override
    public ResponseJson doLoad(String fbaNo) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setFbaNo(fbaNo);
        query.setStatus(AsnPrepareStatus.DELIVER.intCode());
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> whFbaAllocations = queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isNotEmpty(whFbaAllocations)) {
            response = this.doLoad(whFbaAllocations);
            if (StatusCode.FAIL.equalsIgnoreCase(response.getStatus()))
                return response;
        }
        // FBA新流程装车
        WhFbaShipmentQueryCondition shipmentQuery = new WhFbaShipmentQueryCondition();
        shipmentQuery.setShipmentId(fbaNo);
        query.setStatus(AsnPrepareStatus.DELIVER.intCode());
        List<WhFbaShipment> whFbaShipments = whFbaShipmentService.queryWhFbaShipmentAndItems(shipmentQuery, null);
        if (CollectionUtils.isNotEmpty(whFbaShipments)) {
            WhFbaShipment fbaShipment = whFbaShipments.get(0);
            WhFbaAllocationQueryCondition fbaQuery = new WhFbaAllocationQueryCondition();
            fbaQuery.setId(fbaShipment.getFbaId());
            WhFbaAllocation fbaAllocation = this.getWhFbaAllocation(fbaShipment.getFbaId());
            if (fbaAllocation == null) {
                response.setMessage("未找到FBA调拨单信息，fbaId:" + fbaShipment.getFbaId());
                return response;
            }
            fbaAllocation.setPlanNo(fbaAllocation.getShipmentId());
            fbaAllocation.setShipmentId(fbaShipment.getShipmentId());
            fbaAllocation.setPurposeHouse(fbaShipment.getPurposeHouse());
            fbaAllocation.setTrackingNumber(fbaShipment.getTrackingNumber());
            fbaAllocation.setShippingCompany(fbaShipment.getShippingCompany());
            fbaAllocation.setShippingMethod(fbaShipment.getShippingMethod());
            fbaAllocation.setShippingMethodByTms(fbaShipment.getShippingMethodByTms());
            fbaAllocation.setBatNo(fbaShipment.getBatNo());
            fbaAllocation.setItems(fbaShipment.getItems());
            WhAsnExtra whAsnExtra = new WhAsnExtra();
            whAsnExtra.setZipcode(fbaShipment.getZipcode());
            whAsnExtra.setReceiptCountry(fbaShipment.getReceiptCountry());
            whAsnExtra.setReceiptArea(fbaShipment.getReceiptArea());
            whAsnExtra.setReceiptCity(fbaShipment.getReceiptCity());
            whAsnExtra.setReceiptAddress(fbaShipment.getReceiptAddress());
            whAsnExtra.setReceiptAddress2(fbaShipment.getReceiptAddress2());
            whAsnExtra.setReceiptPerson(fbaShipment.getReceiptPerson());
            fbaAllocation.setWhAsnExtra(whAsnExtra);
            // 装车
            response = this.doLoad(Collections.singletonList(fbaAllocation));
            if (StatusCode.FAIL.equalsIgnoreCase(response.getStatus()))
                return response;
            // 更新货件单交运信息
            WhFbaShipment updateShipment = new WhFbaShipment();
            updateShipment.setId(fbaShipment.getId());
            updateShipment.setStatus(AsnPrepareStatus.LOADED.intCode());
            updateShipment.setLoadBy(DataContextHolder.getUserId());
            updateShipment.setLoadTime(new Timestamp(System.currentTimeMillis()));
            whFbaShipmentService.updateWhFbaShipment(updateShipment);
            SystemLogUtils.FBASHIPMENTLOG.log(fbaShipment.getId(), "海外仓出库单装车");
        }
        // 匹配大包发货单
        WhApvQueryCondition whApvQueryCondition = new WhApvQueryCondition();
        whApvQueryCondition.setApvNo(fbaNo);
        whApvQueryCondition.setExpress(true);
        List<WhApv> whApvs = whApvService.queryWhApvAndItemList(whApvQueryCondition, null);
        if (CollectionUtils.isNotEmpty(whApvs)) {
            ResponseJson responseJson = this.doBaleLoad(whApvs);
            if (StatusCode.FAIL.equalsIgnoreCase(responseJson.getStatus()))
                return responseJson;
            // 推送给TMS，异步执行以提高速度
            final String operationId = DataContextHolder.getOperationId();
            for(WhApv whApv:whApvs){
                executorServices.execute(() -> {
                    ExecutorUtils.setThreadSuffix("doLoad.sendMsg");
                    DataContextHolder.setOperationId(operationId);
                    apvExpressService.sendMsg(whApv, TmsSendMsgType.SEND_ORDER_STATUS.intCode(), ApvStatus.LOADED.intCode());
                });
            }
            return responseJson;
        }
        if (CollectionUtils.isEmpty(whFbaAllocations) && CollectionUtils.isEmpty(whApvs)) {
            response.setMessage("未找到发货单！");
            return response;
        }
        return response;
    }

    private WhFbaAllocation findValidFbaAllocation(WhFbaAllocationQueryCondition queryCondition,WhApvGoodsDo domain) {
        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationDao.scanMaxPriorityGoodsPrintFba(queryCondition);
        for (WhFbaAllocation fbaAllocation : whFbaAllocationList) {
            if (!fbaAllocation.isShein()) {
                return fbaAllocation;
            }
            String errorMsg = packExceptionUuidItemService.checkFbaGpsrTag(fbaAllocation.getFbaNo(), queryCondition.getSku());
            if (StringUtils.isBlank(errorMsg)) {
                return fbaAllocation;
            }
            domain.setErrorMsg(errorMsg);
        }
        return null;
    }



    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public WhFbaAllocation scanMaxPriorityGoodsPrintFba(WhFbaAllocationQueryCondition queryCondition,WhApvGoodsDo domain) {
        WhFbaAllocation whFbaAllocation=findValidFbaAllocation(queryCondition, domain);
        if (Objects.isNull(whFbaAllocation)){
            //兼容海外仓
            String apvType = queryCondition.getApvType();
            queryCondition.setApvType(null);
            queryCondition.setQueryAsnFirst(true);
            whFbaAllocation = findValidFbaAllocation(queryCondition, domain);
        }
        
        if (Objects.isNull(whFbaAllocation)){
//            throw new RuntimeException(String.format("扫描sku:%s,没有查询到中转仓发货单号",queryCondition.getSku()));
            return null;
        }
        WhFbaAllocation splitApv = null;
        if (whFbaAllocation.getIsAsn() != null && whFbaAllocation.getIsAsn() && whFbaAllocation.getTransitType() != null
                && whFbaAllocation.getTransitType() == 1) {
            splitApv = BeanConvertUtils.convert(whFbaAllocation, WhFbaAllocation.class);
            queryCondition.setPackCheckSplit(false);
            whFbaAllocation = findValidFbaAllocation(queryCondition, domain);
        }
        boolean normalApvNull = Objects.isNull(whFbaAllocation);
        if (normalApvNull){
            whFbaAllocation = splitApv;
        }
        domain.setErrorMsg(null);
        WhFbaAllocationItemQueryCondition query=new WhFbaAllocationItemQueryCondition();
        query.setFbaId(whFbaAllocation.getId());
        List<WhFbaAllocationItem> whFbaAllocationItemList = whFbaAllocationItemService.queryWhFbaAllocationItems(query,null);

        if (CollectionUtils.isEmpty(whFbaAllocationItemList) || Objects.isNull(whFbaAllocationItemList.get(0))){
            throw new RuntimeException(String.format("发货单号：%s,没有查询到该sku:%s",whFbaAllocation.getFbaNo(),queryCondition.getSku()));
        }

        if (normalApvNull) {
            boolean moreProduct = StringUtils.equals("MM", queryCondition.getApvType());
            Map<String, Integer> skuMap = whFbaAllocationItemList.stream()
                    .collect(Collectors.groupingBy(WhFbaAllocationItem::getProductSku,
                            Collectors.summingInt(i -> moreProduct ? Optional.ofNullable(i.getGridQuantity()).orElse(0)
                                    : Optional.ofNullable(i.getPickQuantity()).orElse(0))));
            throw new RuntimeException(String.format("该SKU对应的发货单:%s，%s已标记拆包，请将SKU放回周转筐", whFbaAllocation.getFbaNo(),
                    JSON.toJSONString(skuMap)));
        }

        WhFbaAllocationItem whFbaAllocationItem=whFbaAllocationItemList.get(0);

        if ("SS".equals(queryCondition.getApvType())){
            WhFbaAllocation updateFba=new WhFbaAllocation();
            Integer updateStatus = whFbaAllocation.getAsnFirst() ? AsnPrepareStatus.WAITING_BOX.intCode()
                    : AsnPrepareStatus.WAITING_DELIVER.intCode();
            updateFba.setStatus(updateStatus);
            updateFba.setId(whFbaAllocation.getId());
            updateFba.setBoxPushBy(DataContextHolder.getUserId());
            updateFba.setBoxPushTime(new Timestamp(System.currentTimeMillis()));
            whFbaAllocationDao.updateWhFbaAllocation(updateFba);

            WhFbaAllocationItem update=new WhFbaAllocationItem();
            update.setId(whFbaAllocationItem.getId());
            update.setLoadingQuantity(1);
            update.setLoadNum(1);
            whFbaAllocationItemService.updateWhFbaAllocationItem(update);
            //记录包装时间
            addPackTrack(whFbaAllocation.getFbaNo());

            SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), "包装发货单状态变更",
                    new String[][] {
                            { "历史状态", AsnPrepareStatus.getNameByCode(whFbaAllocation.getStatus()+"") },
                            { "更改状态", AsnPrepareStatus.getNameByCode(updateStatus+"")} });
        }
        List<String> skuList = whFbaAllocationItemList.stream().map(WhFbaAllocationItem::getProductSku).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuList)) {
            throw new RuntimeException(String.format("发货单号：%s,没有查询到明细sku",whFbaAllocation.getFbaNo()));
        }

        WhSkuQueryCondition skuQueryCondition=new WhSkuQueryCondition();
        skuQueryCondition.setSkus(skuList);
        List<WhSku> whSkus = whSkuService.queryWhSkus(skuQueryCondition, null);
        Map<String, WhSku> skuMap = whSkus.stream().collect(Collectors.toMap(WhSku::getSku, s -> s));
        for (WhFbaAllocationItem fbaAllocationItem : whFbaAllocationItemList) {
            if (MapUtils.isEmpty(skuMap) || !skuMap.containsKey(fbaAllocationItem.getProductSku())) {
                continue;
            }
            fbaAllocationItem.setWhSku(skuMap.get(fbaAllocationItem.getProductSku()));

        }
        whFbaAllocation.setItems(whFbaAllocationItemList);
        // List<WhSkuWithPmsInfo> pmsSkuInfoList = ApvPackUtils.getPmsSkuInfoList(Arrays.asList(queryCondition.getSku()));
        // domain.setWhSkuWithPmsInfos(pmsSkuInfoList);
        ApvPackUtils.getPmsInfosAndSkuTag(domain, Collections.singletonList(queryCondition.getSku()));
        return whFbaAllocation;
    }

    @Override
    public void revokeScanedWhApvByStatus(Integer apvId, Integer beforeStatus) {
        Assert.notNull(beforeStatus);

        // 修改订单状态
        WhFbaAllocation whFbaAllocation = new WhFbaAllocation();
        whFbaAllocation.setId(apvId);

        // 上一个状态
        whFbaAllocation.setStatus(beforeStatus);

        try {
            whFbaAllocationDao.updateWhFbaAllocation(whFbaAllocation);
        }
        catch (SqlerException e) {
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void passBasketScan(Integer apvId) {
        WhFbaAllocationQueryCondition queryCondition=new WhFbaAllocationQueryCondition();
        queryCondition.setId(apvId);
        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationDao.queryWhFbaAllocationAndItems(queryCondition, null);
        if (CollectionUtils.isEmpty(whFbaAllocationList)) {
            throw new RuntimeException("发货单不存在！");
        }
        WhFbaAllocation whFbaAllocation = whFbaAllocationList.get(0);

        WhFbaAllocation updateFba=new WhFbaAllocation();
        updateFba.setId(whFbaAllocation.getId());
        Integer updateStatus = whFbaAllocation.getAsnFirst() ? AsnPrepareStatus.WAITING_BOX.intCode()
                : AsnPrepareStatus.WAITING_DELIVER.intCode();
        updateFba.setStatus(updateStatus);
        updateFba.setBoxPushBy(DataContextHolder.getUserId());
        updateFba.setBoxPushTime(new Timestamp(System.currentTimeMillis()));
        whFbaAllocationDao.updateWhFbaAllocation(updateFba);

        List<WhFbaAllocationItem> items = whFbaAllocation.getItems();
        if (CollectionUtils.isEmpty(items)) {
            throw new RuntimeException("发货单明细不存在！");
        }
        List<WhFbaAllocationItem> updateList = new ArrayList<>();
        items.forEach(item -> {
            WhFbaAllocationItem update = new WhFbaAllocationItem();
            update.setId(item.getId());
            update.setLoadingQuantity(item.getPickQuantity());
            update.setLoadNum(item.getPickQuantity());
            if (item.getSuitFlag() != null && item.getSuitFlag() == 1)
                update.setLoadNum((int) Math.floor((double) Optional.ofNullable(item.getPickQuantity()).orElse(0)
                        / Optional.ofNullable(item.getQuantity()).orElse(1)));
            updateList.add(update);
        });
        whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(updateList);
        //包装完成解绑周转筐
        whFbaAllocationHandleService.boxFinishUnbindBoxNo(whFbaAllocation);
        //记录包装时间
        addPackTrack(whFbaAllocation.getFbaNo());

        SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), "包装发货单状态变更",
                new String[][] {
                        { "历史状态", AsnPrepareStatus.getNameByCode(whFbaAllocation.getStatus()+"") },
                        { "更改状态", AsnPrepareStatus.getNameByCode(updateStatus+"")} });
    }

    @Override
    public void passMoreProductScan(WhFbaAllocation whFbaAllocation) {
        WhFbaAllocation updateFbaAllocation=new WhFbaAllocation();
        updateFbaAllocation.setId(whFbaAllocation.getId());
        Integer updateStatus = whFbaAllocation.getAsnFirst() ? AsnPrepareStatus.WAITING_BOX.intCode()
                : AsnPrepareStatus.WAITING_DELIVER.intCode();
        updateFbaAllocation.setStatus(updateStatus);
        updateFbaAllocation.setBoxPushBy(DataContextHolder.getUserId());
        updateFbaAllocation.setBoxPushTime(new Timestamp(System.currentTimeMillis()));
        whFbaAllocationDao.updateWhFbaAllocation(updateFbaAllocation);
        List<WhFbaAllocationItem> items = whFbaAllocation.getItems();
        if (CollectionUtils.isEmpty(items)) {
            throw new RuntimeException("发货单号："+whFbaAllocation.getFbaNo()+" 明细数据不存在");
        }
        List<WhFbaAllocationItem> updateList=new ArrayList<>();
        for (WhFbaAllocationItem item : items) {
            WhFbaAllocationItem itemUpdate = new WhFbaAllocationItem();
            itemUpdate.setId(item.getId());
            itemUpdate.setLoadingQuantity(item.getGridQuantity());
            itemUpdate.setLoadNum(item.getGridQuantity());
            if (item.getSuitFlag() != null && item.getSuitFlag() == 1)
                itemUpdate.setLoadNum((int) Math.floor((double) Optional.ofNullable(item.getGridQuantity()).orElse(0)
                        / Optional.ofNullable(item.getQuantity()).orElse(1)));
            updateList.add(itemUpdate);
        }
        whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(updateList);
        //记录包装时间
        addPackTrack(whFbaAllocation.getFbaNo());

        SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), "包装发货单状态变更",
                new String[][] {
                        { "历史状态", AsnPrepareStatus.getNameByCode(whFbaAllocation.getStatus()+"") },
                        { "更改状态", AsnPrepareStatus.getNameByCode(updateStatus+"")} });

    }

    /**
     * 记录包装时间
     * @param apvNo
     */
    private void addPackTrack(String apvNo){
        if (StringUtils.isBlank(apvNo))
            return;
        ApvTrackQueryCondition trackQuery = new ApvTrackQueryCondition();
        trackQuery.setApvNo(apvNo);
        List<ApvTrack> apvTracks = apvTrackService.queryApvTracks(trackQuery, null);
        //没有apv轨迹
        if (CollectionUtils.isEmpty(apvTracks)) {
            ApvTrack apvTrack = new ApvTrack();
            apvTrack.setApvNo(apvNo);
            apvTrack.setPackUser(DataContextHolder.getUserId());
            apvTrack.setPackFinishTime(new Timestamp(System.currentTimeMillis()));
            apvTrackService.createApvTrack(apvTrack);

        }
        else {
            // 包装完成
            ApvTrack apvTrack = new ApvTrack();
            apvTrack.setApvNo(apvNo);
            apvTrack.setPackUser(DataContextHolder.getUserId());
            apvTrack.setPackFinishTime(new Timestamp(System.currentTimeMillis()));
            apvTrackService.updateApvTrackByApvNo(apvTrack);
        }
    }

    // FBA装车
    public ResponseJson doBaleLoad(List<WhApv> whApvs) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        final String operationId = DataContextHolder.getOperationId();
        for (WhApv whApv:whApvs) {
            if (!ApvStatus.DELIVER.equals(whApv.getStatus())) {
                response.setMessage("发货单不是已交运状态！");
                return response;
            }
            ApvExpress apvExpress = whApv.getApvExpress();
            if (apvExpress == null) {
                response.setMessage("快递发货单数据未初始化！");
                return response;
            }
            executorServices.execute(() -> {
                ExecutorUtils.setThreadSuffix("updateToLoad");
                DataContextHolder.setOperationId(operationId);
                apvExpressService.updateToLoad(whApv);
            });
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    // FBA装车
    @Override
    public ResponseJson doLoad(List<WhFbaAllocation> allocations) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        List<WhFbaAllocation> updateWhFbaAllocations = new ArrayList<>();
        for (WhFbaAllocation allocation : allocations) {
            WhFbaAllocation update = new WhFbaAllocation();
            update.setId(allocation.getId());
            update.setStatus(AsnPrepareStatus.LOADED.intCode());
            //update.se(DataContextHolder.getUserId());
            //update.setDeliverTime(new Timestamp(System.currentTimeMillis()));
            // 改库存
            //此处一个货件修改库存，防止同批交运中，包含同一店铺中同一sku的不同货件
            // whFbaAllocationStockService.deliverBySelf(allocation);
            updateWhFbaAllocations.add(update);
        }

        if (CollectionUtils.isNotEmpty(updateWhFbaAllocations)) {
            whFbaAllocationDao.batchUpdateWhFbaAllocation(updateWhFbaAllocations);
            //新增外借在途数据
            this.createOnWayOrderAndItem(allocations);
        }
        List<Future<?>> futures = new ArrayList<>(allocations.size());
        final String operationId = DataContextHolder.getOperationId();
        for (WhFbaAllocation allocation : allocations) {
            Future<?> future = executorServices.submit(() -> {
                ExecutorUtils.setThreadSuffix("pushMessageToOMS");
                DataContextHolder.setOperationId(operationId);
                SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "海外仓出库单装车");
                //发消息到oms
                allocation.setStatus(AsnPrepareStatus.LOADED.intCode());
                this.sendMsg(allocation);
            });
            futures.add(future);
        }
        //不修改原先的逻辑，其只有全部推送成功才继续往下执行，否则抛出异常回滚事务
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("异步推送消息到OMS失败", e);
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }
        }
        // TODO 根据批次号发送状态到TMS
        sendMsgToTms(allocations.get(0), TmsSendMsgType.SEND_ORDER_STATUS.intCode());
        response.setMessage("装车成功!");
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 海外仓装车
     * 
     * @param whFbaAllocations
     * @return
     */
    @Override
    public ResponseJson doAsnLoad(List<WhFbaAllocation> whFbaAllocations) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        WhFbaAllocation whAsn = whFbaAllocations.get(0);
        if (!AsnPrepareStatus.DELIVER.intCode().equals(whAsn.getStatus())) {
            response.setMessage("不是已交运状态，不允许装车！");
            return response;
        }

        WhFbaAllocation update = new WhFbaAllocation();
        update.setId(whAsn.getId());
        update.setStatus(AsnPrepareStatus.LOADED.intCode());
        this.updateWhFbaAllocation(update);

        //新增外借在途数据
        this.createOnWayOrderAndItem(whFbaAllocations);

        whAsn.setStatus(AsnPrepareStatus.LOADED.intCode());
        this.sendMsg(whAsn);
        this.sendMsgToTms(whAsn, TmsSendMsgType.SEND_ORDER_STATUS.intCode());
        SystemLogUtils.FBAALLOCATIONLOG.log(whAsn.getId(), "海外仓出库单装车");

        WhAsn asn = BeanConvertUtils.convert(whAsn, WhAsn.class);
        asn.setReceivingCode(whAsn.getFbaNo());
        asn.setTrackingNumber(whAsn.getTrackingNumber());
        response.setMessage(JSON.toJSONString(asn));
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 装车后创建外借在途数据
     * 
     * @param allocations
     */
    @Override
    public void createOnWayOrderAndItem(List<WhFbaAllocation> allocations) {
        if (CollectionUtils.isEmpty(allocations))
            return;
        allocations.forEach(allocation -> {
            OnWayOrder onWayOrder = new OnWayOrder();
            onWayOrder.setFbaNo(allocation.getFbaNo());
            onWayOrder.setLoadTime(new Timestamp(System.currentTimeMillis()));
            onWayOrder.setStatus(OnWayStatus.IN_TRANSIT.intCode());
            onWayOrder.setShippingWarehouse("维珍妮中转仓");
            onWayOrder.setPlatform(allocation.getPurposeHouse());
            if (allocation.isFba())
                onWayOrder.setPlatform("Amazon");
            onWayOrder.setPurposeWarehouse(allocation.getPurposeHouse());
            onWayOrderService.createOnWayOrder(onWayOrder);
            List<Integer> itemIdList = new ArrayList<>();
            allocation.buildGroupItems().forEach(item -> {
                OnWayOrderItem orderItem = new OnWayOrderItem();
                orderItem.setOId(onWayOrder.getId());
                orderItem.setSku(item.getProductSku());
                orderItem.setFnSku(item.getFnSku());
                orderItem.setSellSku(item.getSellSku());
                orderItem.setLoadingQuantity(item.getLoadingQuantity());
                onWayOrderItemService.createOnWayOrderItem(orderItem);
                itemIdList.add(orderItem.getId());
            });
            SystemLogUtils.ON_WAY_ORDER.log(onWayOrder.getId(), "出库单装车,生成头程在途单");

            // TODO 调用物流系统获取预警天数
            syncTmsWarningInfo(allocation, onWayOrder, itemIdList);

        });
    }

    /**
     * 同步物流系统预警天数
     * 
     * @param allocation
     */
    @Override
    public void syncTmsWarningInfo(WhFbaAllocation allocation, OnWayOrder onWayOrder, List<Integer> itemIdList) {
        log.info("异步获取预警信息：" + allocation.getFbaNo());
        if (allocation == null || allocation.getWhAsnExtra() == null ||onWayOrder ==null ||  CollectionUtils.isEmpty(itemIdList))
            return;
        executors.execute(new Runnable() {
            @Override
            public void run() {
                Timestamp loadTime = onWayOrder.getLoadTime() == null ? new Timestamp(System.currentTimeMillis()) : onWayOrder.getLoadTime();
                Timestamp deliverTime = allocation.getDeliverTime();
                //调用物流系统获取预警天数信息
                TmsWarningDayDTO dto = new TmsWarningDayDTO();
                dto.setShippingCompanyName(StringUtils.substringBetween(allocation.getShippingCompany(), "-", "("));
                dto.setTransportType(allocation.getSmCode());
                String countryName = allocation.getWhAsnExtra().getReceiptCountry();
                dto.setCountryName(countryName);
                Map<String, TmsWarningDayDTO> dayDTOMap = TmsUtils.getTmsWarningDayList(dto);

                String key = StringUtils.substringBetween(allocation.getShippingCompany(), "-", "(") + "-" + countryName
                        + "-" + StringUtils.substringAfter(allocation.getShippingMethodByTms(), "-") + "-"
                        + allocation.getSmCode();
                TmsWarningDayDTO warningDayDTO = dayDTOMap.get(key);
                if (warningDayDTO == null)
                    return;

                //计算预计到货时间
                String startExpectArriveTime = DateUtils.dateToString(DateUtils
                                .getAfterDate(deliverTime, warningDayDTO.getMinValue() == null ? 0 : warningDayDTO.getMinValue()),
                        DateUtils.STANDARD_DATE_PATTERN);
                String endExpectArriveTime = DateUtils.dateToString(DateUtils
                                .getAfterDate(deliverTime, warningDayDTO.getMaxValue() == null ? 0 : warningDayDTO.getMaxValue()),
                        DateUtils.STANDARD_DATE_PATTERN);
                OnWayOrder updateOrder = new OnWayOrder();
                updateOrder.setId(onWayOrder.getId());
                updateOrder.setStartExpectArriveTime(Timestamp.valueOf(startExpectArriveTime));
                updateOrder.setEndExpectArriveTime(Timestamp.valueOf(endExpectArriveTime));
                onWayOrderService.updateOnWayOrder(updateOrder);

                // 计算在途预警
                Integer onWayWarning = onWayOrderService.calculateOnWayWarning(loadTime, warningDayDTO.getWarningDay());
                itemIdList.forEach(
                        id -> onWayOrderItemService.updateWarningDays(id, warningDayDTO.getWarningDay(), onWayWarning));

                //TODO 推送在途预警给订单系统
            }
        });
    }

    /**
     * 更新海外仓上架数量，并推送消息到TMS
     *
     * @param query
     */
    @Override
    public void updateOverseasUpQuantity(WhFbaAllocationQueryCondition query, Boolean isAuto) {

        //捞取需要更新的货件
        List<WhFbaAllocation> allocationList = this.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(allocationList))
            return;

        List<String> orderNoList = allocationList.stream().map(WhFbaAllocation::getFbaNo).distinct()
                .collect(Collectors.toList());

        //根据货件单号捞取出入库明细
        WhDrpTurnoverItmeQueryCondition queryCondition = new WhDrpTurnoverItmeQueryCondition();
        queryCondition.setOrderNoStr(StringUtils.join(orderNoList, ","));
        List<Integer> typeList = new ArrayList<>();
        typeList.add(DrpTurnoverOderType.RECEIVE_MORE.intCode());
        typeList.add(DrpTurnoverOderType.SHIPMENT_ORDER.intCode());
        queryCondition.setOrderTypeList(typeList);
        queryCondition.setQuantityType(DrpTurnoverInventoryType.CHECK_IN.intCode());
        List<WhDrpTurnoverItme> drpTurnoverItmeList = whDrpTurnoverItmeService.queryWhDrpTurnoverItmes(queryCondition,
                null);
        if (CollectionUtils.isEmpty(drpTurnoverItmeList))
            return;

        Map<String, Integer> upQuantityMap = drpTurnoverItmeList.stream().collect(Collectors
                .groupingBy(d -> d.getOrderNo() + d.getSku(), Collectors.summingInt(WhDrpTurnoverItme::getQuantity)));

        for (WhFbaAllocation allocation : allocationList) {
            allocation.buildGroupItems();
            Map<String, Integer> loadQuantityMap = allocation.buildGroupItems().stream().collect(
                    Collectors.toMap(WhFbaAllocationItem::getProductSku, WhFbaAllocationItem::getLoadingQuantity));
            List<WhFbaAllocationItem> updateItemList = new ArrayList<>();
            for (WhFbaAllocationItem item : allocation.getItems()) {
                Integer upQuantity = upQuantityMap.get(allocation.getShipmentId() + item.getProductSku()) == null ? 0
                        : upQuantityMap.get(allocation.getShipmentId() + item.getProductSku());
                if (item.getPutawayQuantity() != null && item.getPutawayQuantity().equals(upQuantity))
                    continue;

                WhFbaAllocationItem updateItem = new WhFbaAllocationItem();
                updateItem.setId(item.getId());
                updateItem.setProductSku(item.getProductSku());
                updateItem.setFnSku(item.getFnSku());
                updateItem.setPutawayQuantity(upQuantityMap.get(allocation.getShipmentId() + item.getProductSku()));
                // 上架差异 = 海外仓上架数量-装箱数量
                updateItem
                        .setPutawayDiff((updateItem.getPutawayQuantity() == null ? 0 : updateItem.getPutawayQuantity())
                                - (loadQuantityMap.get(item.getProductSku()) == null ? 0
                                : loadQuantityMap.get(item.getProductSku())));
                whFbaAllocationItemService.updateWhFbaAllocationItem(updateItem);
                updateItemList.add(updateItem);
                if (isAuto != null && isAuto) {
                    item.setPutawayQuantity(updateItem.getPutawayQuantity());
                    item.setPutawayDiff(updateItem.getPutawayDiff());
                }
            }
            OnWayOrderQueryCondition onWayOrderQuery = new OnWayOrderQueryCondition();
            onWayOrderQuery.setFbaNo(StringUtils.join(orderNoList, ","));
            List<OnWayOrder> onWayOrderList = onWayOrderService.queryOnWayOrders(onWayOrderQuery, null);

            if (CollectionUtils.isNotEmpty(onWayOrderList)) {
                Map<String, OnWayOrder> onWayOrderMap = onWayOrderList.stream()
                        .collect(Collectors.toMap(OnWayOrder::getFbaNo, o -> o));
                List<OnWayOrderItem> orderItems = onWayOrderList.stream()
                        .filter(o -> o.getFbaNo().equals(allocation.getFbaNo())).collect(Collectors.toList()).get(0)
                        .getItems().stream().filter(i -> i.getOverseasUpTime() != null).collect(Collectors.toList());
                // 更新签收时间（物流系统的签收时间就是仓库推送的单据里最小的海外仓上架时间）
                if (CollectionUtils.isNotEmpty(orderItems) && onWayOrderMap.get(allocation.getFbaNo()) != null
                        && onWayOrderMap.get(allocation.getFbaNo()).getId() != null
                        && onWayOrderMap.get(allocation.getFbaNo()).getReceiveTime() == null) {
                    Timestamp overseasUpTime = orderItems.stream()
                            .min(Comparator.comparing(OnWayOrderItem::getOverseasUpTime)).map(OnWayOrderItem::getOverseasUpTime).orElse(null);
                    OnWayOrder onWayOrder = new OnWayOrder();
                    onWayOrder.setId(onWayOrderMap.get(allocation.getFbaNo()).getId());
                    onWayOrder.setReceiveTime(overseasUpTime);
                    onWayOrderService.updateOnWayOrder(onWayOrder);
                }
            }
            this.sendMsgToTms(allocation, TmsSendMsgType.SEND_OVERSEAS_UP_TIME.intCode());
            if (isAuto != null && isAuto) {
                this.sendMsgToTms(allocation, TmsSendMsgType.SEND_OVERSEAS_UP_QUANTITY.intCode());
            } else if (CollectionUtils.isNotEmpty(updateItemList)) {
                allocation.setItems(updateItemList);
                this.sendMsgToTms(allocation, TmsSendMsgType.SEND_OVERSEAS_UP_QUANTITY.intCode());
            }
        }
    }

    /**
     * 批量更新中转仓单据状态
     *
     * @param
     */
    @Override
    public List<WhFbaAllocation> updateWhFbaAllocationStatus(List<Integer> ids, Integer code, String logContent) {
        WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
        queryCondition.setIds(ids);
        queryCondition.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationDao.queryWhFbaAllocationAndItems(queryCondition, null);
        if (CollectionUtils.isEmpty(allocationList))
            return null;
        List<WhFbaAllocation> updates = new ArrayList<>();
        List<Integer> jitIds = new ArrayList<>();
        for (WhFbaAllocation allocation : allocationList) {
            WhFbaAllocation updateWhFbaAllocation = new WhFbaAllocation();
            updateWhFbaAllocation.setId(allocation.getId());
            updateWhFbaAllocation.setStatus(code);
            updates.add(updateWhFbaAllocation);
            SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), logContent + "，批量修改单据状态",
                    new String[][]{{"状态", AsnPrepareStatus.getNameByCode(String.valueOf(code))}});
            if (AsnPackageMethodEnum.getJitEnumCodeList().contains(allocation.getPackMethod())) {
                jitIds.add(allocation.getId());
            }
        }
        whFbaAllocationDao.batchUpdateWhFbaAllocation(updates);

        //删除分拣格子
        if (CollectionUtils.isNotEmpty(jitIds)) {
            deleteJitPickBoxAndClearMergeTime(jitIds);
        }
        return allocationList;
    }

    /**
     * 删除分拣格子并清除合并时间
     * @param jitIds
     */
    void deleteJitPickBoxAndClearMergeTime(List<Integer> jitIds){
        if (CollectionUtils.isEmpty(jitIds))
            return;
        whFbaAllocationDao.clearMergeTimeByFbaId(jitIds);
        JitPickBoxQueryCondition boxQuery = new JitPickBoxQueryCondition();
        boxQuery.setFbaIdList(jitIds);
        List<JitPickBox> jitPickBoxList = jitPickBoxDao.queryJitPickBoxAndItemList(boxQuery);
        if (CollectionUtils.isEmpty(jitPickBoxList))
            return;
        for (JitPickBox box : jitPickBoxList) {
            List<Integer> fbaIdList = box.getItems().stream().map(JitPickBoxItem::getFbaId)
                    .collect(Collectors.toList());
            for (JitPickBoxItem item : box.getItems()) {
                if (jitIds.contains(item.getFbaId()))
                    jitPickBoxItemDao.deleteJitPickBoxItem(item.getId());
            }

            if (fbaIdList.stream().allMatch(jitIds::contains)) {
                jitPickBoxDao.deleteJitPickBox(box.getId());
                continue;
            }
        }
    }
    /**
     * 推送到OMS
     *
     * @param allocation
     */
    @Override
    public void sendMsg(WhFbaAllocation allocation) {
        try {
            if (StringUtils.isBlank(allocation.getFbaNo())) return;
            if (allocation.getWhAsnExtra() == null || StringUtils.isBlank(allocation.getPurposeHouse())) {
                WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
                queryCondition.setFbaNo(allocation.getFbaNo());
                queryCondition.setQueryWhAsnExtra(true);
                List<WhFbaAllocation> whFbaAllocations = queryWhFbaAllocationAndItems(queryCondition, null);
                if (CollectionUtils.isEmpty(whFbaAllocations))
                    return;
                allocation.setPurposeHouse(whFbaAllocations.get(0).getPurposeHouse());
                allocation.setShipmentId(whFbaAllocations.get(0).getShipmentId());
                allocation.setIsAsn(whFbaAllocations.get(0).getIsAsn());
                if (allocation.getWhAsnExtra() == null)
                    allocation.setWhAsnExtra(whFbaAllocations.get(0).getWhAsnExtra());
            }
            String warehouseId = CacheUtils.getLocalWarehouseIdStr();
            Map<String, Object> message = new HashMap();
            message.put("warehouseId", warehouseId);
            message.put("receivingCode", allocation.getFbaNo());
            message.put("status", allocation.getStatus());
            message.put("localAllot", allocation.getLocalAllot());
            message.put("handleTime", new Date());
            Integer createBy = DataContextHolder.getUserId();
            if (createBy == null)
                createBy = 1;
            message.put("user", createBy + "-" + TaglibUtils.getEmployeeNameByUserId(createBy));
            message.put("shippingMethod", allocation.getShippingMethod());
            message.put("trackingNumber", allocation.getTrackingNumber());
            boolean isJit=false;
            if (allocation.isFba()) {
                message.put("saleChannel", "Amazon");
                //List<Integer> statusList = Arrays.asList(AsnPrepareStatus.WAITING_GEN.intCode(), AsnPrepareStatus.WAITING_GRID.intCode(), AsnPrepareStatus.WAITING_CONFIRM.intCode(), AsnPrepareStatus.DELIVER.intCode());
                //if (statusList.contains(allocation.getStatus())){
                    message.put("fbaNodeTime",new Date());
               // }
                message.put("planNo", allocation.getShipmentId()); // TODO liurui FBA逻辑临时注释
                if (AsnPrepareStatus.DELIVER.intCode().equals(allocation.getStatus())) {
                    message.put("shipmentId", allocation.getShipmentId());
                }
            } else {
                message.put("saleChannel", allocation.getPurposeHouse());
                WhAsnExtra extra = allocation.getWhAsnExtra();
                String zipCode = "无邮编";
                if (Objects.nonNull(extra)){
                    zipCode = extra.getZipcode();
                }
                if (extra!=null && (allocation.getIsAsn() == null || !allocation.getIsAsn())
                        && (AsnPackageMethodEnum.JIT.getCode().equals(extra.getPackageMethod())
                        || AsnPackageMethodEnum.JIT_HALF.getCode().equals(extra.getPackageMethod()))){
                    isJit=true;
                }
                if (StringUtils.equalsIgnoreCase(allocation.getPurposeHouse(), "SMT")
                        && BooleanUtils.isNotTrue(isJit)
                        && BooleanUtils.isTrue(allocation.getIsAsn())
                        && AsnPrepareStatus.DELIVER.intCode().equals(allocation.getStatus())) {
                    message.put("shippingOrderNo", allocation.getShippingOrderNo());
                    List<WhFbaAllocationItem> items = allocation.getItems();
                    List<Map<String, Object>> orderDetails = new ArrayList<>();
                    for (WhFbaAllocationItem item : items) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("deliverNo", item.getTag());
                        map.put("LBX", item.getTemuTagUrl());
                        map.put("sku", item.getProductSku());
                        map.put("quantity", item.getLoadNum());
                        map.put("goodsId", item.getScItemId());
                        orderDetails.add(map);
                    }
                    message.put("orderDetails", orderDetails);
                }
                message.put("zipCode", zipCode);
            }
            message.put("isJit",isJit);

            String boxInfo = null;
            // 待发货送去
            if ((AsnPrepareStatus.WAITING_DELIVER.getCode().equalsIgnoreCase(allocation.getStatus() + "") && !allocation.isFba())
                    || AsnPrepareStatus.WAITING_CONFIRM.getCode().equalsIgnoreCase(allocation.getStatus() + "") && allocation.isFba()) {
                Map<String, Object> map = new HashMap<>();
                map.put("shipmentId", allocation.getShipmentId());
                List<Map<String, Object>> cartonsList = new ArrayList<>();
                List<WhFbaAllocationItem> items = allocation.getItems();
                if (CollectionUtils.isNotEmpty(items)) {
                    Map<Integer, List<WhFbaAllocationItem>> groupBoxList = items.stream()
                            .collect(Collectors.groupingBy(WhFbaAllocationItem::getBoxNo));
                    map.put("cartonQuantity", groupBoxList.size());
                    for (Integer boxNo : groupBoxList.keySet()) {
                        Map<String, Object> cartonsMap = new HashMap<>();
                        cartonsMap.put("cartonId", boxNo);
                        cartonsMap.put("length", groupBoxList.get(boxNo).get(0).getProductLength());
                        cartonsMap.put("width", groupBoxList.get(boxNo).get(0).getProductWidth());
                        cartonsMap.put("high", groupBoxList.get(boxNo).get(0).getProductHeight());
                        //WMS存的重量是KG，转换成g传给OMS
                        Double boxWeight = groupBoxList.get(boxNo).get(0).getProductWeight();
                        cartonsMap.put("weight", boxWeight == null ? 0 : Math.round(boxWeight * 1000));
                        List<Map<String, Object>> itemList = new ArrayList<>();
                        for (WhFbaAllocationItem item : groupBoxList.get(boxNo)) {
                            Map<String, Object> itemMap = new HashMap<>();
                            itemMap.put("sku", item.getSellSku());
                            itemMap.put("quantityInCase", item.getLoadNum() == null ? 0 : item.getLoadNum());
                            itemMap.put("totalQuantity", items.stream()
                                    .filter(i -> StringUtils.equalsIgnoreCase(item.getSellSku() + "~" + item.getProductSku(), i.getSellSku() + "~" + i.getProductSku()))
                                    .mapToInt(i -> i.getLoadNum() == null ? 0 : i.getLoadNum()).sum());
                            itemMap.put("skuImg", item.getSkuImg());
                            itemMap.put("systemSku", item.getProductSku());
                            itemMap.put("systemSkuQuantityInCase", item.getLoadingQuantity() == null ? 0 : item.getLoadingQuantity());
                            itemMap.put("systemSkuTotalQuantity", items.stream()
                                    .filter(i -> StringUtils.equalsIgnoreCase(item.getProductSku(), i.getProductSku()))
                                    .mapToInt(i -> i.getLoadingQuantity() == null ? 0 : i.getLoadingQuantity()).sum());
                            itemList.add(itemMap);
                        }
                        cartonsMap.put("items", itemList);
                        cartonsList.add(cartonsMap);
                    }
                    map.put("cartons", cartonsList);
                }
                boxInfo = JSONObject.toJSONString(map);
            }
            message.put("boxInfo", boxInfo);
            // 推送交运消息到OMS
//        AmqMessage amqMessage = new AmqMessage();
//        amqMessage.setModuleName(AmqMessageModuleName.PUSH_OMS_ASN_STATUS.getCode());
//        amqMessage.setExchange(RabbitMqExchange.PUSH_WMS_DIRECT_EXCHANGE);
//        amqMessage.setQueue(Queues.PUSH_ASN_STATUS_CHANGE);
//        amqMessage.setRoutingKey(Queues.PUSH_ASN_STATUS_CHANGE);
//        amqMessage.setMessageBody(JSON.toJSONString(message));
//        amqMessage.setRelevantParam(allocation.getFbaNo());
//        amqMessage.setCreateBy(createBy);
//        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
//        amqMessage.setSendStatus(false);
//        amqMessage.setRetryLimit(0);
//        amqMessageService.createAmqMessage(amqMessage);
            String msg = JSON.toJSONString(message);
            log.info("PUSH_OMS_ASN_STATUS body:" + msg);
            amqpTemplate.convertAndSend(RabbitMqExchange.PUSH_WMS_DIRECT_EXCHANGE, Queues.PUSH_ASN_STATUS_CHANGE, msg);
            message.remove("boxInfo");
            amqMessageService.createCopyAmqMessage(new AmqMessage(AmqMessageModuleName.PUSH_OMS_ASN_STATUS.getCode(),
                    RabbitMqExchange.PUSH_WMS_DIRECT_EXCHANGE, Queues.PUSH_ASN_STATUS_CHANGE, Queues.PUSH_ASN_STATUS_CHANGE, allocation.getFbaNo(),
                    JSON.toJSONString(message), true, "MQ消息发送成功"));
        } catch (Exception e) {
            log.error("推送平台仓发货单状态到oms失败："+e.getMessage(),e);
        }
    }

    /**
     * 发送消息到TMS
     *
     * @param allocation
     * @param type       1.推送装箱信息，2.推送发货状态, 3.推送海外仓上架时间
     */
    @Override
    public void sendMsgToTms(WhFbaAllocation allocation, Integer type) {
        if (allocation == null || StringUtils.isBlank(allocation.getFbaNo()))
            return;
        if ((TmsSendMsgType.SEND_BOX_INFO.intCode().equals(type)
                || TmsSendMsgType.SEND_OVERSEAS_UP_QUANTITY.intCode().equals(type))
                && CollectionUtils.isEmpty(allocation.getItems()))
            return;
        // 托管与半托管不推送物流
        WhFbaAllocationQueryCondition whFbaAllocationQueryCondition = new WhFbaAllocationQueryCondition();
        whFbaAllocationQueryCondition.setFbaNo(allocation.getFbaNo());
        whFbaAllocationQueryCondition.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = queryWhFbaAllocationAndItems(whFbaAllocationQueryCondition, null);
        if (CollectionUtils.isNotEmpty(allocationList)) {
            //JIT备货单
            WhAsnExtra whAsnExtra = allocationList.get(0).getWhAsnExtra();
            if (whAsnExtra == null || AsnPackageMethodEnum.JIT.getCode().equals(whAsnExtra.getPackageMethod())
                    || AsnPackageMethodEnum.JIT_HALF.getCode().equals(whAsnExtra.getPackageMethod())) {
                log.info("单号：{}，托管与半托管不推送物流", allocation.getFbaNo());
                return;
            }
        }

        Integer createBy = DataContextHolder.getUserId();
        if (createBy == null)
            createBy = 1;
        // 组装消息体
        FirstOrder firstOrder = new FirstOrder();
        firstOrder.setApvNo(allocation.getFbaNo());
        if (allocation.isFba()){
            firstOrder.setApvNo(allocation.getShipmentId());
        }
        firstOrder.setBatNo(allocation.getBatNo());
        //销售人员
        firstOrder.setSalesperson(allocation.getSalesperson());
        //物流时效
        firstOrder.setLogisticsAging(allocation.getLogisticsAging());
        //所属平台
        firstOrder.setPlatform("Amazon");
        String site=allocation.getSite();
        if (!allocation.isFba()) {
            firstOrder.setPlatform(allocation.getPurposeHouse());
            firstOrder.setOrderDelivered("海外仓头程");
        }
        else{
            firstOrder.setOrderDelivered("FBA头程");
            site=allocation.getAmazonSite();
        }

        if (type != null && type.equals(TmsSendMsgType.SEND_BOX_INFO.intCode())) {
            Map<Integer, List<WhFbaAllocationItem>> boxMap = allocation.getItems().stream()
                    .collect(Collectors.groupingBy(WhFbaAllocationItem::getBoxNo));
            List<String> skuList = allocation.getItems().stream().map(WhFbaAllocationItem::getProductSku).distinct()
                    .collect(Collectors.toList());
            WhSkuQueryCondition skuQuery = new WhSkuQueryCondition();
            skuQuery.setSkus(skuList);
            List<WhSku> whSkuList = whSkuService.queryWhSkus(skuQuery, null);
            Map<String, WhSku> skuMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(whSkuList)) {
                skuMap = whSkuList.stream().collect(Collectors.toMap(WhSku::getSku, item -> item));
            }

            //查询关联sku材质、海关编码的新表，并转化为map方便循环中调用
            WhSkuExtendQueryCondition extendQueryCondition = new WhSkuExtendQueryCondition();
            extendQueryCondition.setSkuList(skuList);
            List<WhSkuExtend> whSkuExtends = whSkuExtendService.queryWhSkuExtends(extendQueryCondition, null);
            Map<String, WhSkuExtend> skuExtendMap = Optional.ofNullable(whSkuExtends).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(WhSkuExtend::getSku, item -> item));

            WhAsnExtraQueryCondition query = new WhAsnExtraQueryCondition();
            query.setWhAsnId(allocation.getId());
            WhAsnExtra asnExtra = whAsnExtraService.queryWhAsnExtra(query);
            if (allocation.isFba()) {
                if (AsnPrepareStatus.WAITING_CONFIRM.intCode().equals(allocation.getStatus())){
                    firstOrder.setSalesConfirmState(1);
                }
                if (AsnPrepareStatus.WAITING_DELIVER.intCode().equals(allocation.getStatus())){
                    firstOrder.setSalesConfirmState(2);
                }
            }

            firstOrder.setShipmentNo(allocation.getShipmentId());
            firstOrder.setAccountNumber(site+"-"+allocation.getAccountNumber());
            firstOrder.setObWarehouse(allocation.getPurposeHouse());
            firstOrder.setDeWarehouse("维珍妮中转仓");
            firstOrder.setTransportType(allocation.getSmCode());
            if (allocation.isFba()) {
                asnExtra = allocation.getWhAsnExtra();
            }
            firstOrder.setZipCode(asnExtra.getZipcode());
            firstOrder.setCountry(asnExtra.getReceiptCountry());
            //新增州省，城市，详细地址推送
            firstOrder.setProvince(asnExtra.getReceiptArea());
            firstOrder.setCity(asnExtra.getReceiptCity());
            firstOrder.setAddress(asnExtra.getReceiptAddress());
            firstOrder.setReceiveName(asnExtra.getReceiptPerson());
            List<FirstOrderItem> firstOrderItemList = new ArrayList<>();
            Map<String, WhSku> finalSkuMap = skuMap;
            boxMap.forEach((k, v) -> {
                FirstOrderItem firstOrderItem = new FirstOrderItem();
                firstOrderItem.setBoxNo(String.valueOf(k));
                firstOrderItem.setWidth(v.get(0).getProductWidth() == null ? 0 : v.get(0).getProductWidth());
                firstOrderItem.setHigh(v.get(0).getProductHeight() == null ? 0 : v.get(0).getProductHeight());
                firstOrderItem.setLength(v.get(0).getProductLength() == null ? 0 : v.get(0).getProductLength());
                firstOrderItem.setWeight(v.get(0).getProductWeight());
                firstOrderItem
                        .setVolume(firstOrderItem.getLength() * firstOrderItem.getWidth() * firstOrderItem.getHigh());
                List<SkuInfo> skuInfoList = new ArrayList<>();
                v.forEach(item -> {
                    SkuInfo skuInfo = new SkuInfo();
                    skuInfo.setFnSku(item.getFnSku());
                    skuInfo.setSku(item.getProductSku());
                    skuInfo.setQuantity(item.getLoadingQuantity());
                    skuInfo.setSkuTag(item.getSkuTags());
                    WhSku whSku = finalSkuMap == null ? null : finalSkuMap.get(item.getProductSku());
                    //sku表里的重量是g，转成KG传给TMS
                    Double skuWeight = whSku != null && whSku.getWeight() != null ? whSku.getWeight() / 1000 : 0d;

                    skuInfo.setWeight(skuWeight);

                    WhSkuExtend whSkuExtend = skuExtendMap.get(item.getProductSku());
                    //材质
                    skuInfo.setMaterial(whSkuExtend == null ? null : whSkuExtend.getTexture());
                    //海关编码
                    skuInfo.setHsCode(whSkuExtend == null ? null : whSkuExtend.getCustomsCode());
                    //首图连接
                    skuInfo.setImgUrl(whSku == null ? null : whSku.getImageUrl());
                    skuInfo.setSkuName(whSku == null ? null : whSku.getName());

                    skuInfo.setPrice(whSku == null ? null : whSku.getAveragePurchasePrice());
                    skuInfo.setCurrency("RMB");
                    skuInfo.setDeclareCnName(whSku == null ? null : whSku.getCustomsNameCn());
                    skuInfo.setDeclareEnName(whSku == null ? null : whSku.getCustomsNameEn());

                    //出库成本
                    WhAssetChangeItemQueryCondition queryCondition = new WhAssetChangeItemQueryCondition();
                    queryCondition.setSku(item.getProductSku());
                    List<WhAssetChangeItem> whAssetChangeItems = whAssetChangeItemService.queryWhAssetChangeItems(queryCondition, null);
                    if (CollectionUtils.isNotEmpty(whAssetChangeItems)) {
                        skuInfo.setEndingInventoryPrice(whAssetChangeItems.get(0).getEndingInventoryPrice());
                    }

                    skuInfoList.add(skuInfo);
                });
                firstOrderItem.setSkuInfoList(skuInfoList);
                firstOrderItem.setSkuInfos(JSONObject.toJSONString(skuInfoList));
                firstOrderItemList.add(firstOrderItem);
            });
            firstOrder.setFirstOrderItemVOList(firstOrderItemList);
            firstOrder.setUpdateTime(allocation.getBoxPushTime());
            // 避免消息体过长存表失败，直接发送队列
            amqpTemplate.convertAndSend(RabbitMqExchange.TMS_WMS_DIRECT_EXCHANGE, Queues.PUSH_FIRST_ORDER_RESULT, JSON.toJSONString(firstOrder));
            log.info("货件信息推送TMS成功,消息体："+JSON.toJSONString(firstOrder));
            firstOrder.setFirstOrderItemVOList(null);
            amqMessageService.createCopyAmqMessage(new AmqMessage(AmqMessageModuleName.PUSH_TMS_ASN_PREPARE_RESULT.getCode(),
                    RabbitMqExchange.TMS_WMS_DIRECT_EXCHANGE, Queues.PUSH_FIRST_ORDER_RESULT, Queues.PMS_WH_EXCEPTION_WMS_QUEUE, allocation.getFbaNo(),
                    JSON.toJSONString(firstOrder), true, "MQ消息发送成功"));
            return;
        }
        else if (TmsSendMsgType.SEND_OVERSEAS_UP_TIME.intCode().equals(type)) {
            firstOrder.setBatStatus("已签收");
            firstOrder.setUpdateTime(allocation.getOverseasUpTime());
        }
        else if (TmsSendMsgType.SEND_OVERSEAS_UP_QUANTITY.intCode().equals(type)) {
            firstOrder.setBatStatus("已到仓数量");
            firstOrder.setUpdateTime(allocation.getOverseasUpTime());
            List<SkuInfo> skuInfoList = new ArrayList<>();
            List<String> skuList = allocation.getItems().stream().map(WhFbaAllocationItem::getProductSku).distinct()
                    .collect(Collectors.toList());
            WhSkuQueryCondition skuQuery = new WhSkuQueryCondition();
            skuQuery.setSkus(skuList);
            List<WhSku> whSkuList = whSkuService.queryWhSkus(skuQuery, null);
            Map<String, WhSku> skuMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(whSkuList)) {
                skuMap = whSkuList.stream().collect(Collectors.toMap(WhSku::getSku, item -> item));
            }
            for (WhFbaAllocationItem item : allocation.buildGroupItems()) {
                SkuInfo skuInfo = new SkuInfo();
                skuInfo.setSku(item.getProductSku());
                skuInfo.setFnSku(item.getFnSku());
                skuInfo.setQuantity(item.getPutawayQuantity());
                skuInfo.setSkuName(
                        skuMap.get(item.getProductSku()) == null ? null : skuMap.get(item.getProductSku()).getName());
                skuInfoList.add(skuInfo);
            }
            firstOrder.setBatNo(null);
            firstOrder.setMsg(JSONObject.toJSONString(skuInfoList));
        }
        else {
            firstOrder.setBatStatus(AsnPrepareStatus.getNameByCode(allocation.getStatus().toString()));
            firstOrder.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            if (StringUtils.isNotBlank(allocation.getTags())) {
                firstOrder.setMsg("已交运取消");
            }
        }
        // 推送交运消息到TMS
        AmqMessage amqMessage = new AmqMessage();
        // TYPE = 1 包装推送订单信息
        // 交运推送状态
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_TMS_ASN_PREPARE_STATUS.getCode());
        amqMessage.setQueue(Queues.PUSH_FIRST_ORDER_STATUS_CHANGE);
        amqMessage.setRoutingKey(Queues.PUSH_FIRST_ORDER_STATUS_CHANGE);
        amqMessage.setExchange(RabbitMqExchange.TMS_WMS_DIRECT_EXCHANGE);
        amqMessage.setMessageBody(JSON.toJSONString(firstOrder));
        amqMessage.setRelevantParam(allocation.getFbaNo());
        if (allocation.isFba()){
            amqMessage.setRelevantParam(allocation.getShipmentId());
        }
        amqMessage.setCreateBy(createBy);
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        amqMessage.setSendStatus(false);
        amqMessage.setRetryLimit(0);
        amqMessageService.createAmqMessage(amqMessage);
    }

    @Override
    public int queryGridQuantity(Integer taskId) {
        return whFbaAllocationDao.queryGridQuantity(taskId);
    }



    /**
     * 过滤明细数据
     * @param fbaAllocation
     * @param item
     * @param type 过滤类型 1-根据sku过滤 2-根据sku并且拣货数量不为零的
     * @return
     */
    private List<WhFbaAllocationItem>  filterFbaItemData(WhFbaAllocation fbaAllocation ,WhFbaAllocationItem item,Integer type){
        if (SaleChannel.saleChannels.contains(fbaAllocation.getPurposeHouse())) {
            //海外仓 不存在fnsku
            switch (type) {
                case 1:
                    return fbaAllocation.getItems().stream().filter(s -> item.getProductSku().equals(s.getProductSku())).collect(Collectors.toList());
                case 2:
                    return fbaAllocation.getItems().stream().filter(s -> item.getProductSku().equals(s.getProductSku()) && (s.getPickQuantity() != null && s.getPickQuantity() > 0)).collect(Collectors.toList());
            }


        }else{
            //中转仓 操作fnsku
            switch (type) {
                case 1:
                    return fbaAllocation.getItems().stream().filter(s -> item.getFnSku().equals(s.getFnSku()) && item.getProductSku().equals(s.getProductSku())).collect(Collectors.toList());
                case 2:
                    return fbaAllocation.getItems().stream().filter(s -> item.getFnSku().equals(s.getFnSku()) && item.getProductSku().equals(s.getProductSku()) && (s.getPickQuantity() != null && s.getPickQuantity() > 0)).collect(Collectors.toList());

            }
        }
        return new ArrayList<>();
    }


    /**
     * 根据传入的参数，判断是否要进行FBA的判断
     *
     * @param needAssertFBA  ture表示要进行判断，false表示不用
     * @param allocation 要进行FBA判断的对象
     * @return true表示判断通过或者不需要进行FBA的判断，false表示判断不通过
     */
    private boolean assertFBA(boolean needAssertFBA, WhFbaAllocation allocation) {
        if (needAssertFBA) {
            return Objects.nonNull(allocation) && allocation.isFba();
        }
        return true;
    }

    /**
     * 仓库审核
     * 
     * @param id
     * @param allocationStr
     * @return
     */
    @Override
    public ResponseJson doCheckLabel(Integer id, String allocationStr) {
        Assert.notNull(id);
        Assert.notNull(allocationStr);
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        List<WhFbaAllocation> allocationList = whFbaAllocationDao.queryWhFbaAllocationAndItems(query, null);

        if (CollectionUtils.isEmpty(allocationList)) {
            response.setMessage("单据不存在！");
            return response;
        }
        WhFbaAllocation allocation = JSONObject.parseObject(allocationStr, WhFbaAllocation.class);
        List<WhFbaAllocationItem> pItems = allocation.getItems();
        if (CollectionUtils.isEmpty(pItems)) {
            response.setMessage("没有要提交的明细！");
            return response;
        }
        // 3、当整个单据只修改加工类型提交时，修改加工类型后，单据进入后一个状态
        // 4、当整个单据存在需要重新加工的FNSKU时，单据变更为待贴标，且被选择为需要重新加工的FNSKU清除掉原贴标信息
        // 5、当一个FNSKU条目既修改了加工类型，又被选择为需要重新加工，按重新加工操作处理
        boolean notExistReProcess = pItems.stream().allMatch(i -> i.getReProcess() == null || !i.getReProcess());
        Integer status = AsnPrepareStatus.WAITING_BOX.intCode();
        if (!notExistReProcess)
            status = AsnPrepareStatus.WAITING_LABEL.intCode();
        
        Map<String, List<WhFbaAllocationItem>> dbFnSkuMap = allocationList.get(0).getItems().stream()
                .collect(Collectors.groupingBy(WhFbaAllocationItem::getFnSku));

        WhFbaAllocation updateAllocation = new WhFbaAllocation();
        updateAllocation.setStatus(status);
        updateAllocation.setId(id);
        whFbaAllocationDao.updateWhFbaAllocation(updateAllocation);
        List<String> labelMsg = new ArrayList<>();
        pItems.forEach(item -> {
            List<WhFbaAllocationItem> items = dbFnSkuMap.get(item.getFnSku());
            if (CollectionUtils.isEmpty(items))
                return;
            if (item.getReProcess() != null && item.getReProcess()) {
                whFbaAllocationItemService.clearTagMsg(item.getFbaId(), item.getFnSku());
                labelMsg.add(item.getLabelDataMes(items.get(0)));
                return;
            }
            if (item.getProcessType() != null && !item.getProcessType().equals(items.get(0).getProcessType())) {
                whFbaAllocationItemService.updateItemByFbaIdAndFnSku(item);
                labelMsg.add(item.getLabelDataMes(items.get(0)));
            }
        });

        SystemLogUtils.FBAALLOCATIONLOG.log(id, "复核贴标信息",
                new String[][] {
                        { "原状态", AsnPrepareStatus.getNameByCode(String.valueOf(allocationList.get(0).getStatus())) },
                        { "新状态", AsnPrepareStatus.getNameByCode(String.valueOf(status)) },
                        { "修改信息", JSONObject.toJSONString(labelMsg) } });
        if (allocationList.get(0).isFba()){
            allocationList.get(0).setStatus(status);
            sendMsg(allocationList.get(0));
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }


    @Override
    public int unBoxCountInTask(String fbaNo) {
        return whFbaAllocationDao.unBoxCountInTask(fbaNo);
    }

    @Override
    public int unPackCountInTask(String fbaNo) {
        return whFbaAllocationDao.unPackCountInTask(fbaNo);
    }

    @Override
    public void deleteTransferDeliverFbaPdf(String startTime, String endDeliverDate) {
        List<String> bagNoList = whFbaAllocationDao.queryToDeletePocketCardList(startTime, endDeliverDate);
        //删除结袋卡
        if (CollectionUtils.isNotEmpty(bagNoList)){
            bagNoList.forEach(bagNo -> {
                if (StringUtils.isBlank(bagNo))
                    return;
                String filePath = "/usr/local/erp/static/file/pdf/pocketcard/" + bagNo + ".pdf";
                PdfUtils.deleteFile(filePath);
                if (StringUtils.startsWith(bagNo, "TEMU")) {
                    String temuFilePath = "/usr/local/erp/static/file/pdf/pocketcard/temu/" + bagNo + ".pdf";
                    PdfUtils.deleteFile(temuFilePath);
                }
            });
        }
        List<WhApvOutStockChain> deliverList = whFbaAllocationDao.queryToDeleteList(endDeliverDate);
        if (CollectionUtils.isEmpty(deliverList))
            return;

        Map<String, List<WhApvOutStockChain>> listMap = deliverList.stream()
                .collect(Collectors.groupingBy(WhApvOutStockChain::getRelevantNo));

        listMap.forEach((fbaNo, value) -> {
            String mergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + fbaNo + "mg.pdf";
            PdfUtils.deleteFile(mergePdfPath);
            String mergePdfPath2 = PdfUtils.STATIC_FILE_PATH + "/" + fbaNo + ".pdf";
            PdfUtils.deleteFile(mergePdfPath2);
            String xiangmaiPdfPath = PdfUtils.STATIC_FILE_JIT_MD_PATH + "/" + fbaNo + ".pdf";
            PdfUtils.deleteFile(xiangmaiPdfPath);
            value.forEach(w -> {
                String skuLabelPath = PdfUtils.STATIC_FILE_PATH + "/" + fbaNo + w.getSku() + ".pdf";
                PdfUtils.deleteFile(skuLabelPath);
                String sku_Path = PdfUtils.STATIC_FILE_PATH + "/" + fbaNo + "_" + w.getSku() + ".pdf";
                PdfUtils.deleteFile(sku_Path);
                // 删除GPSR
                String gpsrPdfPath = ApvPackUtils.STATIC_FILE_GPSR_PATH + "/" + fbaNo + "_" + w.getSku() + ".pdf";
                PdfUtils.deleteFile(gpsrPdfPath);
                if (StringUtils.isNotBlank(w.getSkuBarcode())) {
                    // 删除JITSKU条码命名文件
                    String skuBarcodePath = PdfUtils.STATIC_FILE_PATH + "/" + fbaNo + "_" + w.getSkuBarcode() + ".pdf";
                    PdfUtils.deleteFile(skuBarcodePath);
                }
            });
        });
        // 删除temu的pdf
        deleteTemuPdf(endDeliverDate);
    }

    @Override
    @Transactional
    public ResponseJson importTransferReturnOrder(List<String> trackingNumber) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.SUCCESS);
        if (CollectionUtils.isEmpty(trackingNumber)) {
            response.setMessage("追踪号为空！");
            response.setStatus(StatusCode.FAIL);
            return response;
        }
        WhFbaAllocationQueryCondition queryCondition=new WhFbaAllocationQueryCondition();
        queryCondition.setTrackingNumber(String.join(",", trackingNumber));
        queryCondition.setIsReturn(false);
        queryCondition.setStatusList(Arrays.asList(AsnPrepareStatus.DELIVER.intCode(),AsnPrepareStatus.LOADED.intCode()));
        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationDao.queryWhFbaAllocationAndItems(queryCondition, null);
        if (CollectionUtils.isEmpty(whFbaAllocationList)) {
            response.setMessage("没有找到相关订单或该订单已经退仓");
            response.setStatus(StatusCode.FAIL);
            return response;

        }
        List<PushOmsPacData> omsPacDataList = new ArrayList<>();
        List<WhFbaAllocation> updatefbaList = new ArrayList<>();
        for (WhFbaAllocation whFbaAllocation : whFbaAllocationList) {
            WhFbaAllocation update = new WhFbaAllocation();
            update.setId(whFbaAllocation.getId());
            update.setIsReturn(true);
            updatefbaList.add(update);
            omsPacDataList.add(new PushOmsPacData(whFbaAllocation.getTrackingNumber(), whFbaAllocation.getShipmentId(),true));
         }
        if (CollectionUtils.isNotEmpty(updatefbaList)) {
            whFbaAllocationDao.batchUpdateWhFbaAllocation(updatefbaList);
        }
        pushTransferReturnOrder(omsPacDataList);
        response.setMessage("成功！");
        return response;
    }

    //推送中转仓退仓到oms
    @Override
    public void pushTransferReturnOrder(List<PushOmsPacData> omsPacDataList) {
        if (CollectionUtils.isEmpty(omsPacDataList)) {
            return;
        }
        SystemParam systemParam = CacheUtils.SystemParamGet("OMS_PARAM.PUSH_CIW_RETURN");
        if (systemParam == null) {
            log.error("未配置OMS仓退单地址");
            throw new RuntimeException("未配置OMS仓退单地址");
        }
        String url = systemParam.getParamValue();
        List<List<PushOmsPacData>> batches = Lists.partition(omsPacDataList, 50);
        for (List<PushOmsPacData> batch : batches) {
            executors.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        ApiResult<?> result = HttpUtils.post(url, HttpUtils.ACCESS_TOKEN, batch, ApiResult.class);
                        if (RedissonLockUtil.tryLock(TRANSFER_RETURN_ORDER_PUSH_FAIL_KEY,10,10)) {
                            String pushOmsPacDataJson = StringRedisUtils.get(RedisConstant.TRANSFER_RETURN_ORDER_PUSH_FAIL);
                            List<PushOmsPacData> pushOmsPacDataList=new ArrayList<>();
                            if (StringUtils.isNotBlank(pushOmsPacDataJson)){
                                pushOmsPacDataList = JSON.parseArray(pushOmsPacDataJson, PushOmsPacData.class);
                            }
                            if (result.isSuccess()) {
                                log.info("推送中转仓发货单退仓到oms成功！ 推送数据：" + JSON.toJSONString(batch));
                                if (CollectionUtils.isNotEmpty(pushOmsPacDataList)){
                                    pushOmsPacDataList.removeAll(batch);
                                }

                            }else{
                                log.error("推送中转仓发货单退仓到oms失败! 推送数据：" + JSON.toJSONString(batch) + " 返回数据:" + result.getErrorMsg());
                                pushOmsPacDataList.addAll(batch);
                            }
                            if (CollectionUtils.isNotEmpty(pushOmsPacDataList)){
                                List<PushOmsPacData> omsPacData = pushOmsPacDataList.stream().distinct().collect(Collectors.toList());
                                StringRedisUtils.set(RedisConstant.TRANSFER_RETURN_ORDER_PUSH_FAIL,JSON.toJSONString(omsPacData));
                            }else{
                                StringRedisUtils.del(RedisConstant.TRANSFER_RETURN_ORDER_PUSH_FAIL);
                            }
                        }
                    } catch (Exception e) {
                        log.error("推送中转仓发货单退仓到oms时发生异常: " + e.getMessage(), e);
                    }finally{
                        RedissonLockUtil.unlock(TRANSFER_RETURN_ORDER_PUSH_FAIL_KEY);
                    }
                }
            });
        }
    }

   /* @Override
    public String createPickupOrderV2(List<Integer> ids) {
        // 先同步已请求创建揽收的单据，并排除
        List<WhFbaAllocation> allocationList;
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        if (CollectionUtils.isNotEmpty(ids)) {
            query.setIds(ids);
        }
        query.setIsAsn(true);
        query.setQueryWhAsnExtra(true);
        query.setStatusList(Arrays.asList(AsnPrepareStatus.WAITING_GEN.intCode(),AsnPrepareStatus.CHECK_PRINT.intCode(),AsnPrepareStatus.PICK_STOCK_OUT.intCode()));
        query.setPackageMethodList(AsnPackageMethodEnum.getJitEnumCodeList());
        allocationList = queryWhFbaAllocationAndItems(query, null);
        allocationList = allocationList.stream().filter(a ->a.getItems().stream().allMatch(i -> StringUtils.isNotBlank(i.getTag()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allocationList)) {
            throw new RuntimeException("符合操作条件的数据为0！");
        }
        allocationList = allocationList.stream().filter(a -> a.getWhAsnExtra() != null &&
                StringUtils.isNotBlank(a.getWhAsnExtra().getReceiptAddress())
                && StringUtils.isBlank(a.getWhAsnExtra().getPickupOrderId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(allocationList)) {
            throw new RuntimeException("符合操作条件的数据为0！");
        }

        Iterator<WhFbaAllocation> iterator = allocationList.iterator();
        List<String> remList = new ArrayList<>();
        while (iterator.hasNext()) {
            WhFbaAllocation whFbaAllocation = iterator.next();
            if (remList.contains(whFbaAllocation.getFbaNo()))
                iterator.remove();
            String fbaNo = StringRedisUtils.get(RedisConstant.SMT_CREATE_PICKUP_ORDER + whFbaAllocation.getFbaNo());
            if (StringUtils.isNotBlank(fbaNo)){
                rDelayedQueue.offer(fbaNo, 10, TimeUnit.SECONDS);
                iterator.remove();
                remList.add(fbaNo);
            }
        }
        if (CollectionUtils.isEmpty(allocationList))
            throw new RuntimeException("符合操作条件的数据为0！");

        // 根据账号、到仓地址与类型分组
        if (JedisUtils.exists(ApvTaskRedisLock.ASN_CREATE_PICKUP_ORDER.getName())) {
            log.warn("---invoke SmtCreatePickupOrderJobHandler  createJitCoOrder --- 有账号正在创建发货单");
            throw new RuntimeException("有账号正在创建发货单");
        } else {
            JedisUtils.set(ApvTaskRedisLock.ASN_CREATE_PICKUP_ORDER.getName(), "lock", 600L);
        }
        String result=null;
        try {
            for (WhFbaAllocation allocation : allocationList) {
                    // 查询可揽收时间
                    String estimatedPickupDate = null;
                    try {
                        estimatedPickupDate = aliExpressCallService.queryPickupAvailableDate(Collections.singletonList(allocation));
                    } catch (Exception e) {
                        log.error(e.getMessage(),e);
                        SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "查询揽收时间失败，"+e.getMessage());
                        continue;
                    }
                    if (StringUtils.isBlank(estimatedPickupDate)) {
                        SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "可揽收时间为空");
                        continue;
                    }
                    Integer createNum = 1;
                    try {
                        if (StringRedisUtils.exists(RedisConstant.SMT_CREATE_PICKUP_ORDER_NUM + allocation.getFbaNo())) {
                            Integer retryNum = Integer.valueOf(StringRedisUtils.get(RedisConstant.SMT_CREATE_PICKUP_ORDER_NUM + allocation.getFbaNo()));
                            if ((retryNum) > SMT_RETRY_NUM) {
                                throw new RuntimeException("当天已创建过失败"+SMT_RETRY_NUM+1+"次");
                            } else {
                                createNum = ++retryNum;
                            }
                        }
                        // 拆包需要校验，发货单是否重新创建成功
                        if(allocation.getTransitType() != null && allocation.getTransitType() == 1) {
                            if(allocation.getItems().stream().map(i -> i.getTag()).distinct().count() == 1)
                                throw new RuntimeException("备货单拆包后未成功创建发货单，请先创建发货单");
                        }
                        aliExpressCallService.createPickupOrder(estimatedPickupDate,Collections.singletonList(allocation));
                        if (createNum > 1)
                            StringRedisUtils.del(RedisConstant.SMT_CREATE_PICKUP_ORDER_NUM + allocation.getFbaNo());
                    } catch (Exception e) {
                        StringRedisUtils.set(RedisConstant.SMT_CREATE_PICKUP_ORDER_NUM+allocation.getFbaNo(),createNum.toString(),24 * 60 * 60L);
                        SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "创建揽收失败，"+e.getMessage());
                        continue;
                    }
                    StringRedisUtils.set(RedisConstant.SMT_CREATE_PICKUP_ORDER+allocation.getFbaNo(),
                            allocation.getFbaNo(), 7 * 24 * 60 * 60L);
                    rDelayedQueue.offer(allocation.getFbaNo(), 10, TimeUnit.SECONDS);
                    SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "创建揽收成功");
            }
        } finally {
            JedisUtils.del(ApvTaskRedisLock.ASN_CREATE_PICKUP_ORDER.getName());
        }
        if (StringUtils.isNotBlank(result)){
            result = "部分失败，详细查看日志"+result;
        }
        return result;
    }*/

    // 由于结袋创建揽收需要调用三个接口，且平台异步，结袋只等待一分钟,不需要事务
    @Override
    public String syncCreatePickupOrderV3( List<WhFbaAllocation> fbaAllocations, WhScanShipment scanShipment, Integer retry) {
        // 需要创建揽收单
        Integer scanShipmentId = scanShipment.getId();
        // 从redis获取揽收单号

        if (!StringRedisUtils.exists(RedisConstant.SMT_CREATE_PICKUP_ORDER + scanShipment.getBagNo())
                && StringUtils.isBlank(scanShipment.getPickupOrderNo()) && StringUtils.isBlank(fbaAllocations.get(0).getShippingOrderNo())) {
            // 查询可揽收时间
            Map<String, String> tagMap = new HashMap<>();
            for (WhFbaAllocation fbaAllocation : fbaAllocations) {
                fbaAllocation.getItems()
                        .stream()
                        .map(WhFbaAllocationItem::getTag)
                        .filter(StringUtils::isNotBlank)
                        .distinct()
                        .forEach(tag->{
                            tagMap.put(tag, fbaAllocation.getAccountNumber());
                        });
            }

            Map<String, String> availableDateMap = null;
            try {
                 availableDateMap = aliExpressCallService.queryPickupAvailableDate(fbaAllocations);
            } catch (Exception e) {
                if (retry == 3) {
                    log.error(e.getMessage(), e);
                    SCANSHIPMENTLOG.log(scanShipmentId, "仓发组包查询揽收时间失败，" + e.getMessage());
                    return null;
                } else {
                    return syncCreatePickupOrderV3(fbaAllocations, scanShipment, ++retry);
                }
            }
            try {
                String excludeOrderNos = aliExpressCallService.createPickupOrder(availableDateMap, fbaAllocations);
                SCANSHIPMENTLOG.log(scanShipmentId, String.format("创建揽收成功,揽收时间日期：【%s】,时间【%s-%s】，排除单据【%s】",availableDateMap.get("estimated_pickup_date"),
                        availableDateMap.get("startTime"),availableDateMap.get("endTime"),excludeOrderNos));
                StringRedisUtils.set(RedisConstant.SMT_CREATE_PICKUP_ORDER+scanShipment.getBagNo(),
                        scanShipment.getBagNo(), 7 * 24 * 60 * 60L);
            } catch (Exception e) {
                if (retry == 3) {
                    log.error(e.getMessage(), e);

                    String errorMsg="创建揽收失败"+e.getMessage();

                    List<String> accountNumberList =new ArrayList<>();
                    tagMap.forEach((tag, accountNumber)->{
                        if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("店铺上门揽欠费停服") && e.getMessage().contains(tag)) {
                            accountNumberList.add(accountNumber);
                        }
                    });
                    if (CollectionUtils.isNotEmpty(accountNumberList)) {
                        errorMsg+= "， 欠费店铺："+ accountNumberList;
                    }
                    SCANSHIPMENTLOG.log(scanShipmentId, errorMsg);
                    return null;
                } else {
                    return syncCreatePickupOrderV3(fbaAllocations, scanShipment, ++retry);
                }
            }
            try {
                TimeUnit.SECONDS.sleep(10);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        // 查询揽收单
        if (StringUtils.isBlank(scanShipment.getPickupOrderNo())) {
            if (StringUtils.isNotBlank(fbaAllocations.get(0).getShippingOrderNo())
                    && fbaAllocations.get(0).getWhAsnExtra()!= null
                        && StringUtils.isNotBlank(fbaAllocations.get(0).getWhAsnExtra().getPickupOrderId())) {
                String pickupOrderNo = fbaAllocations.get(0).getWhAsnExtra().getPickupOrderId();
                WhScanShipment updateWhScanShipment = new WhScanShipment();
                updateWhScanShipment.setId(scanShipmentId);
                updateWhScanShipment.setPickupOrderNo(pickupOrderNo);
                scanShipmentService.updateWhScanShipment(updateWhScanShipment);
                scanShipment.setPickupOrderNo(pickupOrderNo);
            } else {
                try {
                    JSONObject jsonObject = aliExpressCallService.getPickupOrderNo(fbaAllocations);
                    // 揽收单号
                    String pickupOrderNo = jsonObject.getString("pickup_order_number");
                    // 服务商运单号
                    String fulfillPickupOrderCode = jsonObject.getString("fulfill_pickup_order_code");
                    List<WhFbaAllocation> updateWhFbaAllocations = new ArrayList<>();
                    List<WhAsnExtra> updateWhAsnExtras = new ArrayList<>();
                    for (WhFbaAllocation allocation : fbaAllocations) {
                        WhFbaAllocation whFbaAllocation = new WhFbaAllocation();
                        whFbaAllocation.setId(allocation.getId());
                        whFbaAllocation.setShippingOrderNo(fulfillPickupOrderCode);
                        updateWhFbaAllocations.add(whFbaAllocation);
                        WhAsnExtra whAsnExtra = new WhAsnExtra();
                        whAsnExtra.setId(allocation.getWhAsnExtra().getId());
                        whAsnExtra.setPickupOrderId(pickupOrderNo);
                        updateWhAsnExtras.add(whAsnExtra);
                    }
                    whAsnExtraService.batchUpdateWhAsnExtra(updateWhAsnExtras);
                    batchUpdateWhFbaAllocation(updateWhFbaAllocations);
                    WhScanShipment updateWhScanShipment = new WhScanShipment();
                    updateWhScanShipment.setId(scanShipmentId);
                    updateWhScanShipment.setPickupOrderNo(pickupOrderNo);
                    scanShipmentService.updateWhScanShipment(updateWhScanShipment);
                    scanShipment.setPickupOrderNo(pickupOrderNo);
                    fbaAllocations.get(0).getWhAsnExtra().setPickupOrderId(pickupOrderNo);
                    SCANSHIPMENTLOG.log(scanShipmentId, "同步揽收信息成功" );
                } catch (Exception e) {
                    if (retry == 3) {
                        log.error(e.getMessage(),e);
                        SCANSHIPMENTLOG.log(scanShipmentId, "同步揽收信息失败，" + e.getMessage());
                        return null;
                    } else {
                        try {
                            TimeUnit.SECONDS.sleep(15);
                        } catch (InterruptedException ei) {
                            throw new RuntimeException(ei);
                        }
                        return syncCreatePickupOrderV3(fbaAllocations, scanShipment, ++retry);
                    }
                }
            }
        }
        // 打印揽收揽收单
        if (StringUtils.isNotBlank(scanShipment.getPickupOrderNo())) {
            try {
                String url = aliExpressCallService.createPickupShippingMarkPdf(fbaAllocations.get(0));
                log.info("海外仓揽收单 bagNo："+scanShipment.getBagNo()+" url：" + url);
                return url;
            } catch (Exception e) {
                if (retry == 3) {
                    log.error(e.getMessage(),e);
                    SCANSHIPMENTLOG.log(scanShipmentId, "打印揽收揽收单失败，" + e.getMessage());
                    return null;
                } else {
                    return syncCreatePickupOrderV3(fbaAllocations, scanShipment, ++retry);
                }
            }
        }
        return null;
    }


    // 同步揽收单号并更新
    @Override
    public void syncWhFbaAllocationPickupOrderNo(String fbaNo, Boolean updateStatusFlag) {
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setFbaNo(fbaNo);
        query.setQueryWhAsnExtra(true);
        query.setQueryAsnPickBoxNumber(true);
        query.setPackageMethodList(AsnPackageMethodEnum.getJitEnumCodeList());
        List<WhFbaAllocation> allocationList = queryWhFbaAllocationAndItems(query, null);
        WhFbaAllocation allocation = allocationList.get(0);

        try {
           /* String pickupOrderNo = allocation.getWhAsnExtra().getPickupOrderId();
           Boolean successFlag = true;
            if (StringUtils.isBlank(allocation.getShippingOrderNo())) {
                JSONObject jsonObject = aliExpressCallService.getPickupOrderNo(allocationList);
                // 揽收单号
                pickupOrderNo = jsonObject.getString("pickup_order_number");
                // 服务商运单号
                String fulfillPickupOrderCode = jsonObject.getString("fulfill_pickup_order_code");

                WhFbaAllocation whFbaAllocation = new WhFbaAllocation();
                whFbaAllocation.setId(allocation.getId());
                whFbaAllocation.setShippingOrderNo(fulfillPickupOrderCode);

                WhAsnExtra whAsnExtra = new WhAsnExtra();
                whAsnExtra.setId(allocation.getWhAsnExtra().getId());
                whAsnExtra.setPickupOrderId(pickupOrderNo);
                whAsnExtraService.updateWhAsnExtra(whAsnExtra);
                updateWhFbaAllocation(whFbaAllocation);
                SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(),  "同步揽收信息成功");
            }*/
            // 获取物流单号
            try {
                if (allocation.getItems().stream().anyMatch(i -> StringUtils.isBlank(i.getTemuTagUrl()))) {
                    Map<String, String> logisticsNoByConsignOrderNo = aliExpressCallService.getLogisticsNoByConsignOrderNo(allocation);
                    List<WhFbaAllocationItem> itemList = new ArrayList<>();
                    for (WhFbaAllocationItem item : allocation.getItems()) {
                        WhFbaAllocationItem updateWhFbaAllocationItem = new WhFbaAllocationItem();
                        updateWhFbaAllocationItem.setId(item.getId());
                        updateWhFbaAllocationItem.setTemuTagUrl(logisticsNoByConsignOrderNo.get(item.getTag()));
                        itemList.add(updateWhFbaAllocationItem);
                    }
                    whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(itemList);
                    SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(),  "获取物流单号成功");
                }
            } catch (ApiException e) {
                log.error(allocation.getFbaNo()+"获取物流单号失败!"+e.getMessage(), e);
                SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "获取物流单号失败!"+e.getMessage());
               // successFlag = false;
            }

            // 分配分拣筐
           /* try {
                AsnPickBox asnPickBox = allocation.getAsnPickBox();
                if (asnPickBox == null || asnPickBox.getNumber() == null){
                    asnPickBox = asnPickBoxService.allotAsnPickBox(pickupOrderNo);
                    if (Objects.isNull(asnPickBox)) {
                        SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "分配分拣筐失败!");
                        successFlag = false;
                    } else {
                        SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(),  "分配分拣筐成功");
                    }
                }
            }catch(Exception e){
                log.error(pickupOrderNo+"分配分拣筐失败!"+e.getMessage(), e);
                SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "分配分拣筐失败!"+e.getMessage());
               // successFlag = false;
            }*/
           /* if (successFlag && updateStatusFlag) {
                if (allocation.getTransitType() != null && allocation.getTransitType() == 1) {
                    String oldStatusName = AsnPrepareStatus.build(allocation.getStatus().toString()).getName();
                    WhFbaAllocation whFbaAllocation = new WhFbaAllocation();
                    whFbaAllocation.setId(allocation.getId());
                    whFbaAllocation.setStatus(AsnPrepareStatus.WAITING_DELIVER.intCode());
                    updateWhFbaAllocation(whFbaAllocation);
                    SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "订单单状态变更",
                            new String[][] { { "历史状态", oldStatusName }, { "更改状态", AsnPrepareStatus.WAITING_DELIVER.getName() } });
                    SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "");
                }
                StringRedisUtils.del(RedisConstant.SMT_CREATE_PICKUP_ORDER+fbaNo);
            }*/
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), e.getMessage());
        }
    }



    @Override
    public List<String> doUndoIntoCar(WhFbaAllocation whFbaAllocation, List<WhFbaAllocationItem> whFbaAllocationItemList,String lbxNo,String expressCompany) {
        List<WhFbaAllocationItem> updatedList = new ArrayList<>();
        for (WhFbaAllocationItem fbaAllocationItem : whFbaAllocationItemList) {
            WhFbaAllocationItem update=new WhFbaAllocationItem();
            update.setId(fbaAllocationItem.getId());
            update.setLoadBy(DataContextHolder.getUserId());
            update.setLoadTime(new Timestamp(System.currentTimeMillis()));
            update.setCompanyName(ShippingCompanyEnum.getNameByCode(expressCompany));
            updatedList.add(update);
        }
        whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(updatedList);

        List<String> lbxNoList =whFbaAllocation.getItems()
                .stream()
                .filter(item -> item.getTemuTagUrl() != null && !lbxNo.equals(item.getTemuTagUrl()) && item.getLoadBy() == null)
                .map(WhFbaAllocationItem::getTemuTagUrl)
                .distinct()
                .collect(Collectors.toList());;

        if (CollectionUtils.isEmpty(lbxNoList)) {
            ApvTrackQueryCondition queryCondition=new ApvTrackQueryCondition();
            queryCondition.setApvNo(whFbaAllocation.getFbaNo());
            List<ApvTrack> apvTracks = apvTrackService.queryApvTracks(queryCondition, null);
            List<ApvTrack> updateApvTracks = apvTracks.stream().map(apvTrack -> {
                ApvTrack update = new ApvTrack();
                update.setId(apvTrack.getId());
                update.setLoadUser(DataContextHolder.getUserId());
                update.setLoadDate(new Timestamp(System.currentTimeMillis()));
                return update;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(updateApvTracks)) {
                apvTrackService.batchUpdateApvTrack(updateApvTracks);
            }
            WhFbaAllocation update=new WhFbaAllocation();
            update.setId(whFbaAllocation.getId());
            update.setStatus(AsnPrepareStatus.LOADED.intCode());
            whFbaAllocationDao.updateWhFbaAllocation(update);
            SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), "PDA装车!");
        }
        return lbxNoList;
    }

    @Override
    public List<WarehouseLoadingSummary> queryTodayIntoCarTotal(Integer loadBy) {
        WhFbaAllocationQueryCondition query=new WhFbaAllocationQueryCondition();
        query.setLoadBy(loadBy);
        query.setLoaded(true);
        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationDao.queryWhFbaAllocationAndItems(query, null);

        List<WarehouseLoadingSummary> warehouseLoadingSummaries=new ArrayList<>();
        List<String> lbxNoList=new ArrayList<>();
        for (WhFbaAllocation whFbaAllocation : whFbaAllocationList) {
            List<WhFbaAllocationItem> items = whFbaAllocation.getItems();
            if (CollectionUtils.isEmpty(items)) continue;
            boolean allMatch = items.stream().allMatch(a -> StringUtils.isNotBlank(a.getCompanyName()));
            for (WhFbaAllocationItem item : items) {
                if (item.getLoadBy()==null || lbxNoList.contains(item.getTemuTagUrl())) continue;
                WarehouseLoadingSummary warehouseLoadingSummary=new WarehouseLoadingSummary();
                warehouseLoadingSummary.setLbxNo(item.getTemuTagUrl());
                warehouseLoadingSummary.setCanUndo(false);
                if (!allMatch){
                    warehouseLoadingSummary.setCanUndo(true);
                }
                warehouseLoadingSummary.setCollectCompanyName(item.getCompanyName());
                warehouseLoadingSummaries.add(warehouseLoadingSummary);
                lbxNoList.add(item.getTemuTagUrl());
            }
        }
        return warehouseLoadingSummaries;
    }

    @Override
    public void doSubmitBoxInformation(String fbaNo, List<WhFbaAllocationItem> whFbaAllocationItemList){
        String boxNoStr = StringRedisUtils.get(RedisConstant.PDA_FBA_PACKING + fbaNo);
        if (StringUtils.isBlank(boxNoStr)) {
           return;
        }
        WhFbaAllocationQueryCondition queryCondition=new WhFbaAllocationQueryCondition();
        queryCondition.setFbaNo(fbaNo);
        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationDao.queryWhFbaAllocationAndItems(queryCondition, null);
        Map<Integer, List<WhFbaAllocationItem>> boxNoMap=new HashMap<>();
        if (CollectionUtils.isNotEmpty(whFbaAllocationList) && CollectionUtils.isNotEmpty(whFbaAllocationList.get(0).getItems())) {
            whFbaAllocationList.get(0).getItems().forEach(i->i.setBoxNo(i.getBoxNo()==null?1:i.getBoxNo()));
            boxNoMap = whFbaAllocationList.get(0).getItems().stream().collect(Collectors.groupingBy(WhFbaAllocationItem::getBoxNo));
        }

        List<WhFbaAllocationItem> whFbaAllocationItems = JSONArray.parseArray(boxNoStr, WhFbaAllocationItem.class);
        Map<Integer, WhFbaAllocationItem> fbaAllocationItemMap = whFbaAllocationItems.stream().collect(Collectors.toMap(WhFbaAllocationItem::getBoxNo, i -> i));
        List<WhFbaAllocationItem> updateItemList=new ArrayList<>();
        for (WhFbaAllocationItem fbaAllocationItem : whFbaAllocationItemList) {
            WhFbaAllocationItem allocationItem = fbaAllocationItemMap.get(fbaAllocationItem.getBoxNo());
            if (allocationItem==null) {
                continue;
            }
            Double productLength = Optional.ofNullable(fbaAllocationItem.getProductLength()).orElse(0d);
            Double productWidth = Optional.ofNullable(fbaAllocationItem.getProductWidth()).orElse(0d);
            Double productHeight = Optional.ofNullable(fbaAllocationItem.getProductHeight()).orElse(0d);
            Double productWeight = Optional.ofNullable(fbaAllocationItem.getProductWeight()).orElse(0d);

            allocationItem.setProductLength(productLength);
            allocationItem.setProductWidth(productWidth);
            allocationItem.setProductHeight(productHeight);
            allocationItem.setProductWeight(productWeight);

            List<WhFbaAllocationItem> fbaAllocationItems = boxNoMap.get(fbaAllocationItem.getBoxNo());
            if (CollectionUtils.isEmpty(fbaAllocationItems)) {
                continue;
            }
            for (WhFbaAllocationItem item : fbaAllocationItems) {
                if (productLength<=0 || productWidth<=0 || productHeight<=0 || productWeight<=0) {
                    throw new RuntimeException("请输入正确的尺寸和重量信息");
                }
                WhFbaAllocationItem updateItem =new WhFbaAllocationItem();
                updateItem.setId(item.getId());
                updateItem.setBoxNo(item.getBoxNo());
                updateItem.setProductLength(productLength);
                updateItem.setProductWidth(productWidth);
                updateItem.setProductHeight(productHeight);
                updateItem.setProductWeight(productWeight);
                updateItemList.add(updateItem);
            }
        }
        StringRedisUtils.set(RedisConstant.PDA_FBA_PACKING + fbaNo,JSONArray.toJSONString(whFbaAllocationItems),60*60*24L);
        if (CollectionUtils.isNotEmpty(updateItemList)) {
            whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(updateItemList);
        }
    }

    @Override
    public void doSubmitFnSkuInformation(WhFbaAllocationItem item) {
        String fbaNo = item.getFbaNo();
        WhFbaAllocationQueryCondition queryCondition=new WhFbaAllocationQueryCondition();
        queryCondition.setFbaNo(fbaNo);
        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationDao.queryWhFbaAllocationAndItems(queryCondition, null);
        List<WhFbaAllocationItem> whFbaAllocationItemList = whFbaAllocationList
                .stream()
                .map(WhFbaAllocation::getItems)
                .flatMap(Collection::stream)
                .filter(i -> i.getFnSku().equals(item.getFnSku()) && (item.getBoxNo().equals(i.getBoxNo()) || (item.getBoxNo()==1 && i.getBoxNo()==null)))
                .collect(Collectors.toList());


        if (CollectionUtils.isNotEmpty(whFbaAllocationItemList)) {
            int sum = whFbaAllocationItemList.stream().mapToInt(t -> Optional.ofNullable(t.getLoadNum()).orElse(0)).sum();
            item.setLoadNum(Optional.ofNullable(item.getLoadNum()).orElse(0)-sum);
        }
        if (item.getUnboxed()< item.getLoadNum() || item.getLoadNum()<=0) {
            throw new RuntimeException("装箱数量不能大于播种数量！");
        }

        if (CollectionUtils.isEmpty(whFbaAllocationItemList) && item.getBoxNo()!=1) {
                   whFbaAllocationItemList=whFbaAllocationList.stream()
                    .map(WhFbaAllocation::getItems)
                    .flatMap(Collection::stream)
                    .filter(i -> i.getFnSku().equals(item.getFnSku()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(whFbaAllocationItemList)) {
                throw new RuntimeException("没有找到对应的fnsku");
            }
            Map<String,WhFbaAllocationItem> map=new HashMap<>();
            for (WhFbaAllocationItem whFbaAllocationItem : whFbaAllocationItemList) {
                WhFbaAllocationItem create = new WhFbaAllocationItem();
                BeanUtils.copyProperties(whFbaAllocationItem, create);
                create.setBoxNo(item.getBoxNo());
                create.setId(null);
                //查询redis是否先添加过箱子信息
                String boxNoStr = StringRedisUtils.get(RedisConstant.PDA_FBA_PACKING + fbaNo);
                WhFbaAllocationItem boxItem=new WhFbaAllocationItem();
                if (StringUtils.isNotBlank(boxNoStr)) {
                    List<WhFbaAllocationItem> whFbaAllocationItems = JSONArray.parseArray(boxNoStr, WhFbaAllocationItem.class);
                    boxItem = whFbaAllocationItems.stream().filter(f -> f.getBoxNo().equals(item.getBoxNo())).findFirst().orElse(null);
                }
                create.setProductWeight(boxItem==null?null:boxItem.getProductWeight());
                create.setProductLength(boxItem==null?null:boxItem.getProductLength());
                create.setProductHeight(boxItem==null?null:boxItem.getProductHeight());
                create.setProductWidth(boxItem==null?null:boxItem.getProductWidth());
                create.setLoadNum(item.getLoadNum());
                // 套装
                if (whFbaAllocationItem.getSuitFlag() != null && whFbaAllocationItem.getSuitFlag().equals(1)) {
                    create.setLoadingQuantity(
                            item.getLoadNum() * (whFbaAllocationItem.getSkuSuitNum() == null ? 0 : whFbaAllocationItem.getSkuSuitNum()));
                }
                else {
                    create.setLoadingQuantity(item.getLoadNum());
                }
                map.put(create.getProductSku()+create.getFnSku()+create.getBoxNo(), create);

            }
            if (MapUtils.isNotEmpty(map)) {
                  whFbaAllocationItemService.batchCreateWhFbaAllocationItem(new ArrayList<>(map.values()));
            }
            SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocationList.get(0).getId(), "PDA提交箱体信息"+item.getLoadDataMes() );
            return;
        }
        List<WhFbaAllocationItem> updateItemList = new ArrayList<>();
        for (WhFbaAllocationItem whFbaAllocationItem : whFbaAllocationItemList) {
            Integer oldLoadNum = Optional.ofNullable(whFbaAllocationItem.getLoadNum()).orElse(0);
            Integer oldLoadingQuantity = Optional.ofNullable(whFbaAllocationItem.getLoadingQuantity()).orElse(0);
            Integer loadNum = Optional.ofNullable(item.getLoadNum()).orElse(0);
            WhFbaAllocationItem updateItem =new WhFbaAllocationItem();
            updateItem.setId(whFbaAllocationItem.getId());
            updateItem.setLoadNum(loadNum+oldLoadNum);
            if (whFbaAllocationItem.getBoxNo()==null) {
                updateItem.setBoxNo(item.getBoxNo());
            }
            if (whFbaAllocationItem.getSuitFlag() != null && whFbaAllocationItem.getSuitFlag().equals(1)) {
                updateItem.setLoadingQuantity(
                        item.getLoadNum() * (whFbaAllocationItem.getSkuSuitNum() == null ? 0 : whFbaAllocationItem.getSkuSuitNum())+oldLoadingQuantity);
            }
            else {
                updateItem.setLoadingQuantity(item.getLoadNum()+oldLoadingQuantity);
            }
            updateItemList.add(updateItem);
        }
        whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(updateItemList);
        SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocationList.get(0).getId(), String.format("PDA-FBA fnsku:%s,装箱信息：%s！",item.getFnSku(),item.getLoadDataMes()));
    }

    @Override
    public void doFinishFbaBox(String fbaNo) {
        WhFbaAllocationQueryCondition queryCondition=new WhFbaAllocationQueryCondition();
        queryCondition.setFbaNo(fbaNo);
        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationDao.queryWhFbaAllocationAndItems(queryCondition, null);
        if (CollectionUtils.isEmpty(whFbaAllocationList)) {
            throw new RuntimeException("没有找到对应的FBA号"+fbaNo);
        }
        WhFbaAllocation dbAllocation = whFbaAllocationList.get(0);
        Map<String, WhFbaAllocationItem> totalItemMap = new HashMap<>();
        Map<String, List<WhFbaAllocationItem>> fnSkuMap =dbAllocation.getItems().stream()
                .collect(Collectors.groupingBy(WhFbaAllocationItem::getFnSku));
        for (Map.Entry<String, List<WhFbaAllocationItem>> entry : fnSkuMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue()) || entry.getValue() == null)
                continue;
            List<WhFbaAllocationItem> items = entry.getValue();
            for (WhFbaAllocationItem item : items) {
                String boxNo = item.getBoxNo() == null ? "1" : String.valueOf(item.getBoxNo());
                Integer loadNum = item.getLoadNum() == null ? 0 : item.getLoadNum();
                Integer quantity = item.getQuantity() == null ? 0 : item.getQuantity();
                Integer pickQuantity = item.getPickQuantity() == null ? 0 : item.getPickQuantity();
                Integer gridQuantity = item.getGridQuantity() == null ? 0 : item.getGridQuantity();
                // 套装
                if (item.getSuitFlag() != null && item.getSuitFlag().equals(1)) {
                    pickQuantity = items.stream()
                            .map(i -> Math.round((float) (i.getPickQuantity() == null ? 0 : i.getPickQuantity())
                                    / (i.getSkuSuitNum() == null ? 1 : i.getSkuSuitNum())))
                            .sorted().collect(Collectors.toList()).get(0);
                    gridQuantity = items.stream()
                            .map(i -> Math.round((float) (i.getGridQuantity() == null ? 0 : i.getGridQuantity())
                                    / (i.getSkuSuitNum() == null ? 1 : i.getSkuSuitNum())))
                            .sorted().collect(Collectors.toList()).get(0);
                }
                if (gridQuantity>pickQuantity){
                    gridQuantity=pickQuantity;
                }

                WhFbaAllocationItem totalItem = totalItemMap.get(item.getFnSku());
                List<Integer> boxNoList =new ArrayList<>();
                if (totalItem == null) {
                    totalItem = new WhFbaAllocationItem();
                    totalItem.setBoxNo(Integer.valueOf(boxNo));
                    totalItem.setFnSku(item.getFnSku());
                    totalItem.setLoadNum(loadNum);
                    totalItem.setQuantity(quantity);
                    totalItem.setPickQuantity(pickQuantity);
                    totalItem.setGridQuantity(gridQuantity);
                    if (loadNum>0) {
                        boxNoList.add(Integer.valueOf(boxNo));
                    }
                    totalItem.setBoxNoList(boxNoList);
                }
                else if (!totalItem.getBoxNo().equals(Integer.valueOf(boxNo))) {
                    if (CollectionUtils.isNotEmpty(totalItem.getBoxNoList())) {
                        boxNoList = new ArrayList<>(totalItem.getBoxNoList());
                    }
                    if (loadNum>0) {
                        boxNoList.add(Integer.valueOf(boxNo));
                    }
                    totalItem.setBoxNo(Integer.valueOf(boxNo));
                    totalItem.setLoadNum(totalItem.getLoadNum() + loadNum);
                    totalItem.setBoxNoList(boxNoList);
                }
                totalItem.setProductSku(item.getProductSku());
                totalItemMap.put(item.getFnSku(), totalItem);
            }
        }
        totalItemMap.forEach((k,v)->{
            if (StringUtils.isBlank(k) || v==null) {
                return;
            }
            Integer gridQuantity = Optional.ofNullable(v.getGridQuantity()).orElse(0);
            Integer loadNum = Optional.ofNullable(v.getLoadNum()).orElse(0);
            if (gridQuantity-loadNum>0){
                throw new RuntimeException( "FBA号"+fbaNo+"的"+v.getFnSku()+"未装箱完成，请继续装箱！");
            }
        });

        if (!dbAllocation.isFba()) {
            //解绑拣货任务周转筐
            whFbaAllocationHandleService.boxFinishUnbindBoxNo(dbAllocation);
        }
        StringRedisUtils.del(RedisConstant.PDA_FBA_PACKING+fbaNo);
        SystemLogUtils.FBAALLOCATIONLOG.log(dbAllocation.getId(), "PDA-FBA装箱完成！");
    }

    @Override
    public boolean syncJitFbaPickupOrderNos(String fbaNo) {
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setFbaNo(fbaNo);
        query.setIsAsn(true);
        query.setQueryWhAsnExtra(true);
        query.setPackageMethodList(AsnPackageMethodEnum.getJitEnumCodeList());
        List<WhFbaAllocation> allocationList = whFbaAllocationDao.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(allocationList)) {
            return false;
        }
        WhFbaAllocation whFbaAllocation = allocationList.get(0);
        try {
            JSONArray jsonArray = aliExpressCallService.getLogisticsNoByPurchaseOrderNo(whFbaAllocation);

            List<WhFbaAllocationItem> items = whFbaAllocation.getItems();
            WhAsnExtra whAsnExtra = whFbaAllocation.getWhAsnExtra();
            Map<String,String> map=new HashMap<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jObject = jsonArray.getJSONObject(i);
                String bizStatus = jObject.getString("biz_status");
                String logisticsNo = jObject.getString("logistics_no");
                String consignOrderNo = jObject.getString("consign_order_no");
                if (StringUtils.isBlank(consignOrderNo) || "-99".equals(bizStatus)){
                    continue;
                }
                map.put("logistics_no",logisticsNo);
                map.put("consign_order_no",consignOrderNo);
                map.put("pickup_order_no",jObject.getString("pickup_order_no"));
            }
            if (MapUtils.isEmpty(map)) {
               return false;
            }
            List<WhFbaAllocationItem> itemList = new ArrayList<>();
            for (WhFbaAllocationItem item :items) {
                WhFbaAllocationItem updateWhFbaAllocationItem = new WhFbaAllocationItem();
                updateWhFbaAllocationItem.setId(item.getId());
                updateWhFbaAllocationItem.setTemuTagUrl(map.get("logistics_no"));
                updateWhFbaAllocationItem.setTag(map.get("consign_order_no"));
                itemList.add(updateWhFbaAllocationItem);
                item.setTag(map.get("consign_order_no"));
            }
            if (CollectionUtils.isNotEmpty(itemList)) {
                whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(itemList);
            }
            WhAsnExtra updateWhAsnExtra = new WhAsnExtra();
            updateWhAsnExtra.setId(whAsnExtra.getId());
            updateWhAsnExtra.setPickupOrderId(map.get("pickup_order_no"));
            whAsnExtraService.updateWhAsnExtra(updateWhAsnExtra);

            try {
                JSONObject jsonObject = aliExpressCallService.getPickupOrderNo(allocationList);

                WhFbaAllocation updateWhFbaAllocation = new WhFbaAllocation();
                updateWhFbaAllocation.setId(whFbaAllocation.getId());
                updateWhFbaAllocation.setShippingOrderNo(jsonObject.getString("fulfill_pickup_order_code"));
                whFbaAllocationDao.updateWhFbaAllocation(updateWhFbaAllocation);

                SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(),  "获取发货单,揽收单，LBX号，服务商侧运单号成功");
            } catch (Exception e) {
                SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(),  "获取发货单，LBX号成功");
                SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(),  "查询揽收单接口失败！"+e.getMessage());
                log.error(e.getMessage(),e);
                return true;
            }
        } catch (Exception e) {
            log.error("查询CO发货单接口失败！"+e.getMessage(),e);
            SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(),  "查询CO发货单接口失败！"+e.getMessage());
            return false;
        }
        return true;
    }

    private void deleteTemuPdf(String endDeliverDate) {
        if (StringUtils.isBlank(endDeliverDate))
            endDeliverDate = DateUtils.dateToString(DateUtils.getBeforeDate(new Date(), 1), DateUtils.DEFAULT_FORMAT);

        String startDeliverDate = DateUtils.dateToString(
                DateUtils.getBeforeDate(DateUtils.stringToDate(endDeliverDate, DateUtils.DEFAULT_FORMAT), 1),
                DateUtils.DEFAULT_FORMAT);
        WhScanShipmentQueryCondition queryCondition = new WhScanShipmentQueryCondition();
        queryCondition.setFromLoadDate(startDeliverDate);
        queryCondition.setToLoadDate(endDeliverDate);
        queryCondition.setPlatform(SaleChannel.CHANNEL_TEMU);
        List<WhScanShipment> shipments = scanShipmentService.queryWhScanShipments(queryCondition, null);
        if (CollectionUtils.isEmpty(shipments))
            return;
        shipments.forEach(s -> {
            String mergePdfPath = PdfUtils.STATIC_FILE_JITU_PATH + s.getExpressOrderNo() + ".pdf";
            PdfUtils.deleteFile(mergePdfPath);
        });
    }

    public String jitCreateCoOrderv2(List<Integer> ids) {

        if (JedisUtils.exists(ApvTaskRedisLock.ASN_CREATE_JIT_CO_ORDER.getName())) {
            log.warn("---invoke SmtCreatePickupOrderJobHandler  createJitCoOrder --- 有账号正在创建发货单");
            throw new RuntimeException("有账号正在创建发货单");
        } else {
            JedisUtils.set(ApvTaskRedisLock.ASN_CREATE_JIT_CO_ORDER.getName(), "lock", 600L);
        }
        String result = null;
        try {
            List<WhFbaAllocation> allocationList;
            WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
            if (CollectionUtils.isNotEmpty(ids)) {
                query.setIds(ids);
            }
            query.setIsAsn(true);
            query.setQueryWhAsnExtra(true);
            query.setStatusList(Arrays.asList(AsnPrepareStatus.WAITING_GEN.intCode(),AsnPrepareStatus.CHECK_PRINT.intCode(),AsnPrepareStatus.PICK_STOCK_OUT.intCode()));
            query.setPackageMethodList(AsnPackageMethodEnum.getJitEnumCodeList());
            allocationList = queryWhFbaAllocationAndItems(query, null);
            allocationList = allocationList.stream().filter(a -> a.getWhAsnExtra() != null &&
                    StringUtils.isNotBlank(a.getWhAsnExtra().getReceiptAddress())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(allocationList)) {
                throw new RuntimeException("符合操作条件的数据为0！");
            }
            //待合单状态
            List<WhFbaAllocation> normalAllocationList = allocationList.stream().filter(a -> a.getStatus().
                    equals(AsnPrepareStatus.WAITING_GEN.intCode()) && a.getTransitType() == null
                            && a.getItems().stream().allMatch(i -> StringUtils.isBlank(i.getTemuTagUrl())))
                    .collect(Collectors.toList());

            List<WhFbaAllocation> splitAllocationList = allocationList.stream().
                    filter(a -> (a.getStatus().equals(AsnPrepareStatus.CHECK_PRINT.intCode())
                            || a.getStatus().equals(AsnPrepareStatus.PICK_STOCK_OUT.intCode()))
                            && a.getTransitType() != null && a.getTransitType() == 1
                                && a.getItems().stream().allMatch(i -> i.getBoxNo() != null)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(normalAllocationList) && CollectionUtils.isEmpty(splitAllocationList)){
                throw new RuntimeException("符合操作条件的数据为0！");
            }
            WhFbaAllocationService whFbaAllocationService = (WhFbaAllocationService) AopContext.currentProxy();

            // 普通
            if (CollectionUtils.isNotEmpty(normalAllocationList)) {
                for (WhFbaAllocation whFbaAllocation:normalAllocationList) {
                    try {
                        // 发货单不为空的，只需要同步LBX
                        if (StringUtils.isBlank(whFbaAllocation.getItems().get(0).getTag())){
                            whFbaAllocationService.jitCreateCoOrder(whFbaAllocation, false);
                        }
                        rDelayedQueue.offer(whFbaAllocation.getFbaNo(), 30, TimeUnit.SECONDS);
                    } catch (Exception e) {
                        log.error(e.getMessage(),e);
                        SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), e.getMessage());
                        if (StringUtils.isBlank(result) )
                            result = e.getMessage();
                    }

                }
            }
            // 拆包
            if (CollectionUtils.isNotEmpty(splitAllocationList)) {
                for (WhFbaAllocation whFbaAllocation:splitAllocationList) {
                    boolean succFlag = false;
                    try {
                         // 拆包的，判断是否，先取消,条目发货单号一致时需要取消
                        // 发货单只有一条的
                         if (whFbaAllocation.getItems().stream().map(i-> i.getTag()).distinct().count() == 1) {
                             String cancelResult = whFbaAllocationService.cancelJitOrder(whFbaAllocation);
                             if (StringUtils.isNotBlank(cancelResult))
                                 continue;
                         }
                        Map<String, List<WhFbaAllocationItem>> itemMap = whFbaAllocation.getItems().stream()
                                .filter(i -> i.getId() != null)
                                .collect(Collectors.groupingBy(
                                        item -> item.getBoxNo().toString()));
                        whFbaAllocation.setItems(null);
                        Iterator<Map.Entry<String, List<WhFbaAllocationItem>>> iterator = itemMap.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, List<WhFbaAllocationItem>> entry = iterator.next();
                            List<WhFbaAllocationItem> items = entry.getValue();
                            if (items.stream().anyMatch(i -> i.getTag() != null)) {
                                // LBX为空
                                if (StringUtils.isBlank(items.get(0).getTemuTagUrl())) {
                                    rDelayedQueue.offer(whFbaAllocation.getFbaNo(), 30, TimeUnit.SECONDS);
                                }
                                continue;
                            }
                            try {
                                WhFbaAllocation targetAllocation = new WhFbaAllocation();
                                BeanUtils.copyProperties(whFbaAllocation,targetAllocation);
                                targetAllocation.setItems(entry.getValue());
                                whFbaAllocationService.jitCreateCoOrder(targetAllocation, true);
                                succFlag = true;
                            } catch (Exception e) {
                                log.error(e.getMessage(),e);
                                SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), "箱号【"+items.get(0).getBoxNo()+"】，"+e.getMessage());
                                if (StringUtils.isBlank(result) )
                                    result = e.getMessage();
                            }
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(),e);
                        SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), "拆包失败，"+e.getMessage());
                    }
                    if (succFlag)  rDelayedQueue.offer(whFbaAllocation.getFbaNo(), 30, TimeUnit.SECONDS);
                }
            }
        } finally {
            JedisUtils.del(ApvTaskRedisLock.ASN_CREATE_JIT_CO_ORDER.getName());
        }
        if (StringUtils.isNotBlank(result)){
            result = "部分失败，详细查看日志";
        }
        return result;
    }

    // 取消发货单和揽收单
    @Override
    public String cancelJitOrder(WhFbaAllocation whFbaAllocation) {
        // 置空单据信息 揽收单号，服务号，追踪号，发货单号
        WhFbaAllocationService whFbaAllocationService = (WhFbaAllocationService) AopContext.currentProxy();
        String result;
        // 取消揽收单
       /* if (StringUtils.isNotBlank(whFbaAllocation.getShippingOrderNo())){
            result = whFbaAllocationService.cancelJitPickOrder(whFbaAllocation);
            if (StringUtils.isNotBlank(result)) {
                SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), result);
                return result;
            } else {
                SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(),"取消揽收单成功,"+whFbaAllocation.getWhAsnExtra().getPickupOrderId());
            }
        }*/
        // 取消发货单
        if (whFbaAllocation.getItems().stream().anyMatch(i -> StringUtils.isNotBlank(i.getTag()))){
            result = whFbaAllocationService.cancelJitCoOrder(whFbaAllocation);
            if (StringUtils.isNotBlank(result)) {
                SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), result);
                return result;
            } else {
                SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(),"取消发货单成功,"+whFbaAllocation.getItems().get(0).getTag());
            }
        }
        //取成功将lbx，揽收单设置为空
        whFbaAllocation.setShippingOrderNo(null);
        whFbaAllocation.getWhAsnExtra().setPickupOrderId(null);
        whFbaAllocation.getItems().forEach( i -> {i.setTag(null);i.setTemuTagUrl(null);});
        return null;
    }

    @Override
    @Transactional
    public String cancelJitCoOrder(WhFbaAllocation whFbaAllocation) {
        try {
            whFbaAllocationDao.updateWhFbaAllocationByCancel(whFbaAllocation,true);
            aliExpressCallService.cancelJitCoOrder(whFbaAllocation);
        } catch (ApiException e) {
            log.error(e.getMessage(),e);
            return e.getMessage();
        }
        return  null;
    }

    @Override
    public String cancelJitPickOrder(WhFbaAllocation whFbaAllocation) {
        try {
            whFbaAllocationDao.updateWhFbaAllocationByCancel(whFbaAllocation,false);
            aliExpressCallService.cancelJitPickOrder(whFbaAllocation);
        } catch (ApiException e) {
            log.error(e.getMessage(),e);
            return e.getMessage();
        }
        return  null;
    }

    //@Transactional
    public String jitCreateCoOrder(WhFbaAllocation allocation, boolean splitFlag) throws ApiException {
        // 根据到仓地址与类型分组
        Integer createNum = 1;
        String key = splitFlag ? allocation.getFbaNo()+"-"+allocation.getItems().get(0).getBoxNo() : allocation.getFbaNo();
        try {

            if (StringRedisUtils.exists(RedisConstant.SMT_CREATE_CO_ORDER_NUM + key)) {
                Integer retryNum = Integer.valueOf(StringRedisUtils.get(RedisConstant.SMT_CREATE_CO_ORDER_NUM + allocation.getFbaNo()));
                createNum = ++retryNum;
                if ((retryNum) > SMT_RETRY_NUM) {
                    throw new RuntimeException("当天已创建失败已超过"+(SMT_RETRY_NUM)+"次，请排查原因！");
                }
            }
            Integer packageMethod=AsnPackageMethodEnum.JIT_HALF.getCode();
            if (allocation.getWhAsnExtra()!=null && AsnPackageMethodEnum.JIT.getCode().equals(allocation.getWhAsnExtra().getPackageMethod())) {
                packageMethod=AsnPackageMethodEnum.JIT.getCode();
            }

            List<CreatCoOrderResponse> jitCoOrderList = aliExpressCallService.createJitCoOrder(packageMethod, Collections.singletonList(allocation));
            if (CollectionUtils.isEmpty(jitCoOrderList)) {
                throw new RuntimeException("创建失败");
            }
            CreatCoOrderResponse creatCoOrderResponse = jitCoOrderList.get(0);
            if ((creatCoOrderResponse.getSuccess()!=null && !creatCoOrderResponse.getSuccess()) || StringUtils.isBlank(creatCoOrderResponse.getConsignOrderNo())) {
                if (!syncJitFbaPickupOrderNos(allocation.getFbaNo())) {
                    throw new RuntimeException("创建发货单失败，"+creatCoOrderResponse.getErrorMsg());
                }
            } else {
                List<WhFbaAllocationItem> updateItems = new ArrayList<>();
                for (WhFbaAllocationItem item : allocation.getItems()) {
                    WhFbaAllocationItem updateItem = new WhFbaAllocationItem();
                    updateItem.setId(item.getId());
                    updateItem.setTag(creatCoOrderResponse.getConsignOrderNo());
                    updateItems.add(updateItem);
                }
                whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(updateItems);
            }
            // 全部都成功
          /*  if (otherSuccess) {
                WhFbaAllocation fbaAllocation = new WhFbaAllocation();
                fbaAllocation.setId(allocation.getId());
                // 非拆包
                if (allocation.getTransitType() != null && allocation.getTransitType() == 1) {
                    if (ApvTypeEnum.SS.getCode().equalsIgnoreCase(allocation.getApvType())) {
                        fbaAllocation.setStatus(AsnPrepareStatus.SINGLETON_TOUCHING.intCode());
                    } else if (ApvTypeEnum.SM.getCode().equalsIgnoreCase(allocation.getApvType())) {
                        fbaAllocation.setStatus(AsnPrepareStatus.EXCESSIVE_PARTS_TOUCHING.intCode());
                    } else if (ApvTypeEnum.MM.getCode().equalsIgnoreCase(allocation.getApvType())) {
                        fbaAllocation.setStatus(AsnPrepareStatus.MULTI_TOUCHING.intCode());
                    }
                } else {
                    fbaAllocation.setStatus(AsnPrepareStatus.WAITING_DELIVER.intCode());
                }
                whFbaAllocationDao.updateWhFbaAllocation(fbaAllocation);
            }*/

            if (createNum > 1)
                StringRedisUtils.del(RedisConstant.SMT_CREATE_CO_ORDER_NUM + key);
            SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "创建发货单成功,发货单号："+creatCoOrderResponse.getConsignOrderNo());
            return null;
        } finally {
            StringRedisUtils.set(RedisConstant.SMT_CREATE_CO_ORDER_NUM+key,createNum.toString(),24 * 60 * 60L);
        }
    }

    // 创建发货单
    @Override
    public String jitCreateCoOrder(List<WhFbaAllocation> allocationList) {
        // 根据到仓地址与类型分组
        Map<String, List<WhFbaAllocation>> allocationMap = allocationList.stream().filter(a -> a.getWhAsnExtra() != null && StringUtils.isNotBlank(a.getWhAsnExtra().getReceiptAddress()))
                .collect(Collectors.groupingBy(w ->w.getAccountNumber() + w.getWhAsnExtra().getPackageMethod() + w.getWhAsnExtra().getReceiptAddress()));
        if (MapUtils.isEmpty(allocationMap))
            return "符合操作条件的数据为0";
        Map<Integer,String> msgMap = new HashMap<>();
        AtomicBoolean allSuccess = new AtomicBoolean(true);
        List<WhAsnExtra> whAsnExtras = new ArrayList<>();
        List<WhFbaAllocation> fbaAllocationList=new ArrayList<>();
        for (Map.Entry<String, List<WhFbaAllocation>> entry : allocationMap.entrySet()) {
            CommonUtils.batchResolve(entry.getValue(),20,(whFbaAllocationList) -> {
                Integer packageMethod=AsnPackageMethodEnum.JIT_HALF.getCode();
                if (whFbaAllocationList.get(0)!=null && whFbaAllocationList.get(0).getWhAsnExtra()!=null && AsnPackageMethodEnum.JIT.getCode().equals( whFbaAllocationList.get(0).getWhAsnExtra().getPackageMethod())) {
                    packageMethod=AsnPackageMethodEnum.JIT.getCode();
                }
                try {
                    List<CreatCoOrderResponse> jitCoOrderList = aliExpressCallService.createJitCoOrder(packageMethod, whFbaAllocationList);
                    if (CollectionUtils.isEmpty(jitCoOrderList)) {
                        allSuccess.set(false);
                        msgMap.putAll(whFbaAllocationList.stream().collect(Collectors.toMap(w -> w.getId(), w -> "创建发货单失败")));
                        return;
                    }
                    Map<String, CreatCoOrderResponse> orderResponseMap = jitCoOrderList.stream().collect(Collectors.toMap(w -> w.getPurchaseOrderNo(), w -> w, (v1, v2) -> v1));
                    for (WhFbaAllocation whFbaAllocation : whFbaAllocationList) {
                        WhAsnExtra whAsnExtra = whFbaAllocation.getWhAsnExtra();
                        if (whAsnExtra==null || orderResponseMap.get(whAsnExtra.getPurchaseOrderNo())==null) {
                            allSuccess.set(false);
                            msgMap.put(whFbaAllocation.getId(), "创建发货单失败! 发货单号对应备货单号不存在");
                            return;
                        }
                        CreatCoOrderResponse creatCoOrderResponse = orderResponseMap.get(whAsnExtra.getPurchaseOrderNo());
                        if ((creatCoOrderResponse.getSuccess()!=null && !creatCoOrderResponse.getSuccess()) || StringUtils.isBlank(creatCoOrderResponse.getConsignOrderNo())) {
                            if (!syncJitFbaPickupOrderNos(whFbaAllocation.getFbaNo())) {
                                allSuccess.set(false);
                                msgMap.put(whFbaAllocation.getId(), "创建发货单失败，"+creatCoOrderResponse.getErrorMsg());
                                return;
                            }
                        }
                        WhAsnExtra updateWhAsnExtra = new WhAsnExtra();
                        updateWhAsnExtra.setId(whAsnExtra.getId());
                        updateWhAsnExtra.setConsignOrderNo(creatCoOrderResponse.getConsignOrderNo());
                        whAsnExtras.add(updateWhAsnExtra);

                        WhFbaAllocation fbaAllocation = new WhFbaAllocation();
                        fbaAllocation.setId(whFbaAllocation.getId());
                        fbaAllocation.setStatus(AsnPrepareStatus.WAITING_DELIVER.intCode());
                        fbaAllocationList.add(fbaAllocation);

                        msgMap.put(whFbaAllocation.getId(), "创建发货单成功,发货单号："+creatCoOrderResponse.getConsignOrderNo());
                    }
                } catch (Exception e) {
                    allSuccess.set(false);
                    log.error(e.getMessage(),e);
                    msgMap.putAll(whFbaAllocationList.stream().collect(Collectors.toMap(w -> w.getId(), w -> "创建发货单失败，"+e.getMessage())));
                }
            });
        }
        if (CollectionUtils.isNotEmpty(whAsnExtras)) {
            whAsnExtraService.batchUpdateWhAsnExtra(whAsnExtras);
        }

        if (CollectionUtils.isNotEmpty(fbaAllocationList)) {
            whFbaAllocationDao.batchUpdateWhFbaAllocation(fbaAllocationList);
        }
        for (Map.Entry<Integer, String> entrie : msgMap.entrySet()) {
            SystemLogUtils.FBAALLOCATIONLOG.log(entrie.getKey(), entrie.getValue());
        }
        return  (allSuccess.get() ? null : "部分失败，请刷新查看日志失败详情");
    }

    // 查询合单列表
    @Override
    public List<TransferOrderMegerVo> queryTransferOrderMegers(TransferOrderMegerDto query, Pager page) {

        Assert.notNull(query, "query is null!");
        List<TransferOrderMegerVo> transferOrderMegerVoList = whFbaAllocationDao.queryTransferOrderMegers(query, page);
        return transferOrderMegerVoList;
    }
    
}