package com.estone.transfer.service.impl;

import com.estone.apv.bean.TransportExceptionRecords;
import com.estone.apv.enums.TransportExceptionWarehouseType;
import com.estone.apv.service.TransportExceptionRecordsService;
import com.estone.common.util.DateUtils;
import com.estone.transfer.bean.*;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.transfer.service.WhFbaAllocationItemService;
import com.estone.transfer.service.WhFbaAllocationService;
import com.estone.transfer.service.WhFbaShipmentService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * WhFbaAllocationService扩展实现类
 * 用于处理新的FBA交运流程
 * <AUTHOR>
 */
@Slf4j
@Service
public class WhFbaAllocationServiceExtImpl {

    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Resource
    private WhFbaShipmentService whFbaShipmentService;

    @Resource
    private TransportExceptionRecordsService transportExceptionRecordsService;


    /**
     * 根据货件单号前缀和发货单号匹配FBA发货单信息
     * @param shipmentId 货件单号（可能包含随机字符串）
     * @param fbaNo 发货单号（YST号）
     * @param boxNum 箱号
     * @return 匹配的FBA发货单列表
     */
    public List<WhFbaAllocation> matchFbaAllocationByShipmentIdAndFbaNo(String shipmentId, String fbaNo, String boxNum) {
        log.info("匹配FBA发货单信息，货件单号前缀: {}, 发货单号: {}, 箱号：{}", shipmentId, fbaNo, boxNum);
        List<WhFbaAllocation> result = new ArrayList<>();
        
        // 查询FBA新流程 - 待发货的货件单
        WhFbaShipmentQueryCondition shipmentQuery = new WhFbaShipmentQueryCondition();
        shipmentQuery.setStatus(AsnPrepareStatus.WAITING_DELIVER.intCode());
        shipmentQuery.setFbaNo(fbaNo);
        List<WhFbaShipment> whFbaShipments = whFbaShipmentService.queryWhFbaShipmentAndItems(shipmentQuery, null);
        // 根据货件单号前缀和发货单号匹配
        if (CollectionUtils.isEmpty(whFbaShipments)) {
            log.info("没有匹配到FBA发货单信息!");
            return result;
        }
        for (WhFbaShipment whFbaShipment : whFbaShipments) {
            if (StringUtils.isNotBlank(shipmentId) && StringUtils.isNotBlank(whFbaShipment.getShipmentId())) {
                if (StringUtils.startsWith(shipmentId, whFbaShipment.getShipmentId()) && StringUtils.equals(whFbaShipment.getFbaNo(), fbaNo)) {
                    List<Integer> boxNumbers = whFbaShipment.getBoxNumbers();
                    if (StringUtils.isNotBlank(boxNum) && !boxNumbers.contains(Integer.valueOf(boxNum))) {
                       continue;
                    }
                    WhFbaAllocation fbaAllocation = new WhFbaAllocation();
                    fbaAllocation.setFbaNo(whFbaShipment.getFbaNo());
                    fbaAllocation.setShipmentId(whFbaShipment.getShipmentId());
                    fbaAllocation.setShippingOrderNo(whFbaShipment.getShippingOrderNo());
                    fbaAllocation.setTrackingNumber(whFbaShipment.getTrackingNumberByTms());
                    fbaAllocation.setBoxTotal(whFbaShipment.getBoxTotal());
                    fbaAllocation.setBatNo(shipmentId);
                    fbaAllocation.setPushTime(whFbaShipment.getConfirmTime());
                    fbaAllocation.setShippingCompany(whFbaShipment.getShippingCompany());
                    fbaAllocation.setShippingMethod(whFbaShipment.getShippingMethodByTms());
                    result.add(fbaAllocation);
                }
            }

        }
        log.info("匹配到 {} 条FBA发货单信息", result.size());
        return result;
    }

    /**
     * 处理新的FBA交运流程
     * @param requests FBA交运请求列表
     * @return 处理结果
     */
    public ResponseJson doDeliverFbaRequests(List<FbaDeliverRequest> requests) {
        log.info("处理FBA交运请求，请求数量: {}", requests.size());
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        
        if (CollectionUtils.isEmpty(requests)) {
            response.setMessage("请求参数为空");
            return response;
        }
        
        List<String> errorMessages = new ArrayList<>();
        List<String> errorBatNos = new ArrayList<>();
        
        for (FbaDeliverRequest request : requests) {
            // 验证必要参数
            if (StringUtils.isBlank(request.getShipmentId())) {
                errorMessages.add("货件单号不能为空");
                errorBatNos.add(request.getBatNo());
                continue;
            }
            
            // 验证箱数是否匹配
            if (request.getScannedBoxCount() == null || request.getTotalBoxCount() == null) {
                errorMessages.add(String.format("货件单号 %s 的已扫描箱数或总箱数为空", request.getShipmentId()));
                errorBatNos.add(request.getBatNo());
                continue;
            }
            
            if (!request.getScannedBoxCount().equals(request.getTotalBoxCount())) {
                errorMessages.add(String.format("货件单号 %s 的已扫描箱数 %d 与总箱数 %d 不匹配", 
                    request.getShipmentId(), request.getScannedBoxCount(), request.getTotalBoxCount()));
                errorBatNos.add(request.getBatNo());
                continue;
            }
            
            // 查找对应的FBA发货单
            List<WhFbaAllocation> allocations = matchFbaAllocationByShipmentIdAndFbaNo(request.getShipmentId(), request.getFbaNo(), null);
            
            if (CollectionUtils.isEmpty(allocations)) {
                errorMessages.add(String.format("未找到货件单号 %s 对应的FBA发货单", request.getShipmentId()));
                errorBatNos.add(request.getBatNo());
                continue;
            }
            String errorMsg = null;
            WhFbaAllocation allocation = allocations.get(0);
            // 执行交运操作
            try {
                ResponseJson deliverResponse = whFbaAllocationService.doDeliver(allocation.getShipmentId());
                if (!deliverResponse.isSuccess()) {
                    errorMessages.add(String.format("货件单号 %s 交运失败: %s", request.getShipmentId(), deliverResponse.getMessage()));
                    errorBatNos.add(request.getBatNo());
                }
            } catch (Exception e) {
                log.error("FBA交运异常", e);
                errorMsg=String.format("货件单号 %s 交运异常: %s",
                        request.getShipmentId(), e.getMessage());
                errorMessages.add(errorMsg);
            }finally {
                if (!response.isSuccess()) {
                    // 记录异常信息
                    transportExceptionRecordsService.createTransportExceptionRecords(
                            new TransportExceptionRecords(
                                    allocation.getShippingOrderNo(),
                                    request.getFbaNo(),
                                    TransportExceptionWarehouseType.FBA.intCode(),
                                    "PDA-FBA出库单交运",
                                    errorMsg != null ? errorMsg : response.getMessage()
                            )
                    );
                }
            }
        }
        
        if (errorMessages.isEmpty()) {
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("所有FBA发货单交运成功");
        } else {
            String errorMsg = String.join("; ", errorMessages);
            log.error("FBA交运失败: {}",errorMsg);
            response.setMessage(errorMsg);
            if (CollectionUtils.isNotEmpty(errorBatNos)){
                Map<String,Object> errorBatNosMap = new HashMap<>();
                errorBatNosMap.put("errorBatNos",errorBatNos);
                response.setBody(errorBatNosMap);
            }
        }
        
        return response;
    }

    /**
     * 根据货件单号前缀和发货单号匹配待装车的FBA发货单信息
     * @param shipmentId 货件单号（可能包含随机字符串）
     * @param fbaNo 发货单号（YST号）
     * @param boxNum 箱号
     * @return 匹配的FBA发货单列表
     */
    public List<WhFbaAllocation> matchLoadFbaAllocationByShipmentIdAndFbaNo(String shipmentId, String fbaNo, String boxNum) {
        log.info("匹配待装车FBA发货单信息，货件单号前缀: {}, 发货单号: {}, 箱号：{}", shipmentId, fbaNo, boxNum);
        List<WhFbaAllocation> result = new ArrayList<>();
        
        // 查询FBA新流程 - 待装车的货件单
        WhFbaShipmentQueryCondition shipmentQuery = new WhFbaShipmentQueryCondition();
        shipmentQuery.setStatus(AsnPrepareStatus.DELIVER.intCode());
        shipmentQuery.setFbaNo(fbaNo);
        shipmentQuery.setFromConfirmTime(DateUtils.dateToString(DateUtils.getBeforeDate(new java.util.Date(), 7), DateUtils.DEFAULT_FORMAT));
        List<WhFbaShipment> whFbaShipments = whFbaShipmentService.queryWhFbaShipmentAndItems(shipmentQuery, null);
        
        // 根据货件单号前缀和发货单号匹配
        if (CollectionUtils.isEmpty(whFbaShipments)) {
            log.info("没有匹配到待装车的FBA发货单信息!");
            return result;
        }
        
        for (WhFbaShipment whFbaShipment : whFbaShipments) {
            if (StringUtils.isNotBlank(shipmentId) && StringUtils.isNotBlank(whFbaShipment.getShipmentId())) {
                if (StringUtils.startsWith(shipmentId, whFbaShipment.getShipmentId()) && StringUtils.equals(whFbaShipment.getFbaNo(), fbaNo)) {
                    List<Integer> boxNumbers = whFbaShipment.getBoxNumbers();
                    if (StringUtils.isNotBlank(boxNum) && !boxNumbers.contains(Integer.valueOf(boxNum))) {
                       continue;
                    }
                    WhFbaAllocation fbaAllocation = new WhFbaAllocation();
                    fbaAllocation.setFbaNo(whFbaShipment.getFbaNo());
                    fbaAllocation.setShipmentId(whFbaShipment.getShipmentId());
                    fbaAllocation.setShippingOrderNo(whFbaShipment.getShippingOrderNo());
                    fbaAllocation.setTrackingNumber(whFbaShipment.getTrackingNumberByTms());
                    fbaAllocation.setBoxTotal(whFbaShipment.getBoxTotal());
                    fbaAllocation.setBatNo(shipmentId);
                    fbaAllocation.setPushTime(whFbaShipment.getConfirmTime());
                    fbaAllocation.setShippingCompany(whFbaShipment.getShippingCompany());
                    fbaAllocation.setShippingMethod(whFbaShipment.getShippingMethodByTms());
                    result.add(fbaAllocation);
                }
            }
        }
        
        log.info("匹配到 {} 条待装车的FBA发货单信息", result.size());
        return result;
    }

    /**
     * 处理新的FBA装车流程
     * @param requests FBA装车请求列表
     * @return 处理结果
     */
    public ResponseJson doLoadFbaRequests(List<FbaDeliverRequest> requests) {
        log.info("处理FBA装车请求，请求数量: {}", requests.size());
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        
        if (CollectionUtils.isEmpty(requests)) {
            response.setMessage("请求参数为空");
            return response;
        }
        
        List<String> errorMessages = new ArrayList<>();
        List<String> errorBatNos = new ArrayList<>();
        
        for (FbaDeliverRequest request : requests) {
            // 验证必要参数
            if (StringUtils.isBlank(request.getShipmentId())) {
                errorMessages.add("货件单号不能为空");
                errorBatNos.add(request.getBatNo());
                continue;
            }
            
            // 验证箱数是否匹配
            if (request.getScannedBoxCount() == null || request.getTotalBoxCount() == null) {
                errorMessages.add(String.format("货件单号 %s 的已扫描箱数或总箱数为空", request.getShipmentId()));
                errorBatNos.add(request.getBatNo());
                continue;
            }
            
            if (!request.getScannedBoxCount().equals(request.getTotalBoxCount())) {
                errorMessages.add(String.format("货件单号 %s 的已扫描箱数 %d 与总箱数 %d 不匹配", 
                    request.getShipmentId(), request.getScannedBoxCount(), request.getTotalBoxCount()));
                errorBatNos.add(request.getBatNo());
                continue;
            }
            
            // 查找对应的FBA发货单
            List<WhFbaAllocation> allocations = matchLoadFbaAllocationByShipmentIdAndFbaNo(request.getShipmentId(), request.getFbaNo(), null);
            
            if (CollectionUtils.isEmpty(allocations)) {
                errorMessages.add(String.format("未找到货件单号 %s 对应的待装车FBA发货单", request.getShipmentId()));
                errorBatNos.add(request.getBatNo());
                continue;
            }
            
            String errorMsg = null;
            WhFbaAllocation allocation = allocations.get(0);
            
            // 执行装车操作
            try {
                ResponseJson loadResponse = whFbaAllocationService.doLoad(allocation.getShipmentId());
                if (!loadResponse.isSuccess()) {
                    errorMessages.add(String.format("货件单号 %s 装车失败: %s", request.getShipmentId(), loadResponse.getMessage()));
                    errorBatNos.add(request.getBatNo());
                }
            } catch (Exception e) {
                log.error("FBA装车异常", e);
                errorMsg = String.format("货件单号 %s 装车异常: %s", request.getShipmentId(), e.getMessage());
                errorMessages.add(errorMsg);
                errorBatNos.add(request.getBatNo());
            }
        }
        
        if (errorMessages.isEmpty()) {
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("所有FBA发货单装车成功");
        } else {
            String errorMsg = String.join("; ", errorMessages);
            log.error("FBA装车失败: {}", errorMsg);
            response.setMessage(errorMsg);
            if (CollectionUtils.isNotEmpty(errorBatNos)){
                Map<String, Object> errorBatNosMap = new HashMap<>();
                errorBatNosMap.put("errorBatNos", errorBatNos);
                response.setBody(errorBatNosMap);
            }
        }
        
        return response;
    }
} 