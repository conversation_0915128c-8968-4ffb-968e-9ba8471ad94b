package com.estone.warehouse.service;

import com.estone.warehouse.bean.*;
import com.estone.warehouse.enums.PrestorageStockTransferSourceEnum;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-03-09 17:34
 */
public interface PrestorageStockTransferService {

    /**
     * 用于根据条件查找存货迁移任务
     *
     * @param queryCondition 查找条件
     * @param pager          分页条件
     * @return 存货迁移任务对象
     */
    List<PrestorageStockTransfer> queryOrders(PrestorageStockTransferQueryCondition queryCondition, Pager pager);

    /**
     * 用于根据条件查询出存货迁移任务数据模型
     *
     * @param queryCondition 查询条件
     * @param pager          分页情况
     * @return 存货迁移任务对象
     */
    List<PrestorageStockTransferOrder> queryTransferOrders(PrestorageStockTransferQueryCondition queryCondition, Pager pager);


    /**
     * 用于根据订单Id，查询出订单明细信息
     *
     * @param orderId 订单Id
     * @return 订单明细信息
     */
    List<PrestorageStockTransferDetail> queryOrderDetailsByOrderId(Integer orderId);


    /**
     * 用于根据订单Id，查询出订单明细信息
     *
     * @param orderId 订单Id
     * @return 订单明细信息
     */
    List<PrestorageStockTransferOrderItem> queryOrderItemsByOrderId(Integer orderId);

    /**
     * 用于查找处符合查询条件的存货迁移任务明细
     *
     * @param queryCondition 查询条件
     * @param pager          分页条件
     * @return 符合查询条件的存货迁移任务明细
     */
    List<PrestorageStockTransferDetail> queryOrderDetails(PrestorageStockTransferQueryCondition queryCondition, Pager pager);


    /**
     * 根据订单Id，废弃掉订单
     *
     * @param orderIds 订单Id
     * @return 废弃失败的订单集合对象，如果都废弃成功时，返回null
     */
    List<PrestorageStockTransferOrder> batchAbortOrder(List<Integer> orderIds);

    /**
     * 根据订单Id,打印任务号
     *
     * @param orderIds 订单Id
     * @return 要进行打印的任务对象
     */
    void printOrder(List<Integer> orderIds);

    List<List<String>> getExportList(List<PrestorageStockTransferDetail> records, String[] selectHeaders);

    /**
     * 用于执行文件解析、内容校验、订单创建功能
     *
     * @param fileMap 文件map对象
     * @return 处理结果
     */
    ResponseJson handleFiles(Map<String, MultipartFile> fileMap);


    /**
     * 用于添加新迁移任务订单（每个任务最多50个明细）
     *
     * @param migrationSkus 迁移任务明细
     * @param source        任务来源
     * @return 创建成功的订单Id列表
     */
    List<Integer> addOrder(List<PrestorageStockTransferSkuBO> migrationSkus, PrestorageStockTransferSourceEnum source);


    /**
     * 用于添加由订单分配库存导致的迁移任务订单
     *
     * @param migrationSkus 迁移任务明细
     * @return 响应内容
     */
    ResponseJson addAllotOrder(List<PrestorageStockTransferSkuBO> migrationSkus);

    /**
     * 用于更新订单信息
     *
     * @param order 要进行更新订单信息的内容
     */
    void updateOrder(PrestorageStockTransferOrder order);

    /**
     * 用于批量完成订单
     *
     * @param orders 要进行批量更新的订单信息
     */
    void batchUpdateOrder(List<PrestorageStockTransferOrder> orders);

    /**
     * 用于更新订单明细信息
     *
     * @param item 要进行更新的订单明细信息
     */
    void updateOrderItem(PrestorageStockTransferOrderItem item);

    /**
     * 用于拣货操作
     *
     * @param orderNo    拣货的订单号
     * @param itemId     拣货的明细ID
     * @param pickNumber 拣货捡取的数量
     * @return 操作结果
     */
    ResponseJson pickSku(String orderNo, Integer itemId, Integer pickNumber);


    /**
     * 用于上架操作
     *
     * @param orderNo              上架的订单号
     * @param itemId               上架的明细ID
     * @param uploadLocationNumber 上架库位
     * @param uploadNumber         上架的数量
     * @return 操作结果
     */
    ResponseJson uploadSku(String orderNo, Integer itemId, String uploadLocationNumber, Integer uploadNumber);

    /**
     * 用于校验sku是否允许上架到对应库位的操作
     *
     * @param orderNo              存货迁移任务号
     * @param sku                  sku或者唯一码
     * @param uploadLocationNumber 上架的库位
     * @return 校验的情况
     */
    ResponseJson checkUploadLocationNumber(String orderNo, String sku, String uploadLocationNumber);
}
