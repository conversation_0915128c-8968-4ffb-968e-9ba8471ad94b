package com.estone.warehouse.service;

import com.estone.warehouse.bean.WhOrderCancel;
import com.estone.warehouse.bean.WhOverseasReturnReport;
import com.estone.warehouse.bean.WhOverseasReturnReportQueryCondition;
import com.estone.warehouse.bean.WhPlatformOrderCount;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhOverseasReturnReportService {
    List<WhOverseasReturnReport> queryAllWhOverseasReturnReports();

    List<WhOverseasReturnReport> queryWhOverseasReturnReports(WhOverseasReturnReportQueryCondition query, Pager pager);

    WhOverseasReturnReport getWhOverseasReturnReport(Integer id);

    WhOverseasReturnReport getWhOverseasReturnReportDetail(Integer id);

    WhOverseasReturnReport queryWhOverseasReturnReport(WhOverseasReturnReportQueryCondition query);

    void createWhOverseasReturnReport(WhOverseasReturnReport whOverseasReturnReport);

    void batchCreateWhOverseasReturnReport(List<WhOverseasReturnReport> entityList);

    void deleteWhOverseasReturnReport(Integer id);

    void updateWhOverseasReturnReport(WhOverseasReturnReport whOverseasReturnReport);

    void batchUpdateWhOverseasReturnReport(List<WhOverseasReturnReport> entityList);

    List<WhOverseasReturnReport> queryOverseasReturnReportList(WhOverseasReturnReportQueryCondition query);

    List<WhPlatformOrderCount> queryPlatformOrderCountList(WhOverseasReturnReportQueryCondition query);

    void statisticsOverseasReturn(String startOfDay, String endOfDay);
}