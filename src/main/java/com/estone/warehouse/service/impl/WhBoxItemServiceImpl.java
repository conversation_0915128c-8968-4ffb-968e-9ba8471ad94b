package com.estone.warehouse.service.impl;

import com.estone.checkin.bean.WhPurchaseExpressRecord;
import com.estone.checkin.bean.WhPurchaseExpressRecordQueryCondition;
import com.estone.checkin.enums.CheckInLogType;
import com.estone.checkin.enums.PurchaseExpressRecordStatus;
import com.estone.checkin.service.WhPurchaseExpressRecordService;
import com.estone.common.enums.LogModule;
import com.estone.common.util.SystemLogUtils;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.bean.WhBoxItem;
import com.estone.warehouse.bean.WhBoxItemQueryCondition;
import com.estone.warehouse.bean.WhBoxQueryCondition;
import com.estone.warehouse.dao.WhBoxDao;
import com.estone.warehouse.dao.WhBoxItemDao;
import com.estone.warehouse.enums.ItemOrderTypeEnum;
import com.estone.warehouse.service.WhBoxItemService;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.whq.tool.sqler.SqlerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("whBoxItemService")
public class WhBoxItemServiceImpl implements WhBoxItemService {
    private static final Logger logger = LoggerFactory.getLogger(WhBoxItemServiceImpl.class);
    final static SystemLogUtils EXPRESSRECORDLOG = SystemLogUtils.create(LogModule.EXPRESSRECORD.getCode());

    @Resource
    private WhBoxItemDao whBoxItemDao;

    @Resource
    private WhBoxDao whBoxDao;

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private WhPurchaseExpressRecordService whPurchaseExpressRecordService;

    /**
     * This method corresponds to the database table wh_box_item
     *
     * @mbggenerated Mon Apr 08 11:07:54 CST 2019
     */
    public WhBoxItem getWhBoxItem(String boxNo, String relationNo) {
        WhBoxItem whBoxItem = whBoxItemDao.queryWhBoxItem(boxNo, relationNo);
        return whBoxItem;
    }

    /**
     * This method corresponds to the database table wh_box_item
     *
     * @mbggenerated Mon Apr 08 11:07:54 CST 2019
     */
    public WhBoxItem getWhBoxItemDetail(String boxNo, String relationNo) {
        WhBoxItem whBoxItem = whBoxItemDao.queryWhBoxItem(boxNo, relationNo);
        // 关联查询
        return whBoxItem;
    }

    /**
     * This method corresponds to the database table wh_box_item
     *
     * @mbggenerated Mon Apr 08 11:07:54 CST 2019
     */
    public WhBoxItem queryWhBoxItem(WhBoxItemQueryCondition query) {
        Assert.notNull(query);
        WhBoxItem whBoxItem = whBoxItemDao.queryWhBoxItem(query);
        return whBoxItem;
    }

    /**
     * This method corresponds to the database table wh_box_item
     *
     * @mbggenerated Mon Apr 08 11:07:54 CST 2019
     */
    public List<WhBoxItem> queryAllWhBoxItems() {
        return whBoxItemDao.queryWhBoxItemList();
    }

    /**
     * This method corresponds to the database table wh_box_item
     *
     * @mbggenerated Mon Apr 08 11:07:54 CST 2019
     */
    public List<WhBoxItem> queryWhBoxItems(WhBoxItemQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whBoxItemDao.queryWhBoxItemCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhBoxItem>();
            }
        }
        List<WhBoxItem> whBoxItems = whBoxItemDao.queryWhBoxItemList(query, pager);
        return whBoxItems;
    }

    /**
     * This method corresponds to the database table wh_box_item
     *
     * @mbggenerated Mon Apr 08 11:07:54 CST 2019
     */
    public void createWhBoxItem(WhBoxItem whBoxItem) {
        try {
            whBoxItemDao.createWhBoxItem(whBoxItem);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_box_item
     *
     * @mbggenerated Mon Apr 08 11:07:54 CST 2019
     */
    public void batchCreateWhBoxItem(List<WhBoxItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whBoxItemDao.batchCreateWhBoxItem(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_box_item
     *
     * @mbggenerated Mon Apr 08 11:07:54 CST 2019
     */
    public void deleteWhBoxItem(String boxNo, String relationNo, Integer orderType, String trackingNum, Boolean isAuto) {
        try {
            whBoxItemDao.deleteWhBoxItem(boxNo, relationNo, orderType);
            if (Objects.equals(ItemOrderTypeEnum.WH_CHECK_IN_EXCEPTION.intCode(), orderType)){
                return;
            }
            // 收货单修改领取人和领取时间
            WhPurchaseExpressRecord expressRecord = whPurchaseExpressRecordService.getWhPurchaseExpressRecord(Integer.parseInt(relationNo));
            expressRecord.setBoxNo(null);
            if (!isAuto) {
                expressRecord.setReceiveUser(null);
                expressRecord.setReceiveDate(null);
            }
            if (expressRecord.getStatus() != null
                    && Objects.equals(expressRecord.getStatus(), PurchaseExpressRecordStatus.SCANNER.intCode())){
                expressRecord.setStatus(PurchaseExpressRecordStatus.FINISH.intCode());
            }
            whPurchaseExpressRecordService.updateWhPurchaseExpressRecord(expressRecord);
            String tip = "快递单号";
            if (Objects.equals(ItemOrderTypeEnum.WL_RECEIPT.intCode(), orderType)){
                tip = "物流单号";
            }
            EXPRESSRECORDLOG.log(Integer.parseInt(relationNo), CheckInLogType.UNBIND_RECEIVE_BOX_NO.getName(),
                    new String[][] { { tip, trackingNum }, { "周转框", boxNo }, { "手/自动", isAuto ? "自动" : "手动" } });
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_box_item
     *
     * @mbggenerated Mon Apr 08 11:07:54 CST 2019
     */
    public void updateWhBoxItem(WhBoxItem whBoxItem) {
        try {
            whBoxItemDao.updateWhBoxItem(whBoxItem);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_box_item
     *
     * @mbggenerated Mon Apr 08 11:07:54 CST 2019
     */
    public void batchUpdateWhBoxItem(List<WhBoxItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whBoxItemDao.batchUpdateWhBoxItem(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public ResponseJson updateStatusAndCreateLog(String boxNo, String oldBoxNo) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        try {
            if (StringUtils.isBlank(boxNo) || StringUtils.isBlank(oldBoxNo)) {
                responseJson.setMessage("周转框参数为空！");
                return responseJson;
            }
            WhBoxQueryCondition query = new WhBoxQueryCondition();
            query.setBoxNo(oldBoxNo);
            WhBox whBox = whBoxDao.queryWhBox(query);
            int itemCount = 0;
            String trackingNums = null;
            Boolean isUsed = true;
            if (whBox != null) {
                WhBoxItemQueryCondition queryCondition = new WhBoxItemQueryCondition();
                queryCondition.setBoxNo(oldBoxNo);
                List<WhBoxItem> whBoxItems = whBoxItemDao.queryWhBoxItemList(queryCondition, null);
                itemCount = whBoxItems.size();
                trackingNums = whBoxItems.stream().map(WhBoxItem::getTrackingNum).collect(Collectors.joining(","));
            }
            if (!boxNo.equals(oldBoxNo) && itemCount == 0) {
                isUsed = false;
            }
            if (!boxNo.equals(oldBoxNo) && itemCount > 0) {
                SystemLogUtils.BOXLOG.log(whBox.getId(), CheckInLogType.BIND_RECEIVE_BOX_NO.getName(),
                        new String[][] { { "绑定快递数量", itemCount + "" },
                                { "绑定快递单号", trackingNums } });

            }
            whBoxService.updateStatus(oldBoxNo, isUsed);
            responseJson.setStatus(StatusCode.SUCCESS);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
        return responseJson;
    }

    @Override
    public int queryUnSplitCountByBoxNo(String boxNo) {
        return whBoxItemDao.queryUnSplitCountByBoxNo(boxNo);
    }

    @Override
    public List<WhBoxItem> queryWhBoxItemList(WhBoxItemQueryCondition query) {
        return whBoxItemDao.queryWhBoxItemList(query, null);
    }
}