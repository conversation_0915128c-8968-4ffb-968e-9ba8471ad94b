package com.estone.warehouse.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.estone.common.CacheName;
import com.estone.common.util.*;
import com.estone.common.util.model.CQuery;
import com.estone.common.util.model.CQueryResult;
import com.estone.pac.service.PacStockService;
import com.estone.sku.bean.ExpManage;
import com.estone.sku.bean.ExpManageItem;
import com.estone.sku.bean.ExpManageQueryCondition;
import com.estone.sku.service.ExpManageService;
import com.estone.sku.service.WhSkuService;
import com.estone.statistics.enums.DrpTurnoverOderType;
import com.estone.system.user.bean.SaleUser;
import com.estone.transfer.bean.TransferStock;
import com.estone.transfer.bean.TransferStockQueryCondition;
import com.estone.transfer.service.TransferStockService;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.*;
import com.estone.warehouse.dao.WhInventoryTaskDao;
import com.estone.warehouse.dao.WhInventoryTaskItemDao;
import com.estone.warehouse.dao.WhPickInventoryDemandDao;
import com.estone.warehouse.enums.InventoryAbnormalStatus;
import com.estone.warehouse.service.*;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service("whInventoryTaskService")
public class WhInventoryTaskServiceImpl implements WhInventoryTaskService {

    private static Logger logger = LoggerFactory.getLogger(WhInventoryTaskServiceImpl.class);

    @Resource
    private WhInventoryTaskDao whInventoryTaskDao;

    @Resource
    private WhInventoryTaskItemDao whInventoryTaskItemDao;

    @Resource
    private WhPickInventoryDemandDao whPickInventoryDemandDao;

    @Resource
    private WhPickInventoryDemandService whPickInventoryDemandService;

    @Resource
    private WhStockService whStockService;
    @Resource
    private InventoryUpdateStockService inventoryUpdateStockService;

    @Resource
    private TransferStockService transferStockService;

    @Resource
    private PacStockService pacStockService;

    @Resource
    private ExpManageService expManageService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private InventoryAbnormalDataService inventoryAbnormalDataService;

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    public WhInventoryTask getWhInventoryTask(Integer id) {
        WhInventoryTask whInventoryTask = whInventoryTaskDao.queryWhInventoryTask(id);
        return whInventoryTask;
    }

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    public WhInventoryTask getWhInventoryTaskDetail(Integer id) {
        WhInventoryTask whInventoryTask = whInventoryTaskDao.queryWhInventoryTask(id);
        // 关联查询
        return whInventoryTask;
    }

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    public WhInventoryTask queryWhInventoryTask(WhInventoryTaskQueryCondition query) {
        Assert.notNull(query);
        WhInventoryTask whInventoryTask = whInventoryTaskDao.queryWhInventoryTask(query);
        return whInventoryTask;
    }

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    public List<WhInventoryTask> queryAllWhInventoryTasks() {
        return whInventoryTaskDao.queryWhInventoryTaskList();
    }

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    public List<WhInventoryTask> queryWhInventoryTasks(WhInventoryTaskQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whInventoryTaskDao.queryWhInventoryTaskCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhInventoryTask>();
            }
        }
        List<WhInventoryTask> whInventoryTasks = whInventoryTaskDao.queryWhInventoryTaskList(query, pager);
        return whInventoryTasks;
    }

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    public void createWhInventoryTask(WhInventoryTask whInventoryTask) {
        try {
            whInventoryTaskDao.createWhInventoryTask(whInventoryTask);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    public void batchCreateWhInventoryTask(List<WhInventoryTask> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whInventoryTaskDao.batchCreateWhInventoryTask(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    public void deleteWhInventoryTask(Integer id) {
        try {
            whInventoryTaskDao.deleteWhInventoryTask(id);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    public int updateWhInventoryTask(WhInventoryTask whInventoryTask) {
        try {
            return whInventoryTaskDao.updateWhInventoryTask(whInventoryTask);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    public void batchUpdateWhInventoryTask(List<WhInventoryTask> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whInventoryTaskDao.batchUpdateWhInventoryTask(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public List<WhInventoryTask> queryWhInventoryTaskAndItemList(WhInventoryTaskQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whInventoryTaskDao.queryWhInventoryTaskAndItemCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhInventoryTask>();
            }
        }
        List<WhInventoryTask> whInventoryTasks = whInventoryTaskDao.queryWhInventoryTaskAndItemList(query, pager);
        return whInventoryTasks;
    }

    /**
     * 任务置顶
     */
    @Override
    public ResponseJson top(Integer id) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhInventoryTask task = whInventoryTaskDao.queryWhInventoryTask(id);
        if (task != null) {
            Integer maxLevel = whInventoryTaskDao.queryWhInventoryTaskGradeMax();
            if (maxLevel == null) {
                maxLevel = 0;
            }
            WhInventoryTask update = new WhInventoryTask();
            update.setGrade(maxLevel + 1);
            update.setId(id);
            if (whInventoryTaskDao.updateWhInventoryTask(update) >= 1) {
                SystemLogUtils.INVENTORYTASK.log(id, "盘点任务置顶-修改等级=" + (maxLevel + 1),
                        new String[][]{{"原等级", maxLevel + ""}});
                response.setStatus(StatusCode.SUCCESS);
            } else {
                response.setMessage("更新失败");
                SystemLogUtils.INVENTORYTASK.log(id, "盘点任务置顶失败", new String[][]{{"原因", "更新失败"}});
            }
        } else {
            response.setMessage("沒找到相关任务");
        }
        return response;
    }

    /**
     * 废弃盘点任务
     */
    @Override
    public boolean discard(Integer id, String type) {
        boolean result = false;
        if ("reset".equals(type)) {
            // 将盘点需求改为待生成
            result = updateToReset(id);
        } else if ("discard".equals(type)) {
            // 废弃需求
            result = updateToDiscard(id);
        }
        return result;
    }

    /**
     * 废弃盘点任务、需求待生成
     */
    public boolean updateToReset(Integer id) {
        boolean result = false;
        WhInventoryTaskQueryCondition query = new WhInventoryTaskQueryCondition();
        query.setId(id);
        List<WhInventoryTask> tasks = queryWhInventoryTaskAndItemList(query, null);
        if (CollectionUtils.isNotEmpty(tasks) && CollectionUtils.isNotEmpty(tasks.get(0).getWhInventoryTaskItems())) {
            WhInventoryTask task = tasks.get(0);
            // 未领取,盘点中,待审核 可以重置
            List<Integer> status = new ArrayList<>();
            status.add(InventoryTaskStatus.UNRECEIVED.intCode());
            status.add(InventoryTaskStatus.INVENTORY.intCode());
            status.add(InventoryTaskStatus.UNREVIEW.intCode());
            if (status.contains(task.getStatus())) {
                WhInventoryTask updateTask = new WhInventoryTask();
                updateTask.setId(task.getId());
                updateTask.setStatus(InventoryTaskStatus.DISCARDED.intCode());
                updateTask.setLastStatus(task.getStatus());
                int updateLines = whInventoryTaskDao.updateWhInventoryTask(updateTask);
                if (updateLines == 0) {
                    logger.info("updateWhInventoryTask to DISCARDED failed id-->" + id);
                    return false;
                }
                List<WhInventoryTaskItem> items = task.getWhInventoryTaskItems();

                List<WhInventoryTaskItem> updateItems = new ArrayList<>();
                List<WhPickInventoryDemand> updateDemands = new ArrayList<>();

                for (WhInventoryTaskItem item : items) {
                    // 未领取、盘点中和盘点完成但未确认的可以废弃
                    if (item.getStatus().equals(InventoryTaskStatus.UNRECEIVED.intCode())
                            || item.getStatus().equals(InventoryTaskStatus.INVENTORY.intCode())
                            || (item.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode())
                            && item.getConfirmQuantity() == null)) {
                        WhInventoryTaskItem updateItem = new WhInventoryTaskItem();
                        updateItem.setId(item.getId());
                        updateItem.setDemandId(item.getDemandId());
                        updateItem.setStatus(InventoryTaskStatus.DISCARDED.intCode());
                        updateItems.add(updateItem);

                        WhPickInventoryDemand updateDemand = new WhPickInventoryDemand();
                        updateDemand.setId(item.getDemandId());
                        updateDemand.setStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
                        updateDemands.add(updateDemand);
                    }
                }
                SystemLogUtils.INVENTORYTASK.log(id, "重置盘点需求", new String[][]{{"原状态", task.getStatusName()},
                        {"变更状态", InventoryTaskStatus.DISCARDED.getName()}});

                // 没有需要修改的明细时直接完成
                if (CollectionUtils.isEmpty(updateItems)) {
                    return true;
                }

                whInventoryTaskItemDao.batchUpdateWhInventoryTaskItem(updateItems);
                whPickInventoryDemandDao.batchUpdateWhPickInventoryDemand(updateDemands);

                for (WhInventoryTaskItem item : updateItems) {
                    SystemLogUtils.INVENTORYTASKITEM.log(item.getId(), "废弃盘点任务明细-重置盘点需求", new String[][]{
                            {"原状态", task.getStatusName()}, {"变更状态", InventoryTaskStatus.DISCARDED.getName()}});
                    SystemLogUtils.PICKINVENTORYDEMAND.log(item.getDemandId(), "重置盘点需求",
                            new String[][]{{"历史状态", task.getStatusName()},
                                    {"变更状态", PickInventoryDemandStatus.UNGENERATE.getName()}});
                }
                result = true;
            }
        } else {
            logger.warn("WhInventoryTask or WhInventoryTaskItem not exist!  id-->" + id);
        }
        return result;
    }

    /**
     * 废弃盘点任务、需求废弃
     */
    public boolean updateToDiscard(Integer id) {
        boolean result = false;
        WhInventoryTaskQueryCondition query = new WhInventoryTaskQueryCondition();
        query.setId(id);
        List<WhInventoryTask> tasks = queryWhInventoryTaskAndItemList(query, null);
        if (CollectionUtils.isNotEmpty(tasks) && CollectionUtils.isNotEmpty(tasks.get(0).getWhInventoryTaskItems())) {
            WhInventoryTask task = tasks.get(0);
            // 未领取,盘点中,待审核 可以废弃
            List<Integer> status = new ArrayList<>();
            status.add(InventoryTaskStatus.UNRECEIVED.intCode());
            status.add(InventoryTaskStatus.INVENTORY.intCode());
            status.add(InventoryTaskStatus.UNREVIEW.intCode());
            if (status.contains(task.getStatus())) {
                WhInventoryTask updateTask = new WhInventoryTask();
                updateTask.setId(task.getId());
                updateTask.setStatus(InventoryTaskStatus.DISCARDED.intCode());
                updateTask.setLastStatus(task.getStatus());
                int updateLines = whInventoryTaskDao.updateWhInventoryTask(updateTask);
                if (updateLines == 0) {
                    logger.info("updateWhInventoryTask  to DISCARDED failed id-->" + id);
                    return false;
                }
                List<WhInventoryTaskItem> items = task.getWhInventoryTaskItems();

                List<WhInventoryTaskItem> updateItems = new ArrayList<>();
                List<WhPickInventoryDemand> updateDemands = new ArrayList<>();

                for (WhInventoryTaskItem item : items) {
                    // 未领取、盘点中和盘点完成但未确认的可以废弃
                    if (item.getStatus().equals(InventoryTaskStatus.UNRECEIVED.intCode())
                            || item.getStatus().equals(InventoryTaskStatus.INVENTORY.intCode())
                            || (item.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode())
                            && item.getConfirmQuantity() == null)) {
                        WhInventoryTaskItem updateItem = new WhInventoryTaskItem();
                        updateItem.setId(item.getId());
                        updateItem.setDemandId(item.getDemandId());
                        updateItem.setStatus(InventoryTaskStatus.DISCARDED.intCode());
                        updateItems.add(updateItem);

                        WhPickInventoryDemand updateDemand = new WhPickInventoryDemand();
                        updateDemand.setId(item.getDemandId());
                        updateDemand.setStatus(PickInventoryDemandStatus.DISCARDED.intCode());
                        updateDemands.add(updateDemand);
                    }
                }

                SystemLogUtils.INVENTORYTASK.log(id, "废弃盘点任务", new String[][]{{"原状态", task.getStatusName()},
                        {"变更状态", InventoryTaskStatus.DISCARDED.getName()}});

                // 没有需要修改的明细时直接完成
                if (CollectionUtils.isEmpty(updateItems)) {
                    return true;
                }

                whInventoryTaskItemDao.batchUpdateWhInventoryTaskItem(updateItems);
                whPickInventoryDemandDao.batchUpdateWhPickInventoryDemand(updateDemands);

                for (WhInventoryTaskItem item : updateItems) {
                    SystemLogUtils.INVENTORYTASKITEM.log(item.getId(), "废弃盘点任务明细-废弃盘点需求", new String[][]{
                            {"原状态", task.getStatusName()}, {"变更状态", InventoryTaskStatus.DISCARDED.getName()}});
                    SystemLogUtils.PICKINVENTORYDEMAND.log(item.getDemandId(), "废弃盘点需求", new String[][]{
                            {"历史状态", task.getStatusName()}, {"变更状态", updateTask.getStatusName()}});
                }
                result = true;
            }
        } else {
            logger.warn("WhInventoryTask or WhInventoryTaskItem not exist!  id-->" + id);
        }
        return result;
    }

    /**
     * 领取盘点任务
     */
    @Override
    public ResponseJson receiveInventoryTaskByType(Integer receiveUser, InventoryTaskType type) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhInventoryTaskQueryCondition query = new WhInventoryTaskQueryCondition();
        query.setStatus(InventoryTaskStatus.INVENTORY.intCode());
        query.setReceiveUser(receiveUser);
        if(Objects.nonNull(type)) {
            query.setTaskType(type.intCode());
        }
        List<WhInventoryTask> tasks = queryWhInventoryTaskAndItemList(query, null);
        if (CollectionUtils.isNotEmpty(tasks)) {
            if (CollectionUtils.isNotEmpty(tasks.get(0).getWhInventoryTaskItems())) {
                // 返回前库位排序
                SkuLocationCompareUtils.compare(tasks.get(0).getWhInventoryTaskItems());
                response.setMessage(JSON.toJSONString(tasks.get(0)));
                response.setStatus(StatusCode.SUCCESS);
                return response;
            } else {
                response.setMessage("当前未完成的盘点任务明细为空!");
            }
        } else {
            query = new WhInventoryTaskQueryCondition();
            query.setStatus(InventoryTaskStatus.UNRECEIVED.intCode());
            query.setNotCurrCreationUser(receiveUser);
            // 只能领取领取人为null的任务
            query.setNotExistReceiveUser(true);
            if(Objects.nonNull(type)) {
                query.setTaskType(type.intCode());
            }
            tasks = queryWhInventoryTaskAndItemList(query, new Pager(0, 1));
            // 盘点任务的话，得过滤当前存在着异常条目信息相关的任务，如果存在未完成的异常信息的话，就不让其进行领取
            // 目的是预防任务是生成的下一阶段的任务，同时又是绑定了周转框但是没有去上架操作的
//            if (Objects.equals(InventoryTaskType.ZONE, type) && CollectionUtils.isNotEmpty(tasks)){
//                List<Integer> ids = tasks.stream().map(WhInventoryTask::getId).collect(Collectors.toList());
//                InventoryAbnormalDataQueryCondition condition = new InventoryAbnormalDataQueryCondition();
//                condition.setNextStepTaskIds(ids);
//                condition.setStatus(InventoryAbnormalStatus.WAITING.intCode());
//                List<InventoryAbnormalData> inventoryAbnormalData = inventoryAbnormalDataService.queryInventoryAbnormalDatas(condition, null);
//                if (CollectionUtils.isNotEmpty(inventoryAbnormalData)){
//                    Set<Integer> waitDualIds = inventoryAbnormalData.stream().map(InventoryAbnormalData::getNextStepTaskId).collect(Collectors.toSet());
//                    tasks = tasks.stream().filter(task -> !waitDualIds.contains(task.getId())).collect(Collectors.toList());
//                    if (CollectionUtils.isEmpty(tasks)){
//                        response.setMessage("当前用户没有任务可领，库区盘点任务存在未完成的异常处理信息，请先进行处理操作!");
//                        return response;
//                    }
//                }
//            }
            if (CollectionUtils.isNotEmpty(tasks)
                    && CollectionUtils.isNotEmpty(tasks.get(0).getWhInventoryTaskItems())) {
                try {
                    response = updateToReceiveInventoryTask(tasks.get(0), receiveUser);
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                    response.setMessage(e.getMessage());
                }
            } else {
                response.setMessage("当前用户没有任务可领");
            }
        }
        return response;
    }

    /**
     * 领取盘点任务-->更新状态
     */
    public ResponseJson updateToReceiveInventoryTask(WhInventoryTask task, Integer receiveUser) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        WhInventoryTask updateTask = new WhInventoryTask();
        updateTask.setId(task.getId());
        updateTask.setReceiveUser(receiveUser);
        updateTask.setReceiveDate(new Timestamp(System.currentTimeMillis()));
        updateTask.setStatus(InventoryTaskStatus.INVENTORY.intCode());
        updateTask.setLastStatus(InventoryTaskStatus.UNRECEIVED.intCode());
        int returnInt = whInventoryTaskDao.updateWhInventoryTask(updateTask);

        if (returnInt > 0) {

            List<WhInventoryTaskItem> items = task.getWhInventoryTaskItems();

            List<WhInventoryTaskItem> updateItems = new ArrayList<>();
            List<WhPickInventoryDemand> updateDemands = new ArrayList<>();

            for (WhInventoryTaskItem item : items) {
                // 排除未领取前就废弃的条目
                if (item.getStatus().equals(InventoryTaskStatus.DISCARDED.intCode())) {
                    continue;
                }
                item.setStatus(InventoryTaskStatus.INVENTORY.intCode());

                WhInventoryTaskItem updateItem = new WhInventoryTaskItem();
                updateItem.setId(item.getId());
                updateItem.setDemandId(item.getDemandId());
                updateItem.setStatus(InventoryTaskStatus.INVENTORY.intCode());
                updateItems.add(updateItem);

                if (Objects.nonNull(item.getDemandId())) {
                    WhPickInventoryDemand updateDemand = new WhPickInventoryDemand();
                    updateDemand.setId(item.getDemandId());
                    updateDemand.setStatus(PickInventoryDemandStatus.INVENTORY.intCode());
                    updateDemands.add(updateDemand);
                }
            }

            whInventoryTaskItemDao.batchUpdateWhInventoryTaskItem(updateItems);
            whPickInventoryDemandDao.batchUpdateWhPickInventoryDemand(updateDemands);

            SystemLogUtils.INVENTORYTASK.log(task.getId(), "领取盘点任务", new String[][]{{"原状态", task.getStatusName()},
                    {"变更状态", InventoryTaskStatus.INVENTORY.getName()}});
            for (WhInventoryTaskItem item : updateItems) {
                SystemLogUtils.INVENTORYTASKITEM.log(item.getId(), "领取盘点任务", new String[][]{
                        {"原状态", task.getStatusName()}, {"变更状态", InventoryTaskStatus.INVENTORY.getName()}});
                if (Objects.nonNull(item.getDemandId())) {
                    SystemLogUtils.PICKINVENTORYDEMAND.log(item.getDemandId(), "领取盘点任务", new String[][]{
                            {"历史状态", InventoryTaskStatus.UNRECEIVED.getName()}, {"变更状态", updateTask.getStatusName()}});
                }
            }
            // 返回前库位排序
            SkuLocationCompareUtils.compare(task.getWhInventoryTaskItems());
            response.setMessage(JSON.toJSONString(task));
            response.setStatus(StatusCode.SUCCESS);
            return response;
        } else {
            response.setMessage("领取任务失败，请重新领取！");
        }
        return response;
    }

    @Override
    public boolean allocation(Integer id, Integer allocationUser) throws Exception {
        boolean result = false;
        WhInventoryTaskQueryCondition query = new WhInventoryTaskQueryCondition();
        query.setId(id);
        //query.setStatus(InventoryTaskStatus.UNRECEIVED.intCode());
        //List<WhInventoryTask> tasks = queryWhInventoryTaskAndItemList(query, null);
        //query = new WhInventoryTaskQueryCondition();
        query.setStatus(InventoryTaskStatus.UNRECEIVED.intCode());
        query.setNotCurrCreationUser(allocationUser);
        List<WhInventoryTask> tasks = queryWhInventoryTaskAndItemList(query, new Pager(0, 1));
        // 当前任务非 未领取状态
        if (CollectionUtils.isEmpty(tasks)) {
            return result;
        }
        // 当前任务 认领人=当前分配人员
        WhInventoryTask task = tasks.get(0);
        if (Objects.equals(allocationUser,task.getReceiveUser())) {
            return result;
        }

        this.updateToAllocationInventoryTask(task, allocationUser);
        return true;
    }

    @Override
    public ResponseJson receiveInventoryTask(Integer taskId, Integer receiveUser) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhInventoryTaskQueryCondition query = new WhInventoryTaskQueryCondition();

        query.setStatus(InventoryTaskStatus.INVENTORY.intCode());
        query.setReceiveUser(receiveUser);
        List<WhInventoryTask> tasks = queryWhInventoryTaskAndItemList(query, null);
        if (CollectionUtils.isNotEmpty(tasks)) {
            if (CollectionUtils.isNotEmpty(tasks.get(0).getWhInventoryTaskItems())) {
                // 返回前库位排序
                SkuLocationCompareUtils.compare(tasks.get(0).getWhInventoryTaskItems());
                response.setMessage(JSON.toJSONString(tasks.get(0)));
                response.setStatus(StatusCode.SUCCESS);
                return response;
            } else {
                response.setMessage("当前未完成的盘点任务明细为空!");
            }
        } else {
            WhInventoryTaskQueryCondition query1 = new WhInventoryTaskQueryCondition();
            query1.setReceiveUser(receiveUser);
            query1.setId(taskId);
            tasks = queryWhInventoryTaskAndItemList(query1, null);
            if (CollectionUtils.isEmpty(tasks)
                    || CollectionUtils.isEmpty(tasks.get(0).getWhInventoryTaskItems())) {
                response.setMessage("当前盘点任务不存在!");
                return response;
            }

            WhInventoryTask task = tasks.get(0);

            List<Integer> canReceiveStatus = Arrays.asList(InventoryTaskStatus.UNRECEIVED.intCode(),InventoryTaskStatus.INVENTORY.intCode());
            if (!canReceiveStatus.contains(task.getStatus())){
                response.setMessage("当前盘点任务状态不为未领取或盘点中，不可进行领取操作!");
                return response;
            }

            if (!receiveUser.equals(task.getReceiveUser())) {
                response.setMessage("当前盘点任务已分配给其它人!");
                return response;
            }

            response = updateToReceiveInventoryTask(tasks.get(0), receiveUser);
        }
        return response;
    }

    /**
     * 查询是否存在未审核完成的盘点任务
     */
    @Override
    public List<Integer> queryNotAuditSku(List<Integer> stockIdList) {
        if (CollectionUtils.isEmpty(stockIdList)) {
            return new ArrayList<>();
        }
        return whInventoryTaskItemDao.queryNotAuditSku(stockIdList);
    }

    @Override
    public List<String> queryAnomalousCauseList() {
        try {
            String anomalousCause = CacheUtils.SystemParamGet("WMS_INVENTORY.INVENTORY_ANOMALOUS_CAUSE").getParamValue();
            // 把引号去掉
            anomalousCause = anomalousCause.replaceAll("\"", "");
            // 把空格去掉
            anomalousCause = anomalousCause.replaceAll(" ", "");
            // 将字符串数组转换成集合list
            return Arrays.asList(StringUtils.split(anomalousCause, ","));
        } catch (Exception e) {
            logger.error("没有盘点异常原因" + e);
        }
        return new ArrayList<>();
    }


    /**
     * 分配盘点任务-->更新状态
     */
    public void updateToAllocationInventoryTask(WhInventoryTask task, Integer receiveUser) {

        WhInventoryTask updateTask = new WhInventoryTask();
        updateTask.setId(task.getId());
        updateTask.setStatus(task.getStatus());
        updateTask.setReceiveUser(receiveUser);
        updateTask.setReceiveDate(new Timestamp(System.currentTimeMillis()));
        int returnInt = whInventoryTaskDao.updateWhInventoryTaskLock(updateTask);
        Assert.state(returnInt == 1, "分配失败，该盘点任务状态改变");
        SaleUser originalBuyer = new SaleUser();
        SaleUser newBuyer = new SaleUser();
        if (task.getReceiveUser() != null) {
            originalBuyer = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(task.getReceiveUser()), SaleUser.class);
        }
        if (receiveUser != null) {
            newBuyer = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(receiveUser), SaleUser.class);
        }
        SystemLogUtils.INVENTORYTASK.log(task.getId(), "分配盘点任务", new String[][]{{"原领取人", originalBuyer.getUsername() + "-" + originalBuyer.getName()},
                {"新领取人", newBuyer.getUsername() + "-" + newBuyer.getName()}});
    }

    /**
     * @Description: 盘点下一步
     */
    @Override
    public ResponseJson updateInventoryItemComplete(WhInventoryTask task,WhInventoryTaskItem taskItem, Integer inventoryQuantity, Boolean next) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setId(taskItem.getStockId());
        WhStock whStock = whStockService.queryWhStock(query);

        //根据sku查询中转仓库存
        //PDA盘点时取汇总的库位库存，优先增删本地仓可用库存，当本地仓库存不够扣减时提示用户是否继续，如果继续则修改中转仓库存，优先扣减库存量高的货主
        TransferStockQueryCondition queryCondition = new TransferStockQueryCondition();
        queryCondition.setStockId(taskItem.getStockId());
        List<TransferStock> transferStocks = transferStockService.queryTransferStocks(queryCondition, null);

        if (whStock == null && CollectionUtils.isEmpty(transferStocks)) {
            response.setMessage("SKU库存记录为空!");
            return response;
        }

        Integer transSurplusQuantity = transferStocks.stream()
                .mapToInt(i -> i.getSurplusQuantity() == null ? 0 : i.getSurplusQuantity()).sum();
        Integer transAllotQuantity = transferStocks.stream()
                .mapToInt(i -> i.getAllotQuantity() == null ? 0 : i.getAllotQuantity()).sum();
        Integer transLocationQuantity = transSurplusQuantity + transAllotQuantity;

        Integer locationQuantity = whStock == null || whStock.getLocationQuantity() == null ? 0
                : whStock.getLocationQuantity();
        Integer surplusQuantity = whStock == null || whStock.getSurplusQuantity() == null ? 0
                : whStock.getSurplusQuantity();

        Integer diffQuantity = inventoryQuantity - locationQuantity - transLocationQuantity;

        if (next == null && diffQuantity + surplusQuantity < 0) {
            response.setMessage("本地仓库存不够扣减，是否继续扣减中转仓库存!");
            response.setExceptionCode("1");// PDA弹选择框
            return response;
        }

        WhInventoryTaskItem updateTaskItem = new WhInventoryTaskItem();
        updateTaskItem.setId(taskItem.getId());
        updateTaskItem.setStockId(taskItem.getStockId());
        updateTaskItem.setSku(taskItem.getSku());
        updateTaskItem.setAnomalousCause(taskItem.getAnomalousCause());
        updateTaskItem.setSkuInventoryDate(new Timestamp(System.currentTimeMillis()));

        //判断是否最后一次盘点类型
        Integer inventoryType =null;
        switch (task.getInventoryCount()) {
            case 1:
                inventoryType=InventoryTaskLevel.FIRST.intCode();
                break;
            case 2:
                inventoryType=InventoryTaskLevel.REPEAT.intCode();
                break;
            case 3:
                 inventoryType=InventoryTaskLevel.FINALLY.intCode();
                 break;
            case 4:
                 inventoryType=InventoryTaskLevel.CONFIRM.intCode();
                 break;
        }
        boolean zoneInventory = Objects.equals(InventoryTaskType.ZONE.intCode(), task.getTaskType());
        if (taskItem.getTaskLevel().equals(InventoryTaskLevel.FIRST.intCode())) {
            updateTaskItem.setInventoryQuantity(inventoryQuantity);
            updateTaskItem.setDiffQuantity(diffQuantity);
            // 对于盘点操作，需要注意， 这里如果设置了ConfirmDiff属性的值，那么就不会再生成下一轮的盘点任务的了
            if (diffQuantity.equals(0)) {
                // 初盘无差异 -->完成
                updateTaskItem.setConfirmQuantity(inventoryQuantity);
                updateTaskItem.setConfirmDiff(diffQuantity);
                updateTaskItem.setStatus(InventoryTaskStatus.COMPLETED.intCode());
            } else {
                // 初盘有差异-->完成，非库区盘点最后一次操作or盘点次数为1次的库区盘点操作有差异且盘点数量不为0-->待审核
                if ((!zoneInventory && taskItem.getTaskLevel().equals(inventoryType))
                        || (zoneInventory && !Objects.equals(0,inventoryQuantity) && Objects.equals(1, task.getInventoryCount()))){
                    updateTaskItem.setConfirmQuantity(inventoryQuantity);
                    updateTaskItem.setConfirmDiff(diffQuantity);
                    updateTaskItem.setStatus(InventoryTaskStatus.UNREVIEW.intCode());
                }else {
                    updateTaskItem.setStatus(InventoryTaskStatus.COMPLETED.intCode());
                }
            }
        } else if (taskItem.getTaskLevel().equals(InventoryTaskLevel.REPEAT.intCode())) {
            updateTaskItem.setRepeatQuantity(inventoryQuantity);
            updateTaskItem.setRepeatDiffQuantity(diffQuantity);
            if (diffQuantity.equals(0)) {
                // 复盘无差异-->完成
                updateTaskItem.setConfirmQuantity(inventoryQuantity);
                updateTaskItem.setConfirmDiff(diffQuantity);
                updateTaskItem.setStatus(InventoryTaskStatus.COMPLETED.intCode());
            } else {
                // 复盘有差异-->待审核
                if (diffQuantity.equals(taskItem.getDiffQuantity()) || taskItem.getTaskLevel().equals(inventoryType)) {
                    // 复盘差异=初盘差异或者是最后一次盘点操作了
                    updateTaskItem.setConfirmQuantity(inventoryQuantity);
                    updateTaskItem.setConfirmDiff(diffQuantity);
                    updateTaskItem.setStatus(InventoryTaskStatus.UNREVIEW.intCode());
                } else {
                    updateTaskItem.setStatus(InventoryTaskStatus.COMPLETED.intCode());
                }
            }

        } else if (taskItem.getTaskLevel().equals(InventoryTaskLevel.FINALLY.intCode())) {
            updateTaskItem.setFinallyQuantity(inventoryQuantity);
            updateTaskItem.setFinallyDiffQuantity(diffQuantity);
            if (diffQuantity.equals(0)) {
                // 终盘无差异-->完成
                updateTaskItem.setConfirmQuantity(inventoryQuantity);
                updateTaskItem.setConfirmDiff(diffQuantity);
                updateTaskItem.setStatus(InventoryTaskStatus.COMPLETED.intCode());
            } else {
                // 终盘有差异-->待审核
                if (diffQuantity.equals(taskItem.getRepeatDiffQuantity())
                        || diffQuantity.equals(taskItem.getDiffQuantity())
                        || taskItem.getTaskLevel().equals(inventoryType)) {
                    // 终盘差异=初盘差异or复盘差异or最后一次盘点操作
                    updateTaskItem.setConfirmQuantity(inventoryQuantity);
                    updateTaskItem.setConfirmDiff(diffQuantity);
                    updateTaskItem.setStatus(InventoryTaskStatus.UNREVIEW.intCode());
                } else {
                    updateTaskItem.setStatus(InventoryTaskStatus.COMPLETED.intCode());
                }
            }
        } else if (taskItem.getTaskLevel().equals(InventoryTaskLevel.CONFIRM.intCode())) {
            updateTaskItem.setConfirmQuantity(inventoryQuantity);
            updateTaskItem.setConfirmDiff(diffQuantity);
            if (diffQuantity.equals(0)) {
                // 确认盘无差异-->完成
                updateTaskItem.setStatus(InventoryTaskStatus.COMPLETED.intCode());
            } else {
                // 确认盘存在差异-->待审核
                updateTaskItem.setStatus(InventoryTaskStatus.UNREVIEW.intCode());
            }
        }

        if (whInventoryTaskItemDao.updateWhInventoryTaskItem(updateTaskItem) >= 1) {
            boolean updateResult = true;
            if (Objects.nonNull(taskItem.getDemandId())) {
                WhPickInventoryDemand updateDemand = new WhPickInventoryDemand();
                updateDemand.setId(taskItem.getDemandId());
                if (updateTaskItem.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode())) {
                    updateDemand.setStatus(PickInventoryDemandStatus.COMPLETED.intCode());
                } else {
                    updateDemand.setStatus(PickInventoryDemandStatus.UNREVIEW.intCode());
                }
                updateResult = whPickInventoryDemandDao.updateWhPickInventoryDemand(updateDemand) >= 1;
            }
            if (updateResult) {
                SystemLogUtils.INVENTORYTASKITEM.log(taskItem.getId(), "盘点SKU",
                        new String[][]{{"历史状态", taskItem.getStatusName()},
                                {"变更状态", updateTaskItem.getStatusName()}, {"差异值", String.valueOf(diffQuantity)}});
                if (Objects.nonNull(taskItem.getDemandId())) {
                    SystemLogUtils.PICKINVENTORYDEMAND.log(taskItem.getDemandId(), "盘点SKU",
                            new String[][]{{"历史状态", taskItem.getStatusName()},
                                    {"变更状态", updateTaskItem.getStatusName()}, {"差异值", String.valueOf(diffQuantity)}});
                }
                // 盘点完成（有确认数量，不经审核直接完成、不生成高一级的盘点任务的覆盖所有未生成任务的SKU需求）
                if (updateTaskItem.getConfirmDiff() != null
                        && updateTaskItem.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode())) {
                    updateOtherDemandComplete(updateTaskItem);
                }
                response.setStatus(StatusCode.SUCCESS);
            }
        }
        return response;
    }

    /**
     * @Description: 盘点完成
     */
    @Override
    public ResponseJson updateInventoryComplete(WhInventoryTask task) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhInventoryTaskItemQueryCondition query = new WhInventoryTaskItemQueryCondition();
        query.setTaskId(task.getId());
        List<WhInventoryTaskItem> items = whInventoryTaskItemDao.queryWhInventoryTaskItemList(query, null);
        if (CollectionUtils.isNotEmpty(items)) {
            boolean zoneInventory = Objects.equals(InventoryTaskType.ZONE.intCode(), task.getTaskType());
            List<WhInventoryTaskItem> generateItems = new ArrayList<>();

            for (WhInventoryTaskItem item : items) {
                if (item.getStatus().equals(InventoryTaskStatus.UNRECEIVED.intCode())
                        || item.getStatus().equals(InventoryTaskStatus.INVENTORY.intCode())) {
                    response.setMessage("目前任务还有SKU未盘点完");
                    return response;
                }
                boolean needGenerate = true;
                // 盘点次数为1次，且差异值为0或库存不为0的，则不生成下一班盘点任务
                if (zoneInventory && task.getInventoryCount() == 1){
                    if (Objects.equals(0, item.getDiffQuantity()) || !Objects.equals(0, item.getInventoryQuantity())){
                        needGenerate = false;
                    }
                }

                if (item.getConfirmDiff() == null && item.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode()) && needGenerate) {
                    generateItems.add(item);
                }
            }

            // 处理异常数据，盘点次数不为1次的时候，异常数据生成盘点任务
            List<InventoryAbnormalData> generateItemAbnormalData = new ArrayList<>();
//            InventoryAbnormalDataQueryCondition abnormalQuery = new InventoryAbnormalDataQueryCondition();
//            abnormalQuery.setTaskNumber(task.getTaskNo());
//            List<InventoryAbnormalData> abnormalDataList = inventoryAbnormalDataService.queryInventoryAbnormalDatas(abnormalQuery,null);
//            if (CollectionUtils.isNotEmpty(abnormalDataList) && task.getInventoryCount() != 1){
//                for (InventoryAbnormalData abnormalData : abnormalDataList) {
//                    WhInventoryTaskItem abnormalTaskItem = new WhInventoryTaskItem();
//                    abnormalTaskItem.setSku(abnormalData.getSku());
//                    abnormalTaskItem.setStockId(abnormalData.getStockId());
//                    if (Objects.equals(InventoryTaskLevel.FIRST.intCode(),task.getTaskLevel())){
//                        abnormalTaskItem.setInventoryQuantity(abnormalData.getUpQuantity());
//                        abnormalTaskItem.setDiffQuantity(abnormalData.getUpQuantity());
//                    }else if(Objects.equals(InventoryTaskLevel.REPEAT.intCode(),task.getTaskLevel())){
//                        abnormalTaskItem.setRepeatQuantity(abnormalData.getUpQuantity());
//                        abnormalTaskItem.setRepeatDiffQuantity(abnormalData.getUpQuantity());
//                    }else if(Objects.equals(InventoryTaskLevel.FINALLY.intCode(),task.getTaskLevel())){
//                        abnormalTaskItem.setFinallyQuantity(abnormalData.getUpQuantity());
//                        abnormalTaskItem.setFinallyDiffQuantity(abnormalData.getUpQuantity());
//                    }else{
//                        abnormalTaskItem.setConfirmQuantity(abnormalData.getUpQuantity());
//                        abnormalTaskItem.setConfirmDiff(abnormalData.getUpQuantity());
//                    }
//                    generateItems.add(abnormalTaskItem);
//                }
//                generateItemAbnormalData = abnormalDataList;
//            }

            WhInventoryTask updateTask = new WhInventoryTask();
            updateTask.setId(task.getId());
            updateTask.setInventoryUser(DataContextHolder.getUserId());
            updateTask.setInventoryDate(new Timestamp(System.currentTimeMillis()));
            updateTask.setReviewDate(new Timestamp(System.currentTimeMillis()));
            updateTask.setReviewUser(DataContextHolder.getUserId());
            // 已完成、生成下一班盘点
            updateTask.setStatus(InventoryTaskStatus.COMPLETED.intCode());
            // 存在还未完成的待审核项,则认为状态变更为待审核
            boolean bool = items.stream().anyMatch(item -> Objects.equals(InventoryTaskStatus.UNREVIEW.intCode(), item.getStatus()));
            if (bool){
                updateTask.setStatus(InventoryTaskStatus.UNREVIEW.intCode());
            }
            updateTask.setInventoryCount(task.getInventoryCount());
            if (whInventoryTaskDao.updateWhInventoryTask(updateTask) >= 1) {
                SystemLogUtils.INVENTORYTASK.log(task.getId(), "盘点完成",
                        new String[][]{{"原状态", task.getStatusName()}, {"变更状态", updateTask.getStatusName()},
                                {"SKU确认个数", String.valueOf(items.size() - generateItems.size())}});
                if (CollectionUtils.isNotEmpty(generateItems)) {
                    // 生成新的盘点任务
                    if (!task.getTaskLevel().equals(InventoryTaskLevel.CONFIRM.intCode())) {
                        if (creatNewWhInventoryTask(task, generateItems, generateItemAbnormalData)) {
                            response.setStatus(StatusCode.SUCCESS);
                        } else {
                            response.setMessage("创建新盘点任务失败");
                        }
                    } else {
                        response.setStatus(StatusCode.SUCCESS);
                    }
                } else {
                    response.setStatus(StatusCode.SUCCESS);
                }
            } else {
                response.setMessage("更新失败");
            }
        } else {
            response.setMessage("目前任务列表数为0");
        }
        return response;
    }

    /**
     * @param lastTask
     * @param lastTaskItems
     * @return
     * @Title: creatNewWhInventoryTask
     * @Description: 盘点完成生成更高级别的盘点任务
     */
    public boolean creatNewWhInventoryTask(WhInventoryTask lastTask, List<WhInventoryTaskItem> lastTaskItems,List<InventoryAbnormalData> lastAbnormalData) {
        boolean result = false;
        Integer maxLevel = whInventoryTaskDao.queryWhInventoryTaskGradeMax();
        if (maxLevel == null) {
            maxLevel = 0;
        }
        WhInventoryTask task = new WhInventoryTask();
        String taskNoPrefix = lastTask.getTaskNoPrefix();
        // task.setTaskType(InventoryTaskType.PICKOUTOFSTOCK.intCode());
        task.setTaskType(lastTask.getTaskType());
        task.setStatus(InventoryTaskStatus.UNRECEIVED.intCode());
        task.setTaskNoPrefix(taskNoPrefix);
        task.setGrade(maxLevel + 1);// 新生成默认置顶

        if (lastTask.getTaskLevel().equals(InventoryTaskLevel.FIRST.intCode())) {
            // 复盘
            task.setTaskLevel(InventoryTaskLevel.REPEAT.intCode());
            task.setTaskNo(taskNoPrefix + "B");
            // 库区盘点，且其盘点次数为1次的，下次生成的就直接是确认盘任务了
            boolean zoneInventory = Objects.equals(InventoryTaskType.ZONE.intCode(), lastTask.getTaskType());
            if (zoneInventory && lastTask.getInventoryCount() == 1){
                task.setTaskLevel(InventoryTaskLevel.CONFIRM.intCode());
                task.setTaskNo(taskNoPrefix + "D");
            }
        } else if (lastTask.getTaskLevel().equals(InventoryTaskLevel.REPEAT.intCode())) {
            // 终盘
            task.setTaskLevel(InventoryTaskLevel.FINALLY.intCode());
            task.setTaskNo(taskNoPrefix + "C");
        } else if (lastTask.getTaskLevel().equals(InventoryTaskLevel.FINALLY.intCode())) {
            // 确认盘
            task.setTaskLevel(InventoryTaskLevel.CONFIRM.intCode());
            task.setTaskNo(taskNoPrefix + "D");
        }


        task.setInventoryCount(lastTask.getInventoryCount());
        whInventoryTaskDao.createWhInventoryTask(task);
        if (task.getId() != null) {
            List<WhInventoryTaskItem> taskItems = new ArrayList<>();
            List<WhPickInventoryDemand> demands = new ArrayList<>();
            for (WhInventoryTaskItem lastTaskItem : lastTaskItems) {
                if (Objects.nonNull(lastTaskItem.getDemandId())) {
                    WhPickInventoryDemand updateDemand = new WhPickInventoryDemand();
                    updateDemand.setId(lastTaskItem.getDemandId());
                    updateDemand.setTaskLevel(task.getTaskLevel());
                    updateDemand.setStatus(PickInventoryDemandStatus.UNRECEIVED.intCode());
                    demands.add(updateDemand);
                }

                WhInventoryTaskItem taskItem = new WhInventoryTaskItem();
                taskItem.setSku(lastTaskItem.getSku());
                taskItem.setStockId(lastTaskItem.getStockId());
                taskItem.setDemandId(lastTaskItem.getDemandId());
                taskItem.setTaskId(task.getId());
                taskItem.setTaskLevel(task.getTaskLevel());
                taskItem.setStatus(InventoryTaskStatus.UNRECEIVED.intCode());
                // 继承上一步的盘点结果
                taskItem.setInventoryQuantity(lastTaskItem.getInventoryQuantity());
                taskItem.setDiffQuantity(lastTaskItem.getDiffQuantity());
                taskItem.setRepeatQuantity(lastTaskItem.getRepeatQuantity());
                taskItem.setRepeatDiffQuantity(lastTaskItem.getRepeatDiffQuantity());
                taskItem.setFinallyQuantity(lastTaskItem.getFinallyQuantity());
                taskItem.setFinallyDiffQuantity(lastTaskItem.getFinallyDiffQuantity());

                taskItems.add(taskItem);
            }
            whInventoryTaskItemDao.batchCreateWhInventoryTaskItem(taskItems);
            whPickInventoryDemandDao.batchUpdateWhPickInventoryDemand(demands);

            List<InventoryAbnormalData> abnormalData = new ArrayList<>();
            for (InventoryAbnormalData abnormal : lastAbnormalData){
                InventoryAbnormalData updateAbnormal = new InventoryAbnormalData();
                updateAbnormal.setId(abnormal.getId());
                updateAbnormal.setNextStepTaskId(task.getId());
                abnormalData.add(updateAbnormal);
            }
            inventoryAbnormalDataService.batchUpdateInventoryAbnormalData(abnormalData);

            SystemLogUtils.INVENTORYTASK.log(task.getId(), "生成盘点任务-" + task.getTaskLevelName(),
                    new String[][]{{"盘点任务号", task.getTaskNo()}, {"SKU数", String.valueOf(taskItems.size())}});
            /*
             * for (WhInventoryTaskItem taskItem : taskItems){
             * SystemLogUtils.INVENTORYTASKITEM.log(taskItem.getId(), "生成盘点任务-" +
             * task.getTaskLevelName(), new String[][] { { "盘点任务号", task.getTaskNo() } }); }
             */
            for (WhPickInventoryDemand demand : demands) {
                SystemLogUtils.PICKINVENTORYDEMAND.log(demand.getId(), "生成盘点任务-" + task.getTaskLevelName(),
                        new String[][]{{"盘点任务号", task.getTaskNo()}});
            }
            result = true;
        } else {
            logger.error("创建任务失败:taskNo--> " + task.getTaskNo());
        }
        return result;
    }

    @Override
    @StockServicelock
    public ResponseJson updateForConfirmAndStock(List<String> skuList, List<WhInventoryTaskItem> inventoryTaskItems, List<String> batchNos) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(inventoryTaskItems)) {
            response.setMessage("参数为空！");
            return response;
        }
        WhInventoryTaskItemQueryCondition itemQuery = new WhInventoryTaskItemQueryCondition();
        itemQuery.setIds(inventoryTaskItems.stream().map(WhInventoryTaskItem::getId).collect(Collectors.toList()));
        itemQuery.setStatusStr(InventoryTaskStatus.UNREVIEW.getCode());
        List<WhInventoryTaskItem> taskItems = whInventoryTaskItemDao.queryWhInventoryTaskItemDetailList(itemQuery, null);

        if (CollectionUtils.isEmpty(taskItems)) {
            response.setMessage("没有待审核的数据，请确认是否跟别人选择了重复的需求，重新筛选！");
            return response;
        }

        inventoryTaskItems = taskItems;

        List<Integer> stockIdList = inventoryTaskItems.stream().map(WhInventoryTaskItem::getStockId).distinct()
                .collect(Collectors.toList());
        // 生成批次详细 选择了批次
        if (CollectionUtils.isNotEmpty(batchNos)) {
            Map<String, Integer> batcnNoMap = new HashMap<>();
            Integer totalQty = 0;

            ExpManageItem expManageItem = new ExpManageItem();
            Integer skuConfirmDiff;
            if ((skuConfirmDiff = inventoryTaskItems.get(0).getConfirmDiff()) > 0) {
                expManageItem.setType(DrpTurnoverOderType.INVENTORY_ORDER_IN.intCode());
            } else {
                expManageItem.setType(DrpTurnoverOderType.INVENTORY_ORDER_OUT.intCode());
            }

            ExpManageQueryCondition expManageQueryCondition = new ExpManageQueryCondition();
            expManageQueryCondition.setSku(skuList.get(0));
            List<ExpManage> expManages = expManageService.queryExpManages(expManageQueryCondition, null);
            expManages = expManages.stream().filter(e -> (e.getQuantity() != null && e.getQuantity() > 0)).collect(Collectors.toList());
            Map<Integer, Integer> notBatchStockMap = expManageService.getNotBatchStockByStockId(stockIdList, expManages);

            for (String batchNoQty : batchNos) {
                String[] split = StringUtils.split(batchNoQty, "|");
                if (split.length < 2)
                    throw new RuntimeException("保质期批次信息错误," + batchNoQty);
                if (batcnNoMap.containsKey(split[0]))
                    throw new RuntimeException("保质期批次选择重复," + batchNoQty);
                if (StringUtils.contains(batchNoQty, "notBatch")) {
                    if (skuConfirmDiff < 0 && ((notBatchStockMap.get(stockIdList.get(0)) < (Integer.parseInt(split[1]))))) {
                        throw new RuntimeException("批次盘减数量大于保质期批次剩余数量");
                    }
                } else {
                    batcnNoMap.put(split[0], Integer.parseInt(split[1]));
                }
                totalQty += Integer.parseInt(split[1]);
            }

            if (!totalQty.equals(Math.abs(skuConfirmDiff))) {
                throw new RuntimeException("保质期批次录入数量与盘点差异不对等");
            }
            expManageItem.setRelationId(inventoryTaskItems.get(0).getId());
            for (String batcnNo : batcnNoMap.keySet()) {
                if (skuConfirmDiff > 0) {
                    expManageItem.setQuantity(batcnNoMap.get(batcnNo));
                } else {
                    expManageItem.setQuantity(-batcnNoMap.get(batcnNo));
                }
                expManageItem.setBatchNos(CommonUtils.splitList(batcnNo, ","));
                expManageService.updateExpManageAndItem(expManageItem);
            }
        }
        List<String> msgList = new ArrayList<String>();
        Map<Integer, String> map = new HashMap<>();
        Map<String, String> skuMap = whSkuService.queryWhSkuDiscard(skuList);
        List<String> discardSku = whSkuService.queryDiscardSku(skuList);
        for (WhInventoryTaskItem taskItem : inventoryTaskItems) {
            // 判断SKU是否合并
            if (skuMap.get(taskItem.getSku()) != null) {
                msgList.add("SKU：" + taskItem.getSku() + "已被合并到新SKU: " + skuMap.get(taskItem.getSku()));
                continue;
            }
            if (CollectionUtils.isNotEmpty(discardSku) && discardSku.contains(taskItem.getSku())) {
                msgList.add("SKU：" + taskItem.getSku() + "已被废弃！");
                continue;
            }

            if (!InventoryTaskStatus.UNREVIEW.intCode().equals(taskItem.getStatus())) {
                response.setMessage("有不是待审核状态的明细，请筛选状态后再审核！");
                return response;
            }
            if (taskItem.getTaskType() != 7) {
                if (taskItem.getConfirmQuantity() == null || taskItem.getConfirmDiff() == null) {
                    logger.error("审核失败, 盘点明细确认数量为空:taskItemId--> " + taskItem.getId());
                    continue;
                }
            }

            WhPickInventoryDemandQueryCondition query = new WhPickInventoryDemandQueryCondition();
            query.setSku(taskItem.getSku());
            query.setStockId(taskItem.getStockId());
            query.setStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
            List<WhPickInventoryDemand> ungenerateList = whPickInventoryDemandService
                    .queryWhPickInventoryDemandAndInventoryTaskList(query, null);

            WhInventoryTaskItem updateItem = new WhInventoryTaskItem();
            updateItem.setId(taskItem.getId());
            updateItem.setStatus(InventoryTaskStatus.COMPLETED.intCode());
            updateItem.setReviewUser(DataContextHolder.getUserId());
            updateItem.setReviewDate(new Timestamp(System.currentTimeMillis()));

            WhPickInventoryDemand updateDemand = null;
            if (Objects.nonNull(taskItem.getDemandId())) {
                updateDemand = new WhPickInventoryDemand();
                updateDemand.setId(taskItem.getDemandId());
                updateDemand.setStatus(PickInventoryDemandStatus.COMPLETED.intCode());
            }
            if (whInventoryTaskItemDao.updateWhInventoryTaskItem(updateItem) < 1) {
                logger.error("审核失败, 修改盘点明细状态失败:taskItemId--> " + taskItem.getId());
                continue;
            }
            map.put(taskItem.getTaskId(), taskItem.getTaskStatusName());
            if (taskItem.getTaskType() != 7 && taskItem.getTaskType() != 9 && taskItem.getTaskType() != 12 && taskItem.getTaskType() != 13) {
                if (Objects.nonNull(updateDemand) && whPickInventoryDemandDao.updateWhPickInventoryDemand(updateDemand) < 1) {
                    throw new RuntimeException("审核失败, 更新盘点明细失败!");
                }
            }
            if (!taskItem.getConfirmDiff().equals(0)) {
                Map<String, ResponseJson> responseMap = inventoryUpdateStockService
                        .batchUpdateStockByVerifyInventoryTask(taskItem);
                if (responseMap != null && responseMap.get(taskItem.getSku()) != null) {
                    ResponseJson responseResult = responseMap.get(taskItem.getSku());
                    if (responseResult.getStatus().equals(StatusCode.FAIL)) {
                        SystemLogUtils.INVENTORYTASKITEM.log(taskItem.getId(), "审核失败",
                                new String[][]{{"原因", responseResult.getMessage()}});
                        throw new RuntimeException(
                                "审核失败, 更新库存失败!-->" + (responseResult.getMessage() == null ? "" : responseResult.getMessage()));
                    }
                    msgList.add(responseResult.getMessage());
                } else {
                    throw new RuntimeException("审核失败, 更新库存失败!");
                }
            }else {
                msgList.add(taskItem.getSku()+"审核成功");
            }

            SystemLogUtils.INVENTORYTASKITEM.log(taskItem.getId(), "审核通过",
                    new String[][]{{"历史状态", taskItem.getStatusName()},
                            {"变更状态", InventoryTaskStatus.COMPLETED.getName()},
                            {"差异值", String.valueOf(taskItem.getConfirmDiff())}});
            if (Objects.nonNull(taskItem.getDemandId())) {
                SystemLogUtils.PICKINVENTORYDEMAND.log(taskItem.getDemandId(), "审核通过",
                        new String[][]{{"历史状态", taskItem.getStatusName()}, {"变更状态", updateDemand.getStatusName()},
                                {"差异值", String.valueOf(taskItem.getConfirmDiff())}});
            }

            // 审核通过覆盖所有未生成盘点的需求
            if (CollectionUtils.isNotEmpty(ungenerateList)) {
                List<WhPickInventoryDemand> updateDemands = new ArrayList<>();
                for (WhPickInventoryDemand demand : ungenerateList) {
                    WhPickInventoryDemand ungenerate = new WhPickInventoryDemand();
                    ungenerate.setId(demand.getId());
                    ungenerate.setLastStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
                    ungenerate.setStatus(PickInventoryDemandStatus.COMPLETED.intCode());
                    updateDemands.add(ungenerate);
                }
                // 覆盖相同SKU未生成盘点任务的盘点需求的状态及盘点结果
                int[] updateLines = whPickInventoryDemandDao.batchUpdateWhPickInventoryDemand(updateDemands);
                for (int i = 0; i < updateLines.length; i++) {
                    if (updateLines[i] >= 1) {
                        SystemLogUtils.PICKINVENTORYDEMAND.log(updateDemands.get(i).getId(), "审核通过-覆盖未生成SKU",
                                new String[][]{{"历史状态", PickInventoryDemandStatus.UNGENERATE.getName()},
                                        {"变更状态", PickInventoryDemandStatus.COMPLETED.getName()},
                                        {"确认值", String.valueOf(taskItem.getConfirmQuantity())},
                                        {"差异值", String.valueOf(taskItem.getConfirmDiff())}});
                    }
                }
            }
        }
        // 更新任务状态
        completedTask(new ArrayList<>(map.keySet()), InventoryTaskStatus.COMPLETED.intCode());
        response.setMessage(JSON.toJSONString(msgList));
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    // 校验当前任务的所有sku是否完成或废弃 满足则更新任务状态
    @Override
    public void completedTask(List<Integer> ids, Integer originalStatus) {
        if (CollectionUtils.isNotEmpty(ids) && originalStatus != null) {
            WhInventoryTaskQueryCondition condition = new WhInventoryTaskQueryCondition();
            condition.setIds(ids);
            List<WhInventoryTask> whInventoryTasks = queryWhInventoryTaskAndItemList(condition, null);
            for (WhInventoryTask task : whInventoryTasks) {
                Integer status = originalStatus;
                String oldStatusName = task.getStatusName();
                List<WhInventoryTaskItem> whInventoryTaskItems = task.getWhInventoryTaskItems();
                if (CollectionUtils.isEmpty(whInventoryTaskItems)) continue;
                // 存在还未完成的项
                boolean bool = whInventoryTaskItems.stream().anyMatch(item -> (item.getStatus().equals(InventoryTaskStatus.UNRECEIVED.intCode())
                        || item.getStatus().equals(InventoryTaskStatus.INVENTORY.intCode()) || item.getStatus().equals(InventoryTaskStatus.UNREVIEW.intCode())));
                if (bool){
                    boolean existNotZeroCompleted = whInventoryTaskItems.stream().anyMatch(item -> (
                            item.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode()) && item.getConfirmDiff() != 0));
                    if (!existNotZeroCompleted || !status.equals(InventoryTaskStatus.COMPLETED.intCode())) {
                        continue;
                    }
                    // 存在已完成的项且存在未完成的项
                    status = InventoryTaskStatus.PARTIAL_UNREVIEW.intCode();
                }
                if (Objects.equals(InventoryTaskStatus.getNameByCode(String.valueOf(status)), oldStatusName)){
                    continue;
                }
                // 更新标记完成
                WhInventoryTask updateTask = new WhInventoryTask();
                updateTask.setId(task.getId());
                if (status.equals(InventoryTaskStatus.DISCARDED.intCode())) {
                    // 废弃时，任务存在已完成的条目，则调整会已完成
                    boolean bool1 = whInventoryTaskItems.stream().anyMatch(item -> (item.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode())));
                    if (bool1) {
                        status = InventoryTaskStatus.COMPLETED.intCode();
                    }
                }
                if (status.equals(InventoryTaskStatus.COMPLETED.intCode())) {
                    updateTask.setReviewDate(new Timestamp(System.currentTimeMillis()));
                    updateTask.setReviewUser(DataContextHolder.getUserId());
                }
                updateTask.setStatus(status);
                if (whInventoryTaskDao.updateWhInventoryTask(updateTask) >= 1) {
                    SystemLogUtils.INVENTORYTASK.log(updateTask.getId(), "盘点完成",
                            new String[][]{{"原状态", oldStatusName}, {"变更状态", updateTask.getStatusName()}});
                }
            }
        }
    }

    public void updateOtherDemandComplete(WhInventoryTaskItem taskItem) {
        WhPickInventoryDemandQueryCondition query = new WhPickInventoryDemandQueryCondition();
        query.setSku(taskItem.getSku());
        query.setStockId(taskItem.getStockId());
        query.setStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
        List<WhPickInventoryDemand> ungenerateList = whPickInventoryDemandService
                .queryWhPickInventoryDemandAndInventoryTaskList(query, null);
        // 盘点条目完成确认覆盖所有未生成盘点的需求
        if (CollectionUtils.isNotEmpty(ungenerateList)) {
            List<WhPickInventoryDemand> updateDemands = new ArrayList<>();
            for (WhPickInventoryDemand demand : ungenerateList) {
                WhPickInventoryDemand ungenerate = new WhPickInventoryDemand();
                ungenerate.setId(demand.getId());
                ungenerate.setLastStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
                ungenerate.setStatus(PickInventoryDemandStatus.COMPLETED.intCode());
                updateDemands.add(ungenerate);
            }
            // 覆盖相同SKU未生成盘点任务的盘点需求的状态及盘点结果
            int[] updateLines = whPickInventoryDemandDao.batchUpdateWhPickInventoryDemand(updateDemands);
            for (int i = 0; i < updateLines.length; i++) {
                if (updateLines[i] >= 1) {
                    SystemLogUtils.PICKINVENTORYDEMAND.log(updateDemands.get(i).getId(), "盘点条目完成确认-覆盖未生成SKU",
                            new String[][]{{"历史状态", PickInventoryDemandStatus.UNGENERATE.getName()},
                                    {"变更状态", PickInventoryDemandStatus.COMPLETED.getName()},
                                    {"确认值", String.valueOf(taskItem.getConfirmQuantity())},
                                    {"差异值", String.valueOf(taskItem.getConfirmDiff())}});
                }
            }
        }
    }

    /**
     * 审核驳回
     */
    @Override
    public boolean updateForDismissed(Integer id, String type,String message) {
        boolean result = false;
        WhInventoryTaskItem taskItem = whInventoryTaskItemDao.queryWhInventoryTaskItem(id);
        if (taskItem != null) {
            if (taskItem.getStatus().equals(InventoryTaskStatus.UNREVIEW.intCode())) {
                if ("reset".equals(type)) {
                    // 将盘点需求改为待生成
                    result = updateDismissedToReset(taskItem);
                } else if ("discard".equals(type)) {
                    // 废弃需求
                    result = updateDismissedToDiscard(taskItem,message);
                }
                // 标记状态
                if (result) {
                    completedTask(Collections.singletonList(taskItem.getTaskId()), InventoryTaskStatus.DISCARDED.intCode());
                }
            } else {
                logger.error("审核驳回失败, 盘点明细不是待审核状态:taskItemId--> " + id);
            }
        } else {
            logger.error("审核驳回失败, 盘点明细不存在:taskItemId--> " + id);
        }
        return result;
    }

    /**
     * 驳回盘点任务、需求待生成
     */
    public boolean updateDismissedToReset(WhInventoryTaskItem taskItem) {
        WhInventoryTaskItem updateItem = new WhInventoryTaskItem();
        updateItem.setId(taskItem.getId());
        updateItem.setStatus(InventoryTaskStatus.DISCARDED.intCode());
        int updateLines = whInventoryTaskItemDao.updateWhInventoryTaskItem(updateItem);
        if (updateLines == 0) {
            logger.info("updateWhInventoryTaskItem to DismissedToReset failed id-->" + taskItem.getId());
        }
        else if (taskItem.getDemandId() != null) {
            WhPickInventoryDemand updateDemand = new WhPickInventoryDemand();
            updateDemand.setId(taskItem.getDemandId());
            updateDemand.setStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
            if (whPickInventoryDemandDao.updateWhPickInventoryDemand(updateDemand) >= 1) {
                WhInventoryTask updateTask = new WhInventoryTask();
                updateTask.setId(taskItem.getTaskId());
                updateTask.setReviewDate(new Timestamp(System.currentTimeMillis()));
                updateTask.setReviewUser(DataContextHolder.getUserId());
                whInventoryTaskDao.updateWhInventoryTask(updateTask);
                SystemLogUtils.PICKINVENTORYDEMAND.log(taskItem.getDemandId(), "审核驳回重置盘点需求",
                        new String[][] { { "历史状态", taskItem.getStatusName() },
                                { "变更状态", PickInventoryDemandStatus.UNGENERATE.getName() } });
            }
            else {
                throw new RuntimeException("审核驳回失败, 更新盘点需求失败!");
            }
        }
        SystemLogUtils.INVENTORYTASKITEM.log(taskItem.getId(), "审核驳回重置盘点需求", new String[][] {
                { "历史状态", taskItem.getStatusName() }, { "变更状态", InventoryTaskStatus.DISCARDED.getName() } });
        return true;
    }

    /**
     * 驳回盘点任务、需求废弃
     */
    public boolean updateDismissedToDiscard(WhInventoryTaskItem taskItem,String message) {
        WhInventoryTaskItem updateItem = new WhInventoryTaskItem();
        updateItem.setId(taskItem.getId());
        updateItem.setStatus(InventoryTaskStatus.DISCARDED.intCode());
        whInventoryTaskItemDao.updateWhInventoryTaskItem(updateItem);
        if (taskItem.getDemandId() != null) {
            WhPickInventoryDemand updateDemand = new WhPickInventoryDemand();
            updateDemand.setId(taskItem.getDemandId());
            updateDemand.setStatus(PickInventoryDemandStatus.DISCARDED.intCode());
            if (whPickInventoryDemandDao.updateWhPickInventoryDemand(updateDemand) >= 1) {
                WhInventoryTask updateTask = new WhInventoryTask();
                updateTask.setId(taskItem.getTaskId());
                updateTask.setReviewDate(new Timestamp(System.currentTimeMillis()));
                updateTask.setReviewUser(DataContextHolder.getUserId());
                whInventoryTaskDao.updateWhInventoryTask(updateTask);
                SystemLogUtils.PICKINVENTORYDEMAND.log(taskItem.getDemandId(),StringUtils.isNotBlank(message)?message:"审核驳回废弃盘点需求",
                        new String[][] { { "历史状态", taskItem.getStatusName() },
                                { "变更状态", PickInventoryDemandStatus.DISCARDED.getName() } });
            }
            else {
                throw new RuntimeException("审核驳回失败, 更新盘点需求失败!");
            }
        }
        SystemLogUtils.INVENTORYTASKITEM.log(taskItem.getId(), StringUtils.isNotBlank(message)?message:"审核驳回废弃盘点需求", new String[][] {
                { "历史状态", taskItem.getStatusName() }, { "变更状态", InventoryTaskStatus.DISCARDED.getName() } });

        return true;
    }


    // 校验是否存在待审核的盘增任务
    public Map<Integer,String> getUnreviewInventoryTask(Set<Integer> stockIds){
        if (CollectionUtils.isEmpty(stockIds))
            return Collections.EMPTY_MAP;
        WhInventoryTaskItemQueryCondition query = new WhInventoryTaskItemQueryCondition();
        query.setStatusStr(InventoryTaskStatus.UNREVIEW.getCode());
        query.setStockIds(new ArrayList<>(stockIds));
        List<WhInventoryTaskItem> whInventoryTaskItems = whInventoryTaskItemDao
                .queryWhInventoryTaskItemDetailList(query, null);
        if (CollectionUtils.isEmpty(whInventoryTaskItems))
            return Collections.EMPTY_MAP;
        return whInventoryTaskItems.stream().filter(w -> w.getDiffQuantity() != null && w.getDiffQuantity()>0)
                .collect(Collectors.toMap(WhInventoryTaskItem::getStockId, WhInventoryTaskItem::getSku, (k1, k2) -> k1));
    }

    @Override
    public CQueryResult queryInventoryReviewList(CQuery<WhInventoryTaskItemQueryCondition> query) {
        CQueryResult res = new CQueryResult<>(0, 0, null);
        WhInventoryTaskItemQueryCondition search = query.getSearch();
        search.setStatusStr(InventoryTaskStatus.COMPLETED.getCode());
        search.setFromInventoryDate(search.getBeginDate());
        search.setEndInventoryDate(search.getEndDate() + " 23:59:59");
        List<WhInventoryTaskItem> whInventoryTaskItems = whInventoryTaskItemDao.queryWhInventoryTaskItemDetailList(search, null);

        if (CollectionUtils.isNotEmpty(whInventoryTaskItems)) {
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> summary = new HashMap<>();

            // 分组数据
            Map<String, List<WhInventoryTaskItem>> groupedData = whInventoryTaskItems.stream()
                    .filter(item -> item.getInventoryDate() != null)
                    .collect(Collectors.groupingBy(m -> DateUtils.dateToString(m.getInventoryDate(), DateUtils.DEFAULT_FORMAT)));

            // 初始化汇总变量
            long totalInventorySkuSum = 0;
            long noDifferenceSkuSum = 0;
            long increaseInventorySkuSum = 0;
            long inventoryLossSkuSum = 0;
            Integer totalPcsSum = 0;
            Integer expandPcsSum = 0;
            Integer lossPcsSum = 0;

            for (Map.Entry<String, List<WhInventoryTaskItem>> entry : groupedData.entrySet()) {
                List<WhInventoryTaskItem> items = entry.getValue();

                // 预先计算需要的结果
                long totalInventorySku = items.size();
                long noDifferenceSku = items.stream().filter(item -> item.getConfirmDiff() != null && item.getConfirmDiff().equals(0)).count();
                long increaseInventorySku = items.stream().filter(item -> item.getConfirmDiff() != null && item.getConfirmDiff() > 0).count();
                long inventoryLossSku = items.stream().filter(item -> item.getConfirmDiff() != null && item.getConfirmDiff() < 0).count();
                int totalPcs = items.stream().filter(item -> item.getConfirmQuantity() != null).mapToInt(WhInventoryTaskItem::getConfirmQuantity).sum();
                int expandPcs = items.stream().filter(item -> item.getConfirmDiff() != null && item.getConfirmDiff() > 0).mapToInt(WhInventoryTaskItem::getConfirmDiff).sum();
                int lossPcs = Math.abs(items.stream().filter(item -> item.getConfirmDiff() != null && item.getConfirmDiff() < 0).mapToInt(WhInventoryTaskItem::getConfirmDiff).sum());

                // 将结果填入 map
                Map<String, Object> map = new HashMap<>();
                map.put("date", entry.getKey());
                map.put("totalInventorySku", totalInventorySku);
                map.put("noDifferenceSku", noDifferenceSku);
                map.put("increaseInventorySku", increaseInventorySku);
                map.put("inventoryLossSku", inventoryLossSku);
                map.put("totalPcs", totalPcs);
                map.put("expandPcs", expandPcs);
                map.put("lossPcs", lossPcs);

                // 更新汇总值
                totalInventorySkuSum += totalInventorySku;
                noDifferenceSkuSum += noDifferenceSku;
                increaseInventorySkuSum += increaseInventorySku;
                inventoryLossSkuSum += inventoryLossSku;
                totalPcsSum += totalPcs;
                expandPcsSum += expandPcs;
                lossPcsSum += lossPcs;

                list.add(map);
            }

            // 构建汇总信息
            summary.put("date", "汇总");
            summary.put("totalInventorySku", totalInventorySkuSum);
            summary.put("noDifferenceSku", noDifferenceSkuSum);
            summary.put("increaseInventorySku", increaseInventorySkuSum);
            summary.put("inventoryLossSku", inventoryLossSkuSum);
            summary.put("totalPcs", totalPcsSum);
            summary.put("expandPcs", expandPcsSum);
            summary.put("lossPcs", lossPcsSum);

            // 分页
            List<Map<String, Object>> pageList = list.stream()
                    .skip((query.getPage() - 1) * query.getSize())
                    .limit(query.getSize())
                    .collect(Collectors.toList());

            pageList.add(summary);
            int totalPages = (int) Math.ceil((double) list.size() / query.getSize());
            res.setTotal(list.size());
            res.setTotalPages(totalPages);
            res.setRows(pageList);
        }
        return res;
    }


    @Override
    public void createInventoryTask(List<String> localList, Integer inventoryNumber) {
        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setLocationNos(localList);
        List<WhStock> whStocks = whStockService.queryWhStocks(query, null);
        if (CollectionUtils.isEmpty(whStocks)) {
            throw new RuntimeException ("生成盘点任务失败,库位不存在!");
        }
        Integer maxLevel = whInventoryTaskDao.queryWhInventoryTaskGradeMax();
        if (maxLevel == null) {
            maxLevel = 0;
        }

        WhInventoryTask task = new WhInventoryTask();
        String taskNoPrefix = CreateTaskNoUtils.createInventoryTaskNo();
        task.setTaskType(InventoryTaskType.ZONE.intCode());
        task.setStatus(InventoryTaskStatus.UNRECEIVED.intCode());
        task.setTaskLevel(InventoryTaskLevel.FIRST.intCode());
        task.setTaskNoPrefix(taskNoPrefix);
        task.setTaskNo(taskNoPrefix + "A");
        task.setGrade(maxLevel + 1);// 新生成默认置顶
        task.setInventoryCount(inventoryNumber);
        whInventoryTaskDao.createWhInventoryTask(task);
        if (task.getId() == null) {
           throw new RuntimeException("盘点任务创建失败!");
        }
        List<WhInventoryTaskItem> taskItems = new ArrayList<>();
        for (WhStock whStock : whStocks) {
            WhInventoryTaskItem taskItem = new WhInventoryTaskItem();
            taskItem.setSku(whStock.getSku());
            taskItem.setStockId(whStock.getId());
            taskItem.setTaskId(task.getId());
            taskItem.setTaskLevel(task.getTaskLevel());
            taskItem.setStatus(InventoryTaskStatus.UNRECEIVED.intCode());
            taskItems.add(taskItem);
        }
        whInventoryTaskItemDao.batchCreateWhInventoryTaskItem(taskItems);
        SystemLogUtils.INVENTORYTASK.log(task.getId(), "生成库区盘点任务-" + task.getTaskLevelName(),
                new String[][]{{"盘点任务号", task.getTaskNo()}, {"SKU数", String.valueOf(taskItems.size())}});
    }

    /**
     * @Description 盘点报表-库存准确率趋势
     * <AUTHOR>
     * @date 2019/9/10 12:19
     * @version 1.0
     */
    @Override
    public Map<String, Object> queryInventoryTaskDiffSkuCount(WhInventoryTaskQueryCondition query) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> list = whInventoryTaskDao.queryInventoryTaskDiffSkuCount(query);
        JSONArray axisData = new JSONArray();
        JSONArray noDiffSkuCountData = new JSONArray();
        JSONArray diffSkuCountData = new JSONArray();
        JSONArray totalSkuCountData = new JSONArray();
        Integer noDiffSkuCount = 0;
        Integer diffSkuCount = 0;
        for (Map<String, Object> map : list) {
            String date = map.get("date") + "";
            String noDiffSkuCountStr = map.get("noDiffSkuCount") + "";
            String diffSkuCountStr = map.get("diffSkuCount") + "";
            axisData.add(date);
            noDiffSkuCountData.add(noDiffSkuCountStr);
            diffSkuCountData.add(diffSkuCountStr);

            Integer noDiffSku = StringUtils.isBlank(noDiffSkuCountStr) ? 0 : Integer.valueOf(noDiffSkuCountStr);
            Integer diffSku = StringUtils.isBlank(diffSkuCountStr) ? 0 : Integer.valueOf(diffSkuCountStr);

            noDiffSkuCount += noDiffSku;
            diffSkuCount += diffSku;

            Integer total = noDiffSku + diffSku;
            if (total > 0) {
                double accuracy = Double.valueOf(noDiffSku) / Double.valueOf(total);
                totalSkuCountData.add((double) Math.round(accuracy * 100 * 100) / 100);
            } else {
                totalSkuCountData.add(0);
            }
        }
        if (axisData.size() > 0) {
            resultMap.put("axisData", axisData);
            resultMap.put("noDiffSkuCountData", noDiffSkuCountData);
            resultMap.put("diffSkuCountData", diffSkuCountData);
            resultMap.put("totalSkuCountData", totalSkuCountData);
        }

        if ((noDiffSkuCount + diffSkuCount) != 0) {
            double accuracy = Double.valueOf(noDiffSkuCount) / Double.valueOf(String.valueOf(noDiffSkuCount + diffSkuCount));
            resultMap.put("currentAccuracy", (double) Math.round(accuracy * 100 * 100) / 100 + "%");
        } else {
            resultMap.put("currentAccuracy", "0%");
        }
        WhInventoryTaskQueryCondition totalQuery = new WhInventoryTaskQueryCondition();
        totalQuery.setReadOnly(true);
        List<Map<String, Object>> totalResult = whInventoryTaskDao
                .queryTotalInventoryTaskDiffSkuCount(totalQuery);
        for (Map<String, Object> map : totalResult) {
            String noDiffSkuCountStr = map.get("noDiffSkuCount") + "";
            String diffSkuCountStr = map.get("diffSkuCount") + "";
            Integer noDiffSku = StringUtils.isBlank(noDiffSkuCountStr) ? 0 : Integer.valueOf(noDiffSkuCountStr);
            Integer diffSku = StringUtils.isBlank(diffSkuCountStr) ? 0 : Integer.valueOf(diffSkuCountStr);

            if ((noDiffSku + diffSku) != 0) {
                double accuracy = Double.valueOf(noDiffSku) / Double.valueOf(String.valueOf(noDiffSku + diffSku));
                resultMap.put("totalAccuracy", (double) Math.round(accuracy * 100 * 100) / 100 + "%");
            } else {
                resultMap.put("totalAccuracy", "0%");
            }

        }
        return resultMap;
    }

    /**
     * @Description 盘点报表-盘点人员统
     * <AUTHOR>
     * @date 2019/9/10 12:19
     * @version 1.0
     */
    @Override
    public List<InventoryTaskViewExport> queryInventoryTaskViewExportByAccount(WhInventoryTaskQueryCondition query) {
        List<Map<String, Object>> listMap = whInventoryTaskDao.queryInventoryTaskViewExportByAccount(query);
        query.setFromInventoryDate(null);
        query.setEndInventoryDate(null);

        WhInventoryTaskQueryCondition query2 = new WhInventoryTaskQueryCondition();
        query2.setReadOnly(true);
        List<Map<String, Object>> listMap2 = whInventoryTaskDao
                .queryInventoryTaskViewExportByAccount(query2);
        List<InventoryTaskViewExport> viewExportList1 = handelViewExport(listMap);
        List<InventoryTaskViewExport> viewExportList2 = handelViewExport(listMap2);
        for (InventoryTaskViewExport viewExport1 : viewExportList1) {
            viewExport1.setInventoryUserName(TaglibUtils.getEmployeeNameByUserId(viewExport1.getInventoryUser()));
            for (InventoryTaskViewExport viewExport2 : viewExportList2) {
                if (viewExport1.getInventoryUser() != null
                        && viewExport1.getInventoryUser().equals(viewExport2.getInventoryUser())) {
                    viewExport1.setTotalTaskCount(viewExport2.getTaskCount());
                    viewExport1.setTotalSkuCount(viewExport2.getSkuCount());
                    viewExport1.setTotalPcsCount(viewExport2.getPcsCount());
                    viewExport1.setTotalAccuracy(viewExport2.getAccuracy());
                }
            }
        }
        return viewExportList1;
    }

    public List<InventoryTaskViewExport> handelViewExport(List<Map<String, Object>> listMap) {
        List<InventoryTaskViewExport> result = new ArrayList<>();
        for (Map<String, Object> map : listMap) {
            if (map.get("inventoryUser") == null) {
                continue;
            }
            Integer inventoryUser = map.get("inventoryUser") == null ? 0 : Integer.valueOf(map.get("inventoryUser").toString());

            Integer taskCount = map.get("taskCount") == null ? 0 : Integer.valueOf(map.get("taskCount").toString());
            Integer skuCount = map.get("skuCount") == null ? 0 : Integer.valueOf(map.get("skuCount").toString());

            Integer pcsCount1 = map.get("pcsCount1") == null ? 0 : Integer.valueOf(map.get("pcsCount1").toString());
            Integer pcsCount2 = map.get("pcsCount2") == null ? 0 : Integer.valueOf(map.get("pcsCount2").toString());
            Integer pcsCount3 = map.get("pcsCount3") == null ? 0 : Integer.valueOf(map.get("pcsCount3").toString());
            Integer pcsCount4 = map.get("pcsCount4") == null ? 0 : Integer.valueOf(map.get("pcsCount4").toString());

            Integer noDiffCount1 = map.get("noDiffCount1") == null ? 0 : Integer.valueOf(map.get("noDiffCount1").toString());
            Integer noDiffCount2 = map.get("noDiffCount2") == null ? 0 : Integer.valueOf(map.get("noDiffCount2").toString());
            Integer noDiffCount3 = map.get("noDiffCount3") == null ? 0 : Integer.valueOf(map.get("noDiffCount3").toString());
            Integer noDiffCount4 = map.get("noDiffCount4") == null ? 0 : Integer.valueOf(map.get("noDiffCount4").toString());
            Integer noDiffCount5 = map.get("noDiffCount5") == null ? 0 : Integer.valueOf(map.get("noDiffCount5").toString());
            Integer noDiffCount6 = map.get("noDiffCount6") == null ? 0 : Integer.valueOf(map.get("noDiffCount6").toString());
            Integer noDiffCount7 = map.get("noDiffCount7") == null ? 0 : Integer.valueOf(map.get("noDiffCount7").toString());

            Integer pcsCount = pcsCount1 + pcsCount2 + pcsCount3 + pcsCount4;
            Integer noDiffCount = noDiffCount1 + noDiffCount2 + noDiffCount3 + noDiffCount4 + noDiffCount5
                    + noDiffCount6 + noDiffCount7;

            InventoryTaskViewExport export = new InventoryTaskViewExport();
            export.setInventoryUser(inventoryUser);
            export.setTaskCount(taskCount);
            export.setSkuCount(skuCount);
            export.setPcsCount(pcsCount);
            if (skuCount != 0) {
                double accuracy = Double.valueOf(noDiffCount) / Double.valueOf(skuCount);
                export.setAccuracy((double) Math.round(accuracy * 100 * 100) / 100);
            } else {
                export.setAccuracy(0.0);
            }
            result.add(export);
        }
        return result;
    }
}