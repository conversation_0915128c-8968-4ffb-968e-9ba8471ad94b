package com.estone.warehouse.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.dao.ApvTrackDao;
import com.estone.apv.dao.WhApvDao;
import com.estone.apv.service.WhApvService;
import com.estone.checkin.utils.GetSkuQcCategoryDescUtil;
import com.estone.checkout.bean.*;
import com.estone.checkout.dao.SmtReturnOrderDao;
import com.estone.checkout.dao.SmtReturnOrderItemDao;
import com.estone.checkout.enums.SmtReturnOrderStatus;
import com.estone.checkout.service.SheinReturnOrderService;
import com.estone.common.util.CommonUtils;
import com.estone.common.util.CreateTaskNoUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.foreign.bean.PushOmsPacData;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuExtend;
import com.estone.sku.bean.WhSkuExtendQueryCondition;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.dao.WhSkuDao;
import com.estone.sku.service.WhSkuExtendService;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.estone.temu.bean.TemuReturnPackage;
import com.estone.temu.bean.TemuReturnPackageItem;
import com.estone.temu.bean.TemuReturnPackageQueryCondition;
import com.estone.temu.enums.TemuReturnStatus;
import com.estone.temu.service.TemuReturnPackageService;
import com.estone.transfer.bean.WhFbaAllocation;
import com.estone.transfer.bean.WhFbaAllocationItem;
import com.estone.transfer.bean.WhFbaAllocationQueryCondition;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.transfer.service.WhFbaAllocationService;
import com.estone.warehouse.bean.*;
import com.estone.warehouse.dao.WhIntermediateReturnDao;
import com.estone.warehouse.enums.*;
import com.estone.warehouse.service.*;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service("whIntermediateReturnService")
@Slf4j
public class WhIntermediateReturnServiceImpl implements WhIntermediateReturnService {
    @Resource
    private WhIntermediateReturnDao whIntermediateReturnDao;
    
    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Resource
    private SmtReturnOrderDao smtReturnOrderDao;

    @Resource
    private SheinReturnOrderService sheinReturnOrderService;

    @Resource
    private TemuReturnPackageService temuReturnPackageService;

    @Resource
    private WhSkuDao whSkuDao;

    @Resource
    private WhSkuExtendService whSkuExtendService;

    @Resource
    private WhApvService whApvService;

    @Resource
    private WhApvDao whApvDao;

    @Resource
    private ApvTrackDao apvTrackDao;

    @Resource
    private AmqMessageService amqMessageService;

    @Resource
    private WhIntermediateReturnApvItemService whIntermediateReturnApvItemService;

    @Resource
    private WhAbroadReturnService whAbroadReturnService;

    @Resource
    private SkuReturnStatisticsService skuReturnStatisticsService;

    @Resource
    private SmtReturnOrderItemDao smtReturnOrderItemDao;

    @Override
    public WhIntermediateReturn getWhIntermediateReturn(Integer id) {
        WhIntermediateReturn whIntermediateReturn = whIntermediateReturnDao.queryWhIntermediateReturn(id);
        return whIntermediateReturn;
    }

    @Override
    public WhIntermediateReturn getWhIntermediateReturnDetail(Integer id) {
        WhIntermediateReturn whIntermediateReturn = whIntermediateReturnDao.queryWhIntermediateReturn(id);
        // 关联查询
        return whIntermediateReturn;
    }

    @Override
    public WhIntermediateReturn queryWhIntermediateReturn(WhIntermediateReturnQueryCondition query) {
        Assert.notNull(query, "query is null!");
        WhIntermediateReturn whIntermediateReturn = whIntermediateReturnDao.queryWhIntermediateReturn(query);
        return whIntermediateReturn;
    }

    @Override
    public List<WhIntermediateReturn> queryAllWhIntermediateReturns() {
        return whIntermediateReturnDao.queryWhIntermediateReturnList();
    }

    @Override
    public List<WhIntermediateReturn> queryWhIntermediateReturns(WhIntermediateReturnQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = whIntermediateReturnDao.queryWhIntermediateReturnCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<WhIntermediateReturn>();
            }
        }
        List<WhIntermediateReturn> whIntermediateReturns = whIntermediateReturnDao.queryWhIntermediateReturnList(query, pager);
        return whIntermediateReturns;
    }

    @Override
    public void createWhIntermediateReturn(WhIntermediateReturn whIntermediateReturn) {
        try {
            whIntermediateReturnDao.createWhIntermediateReturn(whIntermediateReturn);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateWhIntermediateReturn(List<WhIntermediateReturn> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whIntermediateReturnDao.batchCreateWhIntermediateReturn(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteWhIntermediateReturn(Integer id) {
        try {
            whIntermediateReturnDao.deleteWhIntermediateReturn(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateWhIntermediateReturn(WhIntermediateReturn whIntermediateReturn) {
        try {
            whIntermediateReturnDao.updateWhIntermediateReturn(whIntermediateReturn);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateWhIntermediateReturn(List<WhIntermediateReturn> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whIntermediateReturnDao.batchUpdateWhIntermediateReturn(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public List<WhIntermediateReturn> queryWhIntermediateReturnAndItemList(WhIntermediateReturnQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = whIntermediateReturnDao.queryWhIntermediateReturnAndItemCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<WhIntermediateReturn>();
            }
        }
        List<WhIntermediateReturn> whIntermediateReturns = whIntermediateReturnDao.queryWhIntermediateReturnAndItemList(query, pager);
        return whIntermediateReturns;
    }

    @Override
    public ApiResult<WhIntermediateReturnDto> scanIntermediateReturn(String trackingNo,String orderNo) {
        ApiResult<WhIntermediateReturnDto> result=new ApiResult<>();
        result.setSuccess(true);
        if (StringUtils.isBlank(trackingNo)) {
            result.setErrorMsg("快递单号不能为空");
            return result;
        }
        WhIntermediateReturnDto returnDto=new WhIntermediateReturnDto();
        returnDto.setTrackingNo(trackingNo);
        List<WhIntermediateReturnApvItem> skuItemsSubmit = returnDto.getSkuItemsSubmit();
        List<WhIntermediateSkuReturnDto> skuReturnList = returnDto.getSkuReturnList();
        //拆包包裹已退件数量map
        Map<String, Integer> apvItemMap=new HashMap<>();

        //扫描JIT、仓发快递单号
        SmtReturnOrderQueryCondition query = new SmtReturnOrderQueryCondition();
        query.setExpressNo(trackingNo);
        List<SmtReturnOrder> smtReturnOrders = smtReturnOrderDao.querySmtReturnOrderAndItems(query, null);
        if (CollectionUtils.isNotEmpty(smtReturnOrders) && smtReturnOrders.get(0).getExpressNo().contains(",")) {
            List<String> expressNoList = CommonUtils.splitList(smtReturnOrders.get(0).getExpressNo(), ",");
            apvItemMap=queryFbaReturnSkuQuery(expressNoList, trackingNo);
        }
        SheinReturnOrderQueryCondition sheinQueryCondition = new SheinReturnOrderQueryCondition();
        sheinQueryCondition.setExpressNo(trackingNo);
        List<SheinReturnOrder> sheinReturnOrders = sheinReturnOrderService.querySheinReturnOrderAndItems(sheinQueryCondition, null);
        if (CollectionUtils.isNotEmpty(sheinReturnOrders) && sheinReturnOrders.get(0).getExpressNo().contains(",")) {
            List<String> expressNoList = CommonUtils.splitList(sheinReturnOrders.get(0).getExpressNo(), ",");
            apvItemMap=queryFbaReturnSkuQuery(expressNoList,trackingNo);
        }


        WhIntermediateReturnQueryCondition whIntermediateReturnQueryCondition = new WhIntermediateReturnQueryCondition();
        whIntermediateReturnQueryCondition.setApvNo(trackingNo);
        List<WhIntermediateReturn> intermediateReturns = queryWhIntermediateReturnAndItemList(whIntermediateReturnQueryCondition, null);

        if (CollectionUtils.isEmpty(intermediateReturns) && StringUtils.isNotBlank(orderNo)) {
            whIntermediateReturnQueryCondition = new WhIntermediateReturnQueryCondition();
            whIntermediateReturnQueryCondition.setOrderNo(orderNo);
            intermediateReturns = queryWhIntermediateReturnAndItemList(whIntermediateReturnQueryCondition, null);
        }
        if (CollectionUtils.isNotEmpty(intermediateReturns)) {
            if (!WhBatchReturnStatus.SPLIT.intCode().equals( intermediateReturns.get(0).getStatus())) {
                result.setErrorMsg("当前包裹已录入不能扫描,对应批次号:"+intermediateReturns.get(0).getOrderNo());
                queryFbaReturnSku(returnDto,trackingNo);
                result.setResult(returnDto);
                return result;
            }
            skuItemsSubmit.addAll(intermediateReturns.get(0).getIntermediateReturnApvItems());
            returnDto.setOrderNo(intermediateReturns.get(0).getOrderNo());
        }else{
            returnDto.setOrderNo(CreateTaskNoUtils.createWhIntermediateReturnOrderNo());
        }

        if (CollectionUtils.isNotEmpty(smtReturnOrders)) {
            Map<Integer, String> smtReturnAccountMap = this.getSmtReturnAccountMap(smtReturnOrders);
            SmtReturnOrder smtReturnOrder = mergeSmtReturn(smtReturnOrders);
            returnDto.setId(smtReturnOrder.getId());
            String accountNumber = null;
            if ("仓发".equals(smtReturnOrder.getBizType())) {
                returnDto.setDeliveryType( BatchReturnType.TRANSIT.getName());
                returnDto.setWarehouse(BatchReturnType.FBA.getName());
            }
            if ("JIT".equals(smtReturnOrder.getBizType())) {
                returnDto.setDeliveryType(BatchReturnType.LOCAL_JIT.getName());
                returnDto.setWarehouse(BatchReturnType.LOCAL.getName());
                accountNumber = BatchReturnType.LOCAL.getName();
            }
            List<SmtReturnOrderItem> itemList = Optional.ofNullable(smtReturnOrder.getMergeItemSkuList()).orElse(new ArrayList<>());
            for (SmtReturnOrderItem item : itemList) {
                WhIntermediateSkuReturnDto skuReturnDto = new WhIntermediateSkuReturnDto();
                skuReturnDto.setSku(item.getBarcode());
                skuReturnDto.setAccount(smtReturnAccountMap.get(item.getReturnId()));
                if (StringUtils.isNotBlank(accountNumber)){
                    skuReturnDto.setAccount(accountNumber);
                }
                Integer returnQuantity = apvItemMap.get(item.getBarcode());
                Integer integer = Optional.ofNullable(item.getReturnQuantity()).orElse(0);
                if (returnQuantity != null && returnQuantity > 0) {
                    int quantity = integer - returnQuantity > 0 ? integer - returnQuantity : 0;
                    skuReturnDto.setReturnQuantity(quantity);
                    if (quantity<=0){
                        skuReturnDto.setSubmitComplete(true);
                    }
                }else{
                    skuReturnDto.setReturnQuantity(integer);
                }
                skuReturnDto.setSmtId(item.getReturnId());
                skuReturnList.add(skuReturnDto);
            }
            queryFbaReturnSku(returnDto,trackingNo);
            return ApiResult.newSuccess(returnDto);
        }

        //shein退仓
        if (CollectionUtils.isNotEmpty(sheinReturnOrders)) {
            Map<Integer, String> sheinReturnAccountMap = this.getSheinReturnAccountMap(sheinReturnOrders);
            SheinReturnOrder sheinReturnOrder = mergeShineReturn(sheinReturnOrders);
            returnDto.setId(sheinReturnOrder.getId());
            returnDto.setDeliveryType(BatchReturnType.SHEIN.getName());
            returnDto.setWarehouse(BatchReturnType.FBA.getName());
            List<SheinReturnOrderItem> itemList = Optional.ofNullable(sheinReturnOrder.getMergeItemSkuList()).orElse(new ArrayList<>());
            for (SheinReturnOrderItem item : itemList) {
                WhIntermediateSkuReturnDto skuReturnDto = new WhIntermediateSkuReturnDto();
                skuReturnDto.setSku(item.getSku());
                skuReturnDto.setAccount(sheinReturnAccountMap.get(item.getReturnId()));
                Integer returnQuantity = apvItemMap.get(item.getSku());
                Integer integer = Optional.ofNullable(item.getReturnQuantity()).orElse(0);
                if (returnQuantity != null && returnQuantity > 0) {
                    int quantity = Math.max(integer - returnQuantity, 0);
                    skuReturnDto.setReturnQuantity(quantity);
                    if (quantity<=0){
                        skuReturnDto.setSubmitComplete(true);
                    }
                }else{
                    skuReturnDto.setReturnQuantity(integer);
                }
                skuReturnDto.setSmtId(item.getReturnId());
                skuReturnList.add(skuReturnDto);
            }
            queryFbaReturnSku(returnDto,trackingNo);
            return ApiResult.newSuccess(returnDto);


        }



        //扫描中转仓
        WhFbaAllocationQueryCondition fbaQueryCondition =  new WhFbaAllocationQueryCondition();
        fbaQueryCondition.setFbaNo(trackingNo);
        List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(fbaQueryCondition, null);
        if (CollectionUtils.isNotEmpty(whFbaAllocations)) {
            WhFbaAllocation whFbaAllocation = whFbaAllocations.get(0);
            List<Integer> statusList = Arrays.asList(AsnPrepareStatus.DELIVER.intCode(), AsnPrepareStatus.LOADED.intCode());
            if (!statusList.contains(whFbaAllocation.getStatus())) {
                result.setErrorMsg("当前中转单状态不是已交运或已装车，不能继续扫描");
                queryFbaReturnSku(returnDto,trackingNo);
                result.setResult(returnDto);
                return result;
            }
            returnDto.setId(whFbaAllocation.getId());
            returnDto.setDeliveryType(BatchReturnType.FBA.getName());
            returnDto.setWarehouse(BatchReturnType.FBA.getName());
            Map<String, Integer> map = whFbaAllocation.getItems()
                    .stream()
                    .collect(Collectors.toMap(WhFbaAllocationItem::getProductSku,
                            i -> Optional.ofNullable(i.getGridQuantity()).orElse(Optional.ofNullable(i.getPickQuantity()).orElse(0)),
                            Integer::sum));
            map.forEach((sku, value)-> {
                WhIntermediateSkuReturnDto skuReturnDto = new WhIntermediateSkuReturnDto();
                skuReturnDto.setSku(sku);
                skuReturnDto.setAccount(whFbaAllocation.getAccountNumber());
                skuReturnDto.setReturnQuantity(value);
                skuReturnList.add(skuReturnDto);
            });
            queryFbaReturnSku(returnDto,whFbaAllocation.getFbaNo());
            return ApiResult.newSuccess(returnDto);
        }

         //扫描temu订单号
        TemuReturnPackageQueryCondition temuQuery = new TemuReturnPackageQueryCondition();
        temuQuery.setPackageNo(trackingNo);
        List<TemuReturnPackage> orderList = temuReturnPackageService.queryTemuReturnPackages(temuQuery, null);
        if (CollectionUtils.isNotEmpty(orderList)) {
            returnDto.setDeliveryType(BatchReturnType.TEMU.getName());
            returnDto.setWarehouse(BatchReturnType.FBA.getName());
            TemuReturnPackage temuReturnPackage = orderList.get(0);
            returnDto.setId(temuReturnPackage.getId());

            Map<String, Integer> skuMap = temuReturnPackage.getItemList().stream().collect(Collectors.toMap(TemuReturnPackageItem::getSku, i -> Optional.ofNullable(i.getQuantity()).orElse(0), Integer::sum));
            if (MapUtils.isNotEmpty(skuMap)) {
                skuMap.forEach((sku, quantity) -> {
                    WhIntermediateSkuReturnDto skuReturnDto = new WhIntermediateSkuReturnDto();
                    skuReturnDto.setSku(sku);
                    skuReturnDto.setAccount(temuReturnPackage.getAccountNumber());
                    skuReturnDto.setReturnQuantity(quantity);
                    skuReturnList.add(skuReturnDto);
                });
            }
            queryFbaReturnSku(returnDto,temuReturnPackage.getPackageNo());
            return ApiResult.newSuccess(returnDto);
        }
        queryFbaReturnSku(returnDto,trackingNo);
        result.setResult(returnDto);
        if (CollectionUtils.isNotEmpty(skuItemsSubmit)) {
            result.setExceptionCode("1");
            result.setErrorMsg("包裹号未匹配，请在下方的SKU输入框中扫描或手动输入唯一码/SKU生成条目继续进行");
            return result;
        }
        result.setExceptionCode("1");
        result.setErrorMsg("包裹号未匹配，请在下方的SKU输入框中扫描或手动输入唯一码/SKU生成条目继续进行");
        return result;
    }

    @Override
    public ApiResult<WhIntermediateReturnDto> scanUnmatchedSku(String sku, String trackingNo, String orderNo) {
        ApiResult<WhIntermediateReturnDto> result = new ApiResult<>();
        result.setSuccess(true);
        if (StringUtils.isBlank(sku)){
            result.setErrorMsg("sku/唯一码不能为空");
            return result;
        }
        if (StringUtils.isBlank(trackingNo)) {
            result.setErrorMsg("快递单号不能为空");
            return result;
        }
        WhIntermediateReturnDto returnDto=new WhIntermediateReturnDto();
        List<WhIntermediateReturnApvItem> skuItemsSubmit = returnDto.getSkuItemsSubmit();
        List<WhIntermediateSkuReturnDto> skuReturnList = returnDto.getSkuReturnList();
        returnDto.setOrderNo(orderNo);
        List<WhIntermediateReturn> intermediateReturns = null;

        if (StringUtils.isNotBlank(orderNo)) {
            WhIntermediateReturnQueryCondition whIntermediateReturnQueryCondition = new WhIntermediateReturnQueryCondition();
            whIntermediateReturnQueryCondition.setOrderNo(orderNo);
            intermediateReturns = queryWhIntermediateReturnAndItemList(whIntermediateReturnQueryCondition, null);
        }
        if (CollectionUtils.isNotEmpty(intermediateReturns)) {
            if (!WhBatchReturnStatus.SPLIT.intCode().equals( intermediateReturns.get(0).getStatus())) {
                result.setErrorMsg("当前包裹已录入不能扫描,对应批次号:"+intermediateReturns.get(0).getOrderNo());
                queryFbaReturnSku(returnDto,trackingNo);
                result.setResult(returnDto);
                return result;
            }
            returnDto.setOrderNo(intermediateReturns.get(0).getOrderNo());
            List<WhIntermediateReturnApvItem> intermediateReturnApvItems = intermediateReturns.get(0).getIntermediateReturnApvItems();
            skuItemsSubmit.addAll(intermediateReturnApvItems);
            // 已扫描的需要填充到return对象中进行回显操作
            for (WhIntermediateReturnApvItem intermediateReturnApvItem : intermediateReturnApvItems) {
                if (!StringUtils.equals(intermediateReturnApvItem.getApvNo(),trackingNo)){
                    continue;
                }
                WhIntermediateSkuReturnDto skuReturnDto = new WhIntermediateSkuReturnDto();
                skuReturnDto.setSku(intermediateReturnApvItem.getSku());
                skuReturnDto.setAccount(intermediateReturnApvItem.getAccount());
                skuReturnDto.setSortId(intermediateReturnApvItem.getId());
                skuReturnList.add(skuReturnDto);
            }
            if (CollectionUtils.isNotEmpty(intermediateReturnApvItems)){
                boolean isContains = intermediateReturnApvItems.stream().anyMatch(i -> i.getSku().equals(sku) && i.getApvNo().equals(trackingNo));
                if (isContains){
                    result.setErrorMsg("当前包裹已录入对应sku，不能再次扫描提交!");
                    queryFbaReturnSku(returnDto,trackingNo);
                    result.setResult(returnDto);
                    return result;
                }
            }
        }
        WhSkuQueryCondition queryCondition=new WhSkuQueryCondition();
        queryCondition.setSku(sku);
        List<WhSku> whSkuList = whSkuDao.queryWhSkuList(queryCondition, null);
        if (CollectionUtils.isNotEmpty(whSkuList)){
            WhIntermediateSkuReturnDto skuReturnDto = new WhIntermediateSkuReturnDto();
            skuReturnDto.setSku(sku);
            skuReturnDto.setAccount("未匹配");
            skuReturnList.add(skuReturnDto);
        }else{
            result.setErrorMsg("未匹配到sku");
        }
        queryFbaReturnSku(returnDto, trackingNo);
        result.setResult(returnDto);
        return result;
    }

    @Override
    public List<String> getUnmatchedReturnTypeName() {
        List<String> unmatchedReturnTypeName = Arrays.asList(BatchReturnType.TRANSIT.getName(), BatchReturnType.LOCAL_JIT.getName(),
                BatchReturnType.SHEIN.getName(),BatchReturnType.TEMU.getName());
        return unmatchedReturnTypeName;
    }

    /**
     * 获取SMT退货单账号
     * @param smtReturnOrderList
     * @return key -> 退货单ID value -> 账号
     */
    private Map<Integer,String> getSmtReturnAccountMap(List<SmtReturnOrder> smtReturnOrderList){
        if (CollectionUtils.isEmpty(smtReturnOrderList)){
            return new HashMap<>();
        }
        return smtReturnOrderList
                .stream()
                .collect(Collectors.toMap(SmtReturnOrder::getId, SmtReturnOrder::getAccountNumber));
    }

    /**
     * 获取Shein退货单账号
     * @param sheinReturnOrderList
     * @return key -> 退货单ID value -> 账号
     */
    private Map<Integer,String> getSheinReturnAccountMap(List<SheinReturnOrder> sheinReturnOrderList){
        if (CollectionUtils.isEmpty(sheinReturnOrderList)){
            return new HashMap<>();
        }
        return sheinReturnOrderList
                .stream()
                .collect(Collectors.toMap(SheinReturnOrder::getId, SheinReturnOrder::getAccountNumber));
    }


    //获取已退仓中转仓数据
    public Map<String, Integer> queryFbaReturnSkuQuery(List<String> expressNoList,String trackingNo) {
        Map<String, Integer> apvItemMap=new HashMap<>();
        expressNoList=Optional.of(expressNoList).orElse(new ArrayList<>()).stream().filter(e -> !e.equals(trackingNo)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(expressNoList)) {
            return apvItemMap;
        }
        WhIntermediateReturnQueryCondition whIntermediateReturnQueryCondition = new WhIntermediateReturnQueryCondition();
        whIntermediateReturnQueryCondition.setApvNo(String.join(",", expressNoList));
        List<WhIntermediateReturn> intermediateReturns = queryWhIntermediateReturnAndItemList(whIntermediateReturnQueryCondition, null);
        List<String> finalExpressNoList = expressNoList;
        return intermediateReturns
                .stream()
                .map(WhIntermediateReturn::getIntermediateReturnApvItems)
                .flatMap(Collection::stream)
                .filter(e -> finalExpressNoList.contains(e.getApvNo()))
                .collect(Collectors.toMap(WhIntermediateReturnApvItem::getSku,
                        i -> Optional.ofNullable(i.getGoodQuantity()).orElse(0)+
                                Optional.ofNullable(i.getBadQuantity()).orElse(0)
                        , Integer::sum));
    }


    @Override
    public ApiResult<?> doSubmitIntermediateSku(WhIntermediateReturnRequest request) {
        boolean isUnmatchedPackage = Objects.equals("未匹配", request.getAccount());
        if(!isUnmatchedPackage && (request.getId()==null || request.getReturnQuantity()==null)){
            return ApiResult.newError("非未匹配包裹！参数不全,请联系开发人员");
        }
        if (StringUtils.isBlank(request.getOrderNo()) || StringUtils.isBlank(request.getDeliveryType()) || StringUtils.isBlank(request.getSku())
                || request.getBadQuantity()==null || request.getGoodQuantity()==null ||  StringUtils.isBlank(request.getTrackingNo())
                || StringUtils.isBlank(request.getAccount())){
            log.info("参数不全,请联系开发人员，dto="+JSON.toJSONString(request));
            return ApiResult.newError("参数不全,请联系开发人员");
        }
        WhIntermediateReturnQueryCondition whIntermediateReturnQueryCondition = new WhIntermediateReturnQueryCondition();
        whIntermediateReturnQueryCondition.setOrderNo(request.getOrderNo());
        List<WhIntermediateReturn> intermediateReturns = queryWhIntermediateReturnAndItemList(whIntermediateReturnQueryCondition, null);
        WhIntermediateReturn intermediateReturn = new WhIntermediateReturn();
        String apvNo = request.getTrackingNo();
        if(CollectionUtils.isNotEmpty(intermediateReturns)){
            intermediateReturn= intermediateReturns.get(0);
            if (WhBatchReturnStatus.GRID.intCode().equals(intermediateReturn.getStatus())) {
                return ApiResult.newError("当前批次播种中状态，不能提交");
            }
            boolean anyMatch = intermediateReturn.getIntermediateReturnApvItems().stream().anyMatch(item -> !WhBatchReturnSkuStatus.SPLIT.intCode().equals(item.getStatus()) && request.getSku().equals(item.getSku()) && apvNo.equals(item.getApvNo()));
            if (anyMatch) {
                return ApiResult.newError("该包裹已提交过，请勿重复提交");
            }

        }

        if (intermediateReturn.getId()==null){
            intermediateReturn.setOrderNo(request.getOrderNo());
            intermediateReturn.setStatus(WhBatchReturnStatus.SPLIT.intCode());
            whIntermediateReturnDao.createWhIntermediateReturn(intermediateReturn);
        }

        String deliveryType = request.getDeliveryType();
        Integer smtReturnId = request.getSmtId();
        Map<String, Integer> map = new HashMap<>();

        Boolean isSplit=false;
        //拆包包裹已退件数量map
        Map<String, Integer> apvItemMap=new HashMap<>();

        if(isUnmatchedPackage){
            int returnQuantity = Optional.ofNullable(request.getBadQuantity()).orElse(0) + Optional.ofNullable(request.getGoodQuantity()).orElse(0);
            request.setReturnQuantity(returnQuantity);
            map.put(request.getSku(), returnQuantity);
        }

        //中转仓单据 推送退仓信息到oms
        if (BatchReturnType.FBA.getName().equals(deliveryType) && !isUnmatchedPackage) {
             WhFbaAllocationQueryCondition fbaQueryCondition =  new WhFbaAllocationQueryCondition();
             fbaQueryCondition.setId(request.getId());
             List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(fbaQueryCondition, null);
            if (CollectionUtils.isEmpty(whFbaAllocations)) {
                throw new RuntimeException("未找到中转仓发货单");
            }
            WhFbaAllocation whFbaAllocation = whFbaAllocations.get(0);
            smtReturnId=whFbaAllocation.getId();
            //推送退仓信息到OMS
            if (whFbaAllocation.getIsReturn()==null || !whFbaAllocation.getIsReturn()) {
                WhFbaAllocation update = new WhFbaAllocation();
                update.setId(whFbaAllocation.getId());
                update.setIsReturn(true);
                whFbaAllocationService.updateWhFbaAllocation(update);
                List<PushOmsPacData> omsPacDataList = new ArrayList<>();
                omsPacDataList.add(new PushOmsPacData(whFbaAllocation.getTrackingNumber(), whFbaAllocation.getShipmentId(),true));
                //推送订单
                whFbaAllocationService.pushTransferReturnOrder(omsPacDataList);
            }
            whFbaAllocation.buildGroupItems();
            map = Optional.ofNullable(whFbaAllocation.getGroupItems()).orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toMap(WhFbaAllocationItem::getProductSku, i -> Optional.ofNullable(i.getGridQuantity()).orElse(i.getPickQuantity()), Integer::sum));
        }

        //本地仓JIT/中转仓仓发数据 标记退仓 推送oms
        List<String> returnTypeList = Arrays.asList(BatchReturnType.TRANSIT.getName(), BatchReturnType.LOCAL_JIT.getName());
        if (returnTypeList.contains(deliveryType) && !isUnmatchedPackage) {
            SmtReturnOrderQueryCondition smtQueryCondition = new SmtReturnOrderQueryCondition();
            smtQueryCondition.setId(smtReturnId);
            List<SmtReturnOrder> smtReturnOrders = smtReturnOrderDao.querySmtReturnOrderAndItems(smtQueryCondition, null);
            if (CollectionUtils.isEmpty(smtReturnOrders)) {
                throw new RuntimeException("未找到中转仓["+deliveryType+"]发货单");
            }
            SmtReturnOrder smtReturnOrder = mergeSmtReturn(smtReturnOrders);


            if (smtReturnOrder!=null && smtReturnOrder.getExpressNo().contains(",")) {
                List<String> expressNoList = CommonUtils.splitList(smtReturnOrders.get(0).getExpressNo(), ",");
                if (CollectionUtils.isNotEmpty(expressNoList)) {
                    isSplit=true;
                    apvItemMap = queryFbaReturnSkuQuery(expressNoList, apvNo);
                }
            }

            List<String> trackingNumberList = smtReturnOrder.getItemList()
                    .stream()
                    .filter(item ->StringUtils.isNotBlank(request.getSku()) && item.getReturnId()!=null && request.getSku().equals(item.getBarcode()) && item.getReturnId().equals(request.getSmtId()))
                    .map(SmtReturnOrderItem::getOriginOutboundNo)
                    .distinct()
                    .collect(Collectors.toList());
            //推送退仓信息到OMS
            if (deliveryType.equals(BatchReturnType.LOCAL_JIT.getName()) && CollectionUtils.isNotEmpty(trackingNumberList)){
                WhApvQueryCondition queryCondition=new WhApvQueryCondition();
                queryCondition.setTrackingNumber(StringUtils.join(trackingNumberList, ","));
                List<WhApv> whApvList = whApvService.queryWhApvAndItemList(queryCondition, null);
                List<PushOmsPacData> omsPacDataList=new ArrayList<>();
                List<String> addApvNos=new ArrayList<>();
                for (WhApv whApv : whApvList) {
                    boolean anyMatch = whApv.getWhApvItems().stream().anyMatch(item -> request.getSku().equals(item.getSku()));
                    if (!anyMatch) {
                        continue;
                    }
                    if (whApv.getSignOutTreasury()!=null && whApv.getSignOutTreasury()) {
                        continue;
                    }
                    addApvNos.add(whApv.getApvNo());
                    String salesRecordNumber = whApv.getSalesRecordNumber();
                    if (StringUtils.isNotBlank(salesRecordNumber) && salesRecordNumber.startsWith("NDD")) {
                        salesRecordNumber = salesRecordNumber.substring(3);
                    }
                    omsPacDataList.add(new PushOmsPacData(whApv.getTrackingNumber(),salesRecordNumber,true));
                }
                whApvDao.updateBatchReturnApvTag(addApvNos, 1);
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                // 设置退货时间
                apvTrackDao.updateOverseaReturnTimeBatch(addApvNos,timestamp);
                whFbaAllocationService.pushTransferReturnOrder(omsPacDataList);

                //创建sku退仓标签记录
                skuReturnStatisticsService.createSkuReturnStatisticList(addApvNos,timestamp);
            }

            map =smtReturnOrder.getMergeItemSkuList().stream()
                    .filter(item -> item.getReturnId()!=null && item.getReturnId().equals(request.getSmtId()))
                    .collect(Collectors.toMap(SmtReturnOrderItem::getBarcode,i->Optional.ofNullable(i.getReturnQuantity()).orElse(0), Integer::sum));

            if (SmtReturnOrderStatus.PENDING.intCode().equals(smtReturnOrder.getStatus())){
                SmtReturnOrder updateOrder = new SmtReturnOrder();
                updateOrder.setId(smtReturnOrder.getId());
                updateOrder.setSplitTime(new Timestamp(System.currentTimeMillis()));
                updateOrder.setStatus(SmtReturnOrderStatus.UNPACKED.intCode());
                smtReturnOrderDao.updateSmtReturnOrder(updateOrder);
            }
            // 分配退货单已扫描良品不良品数量
            this.alloSmtReceivedQty(request, smtReturnOrder.getId());
        }
        //SHEIN
        if (deliveryType.equals(BatchReturnType.SHEIN.getName()) && !isUnmatchedPackage){
            SheinReturnOrderQueryCondition sheinQueryCondition = new SheinReturnOrderQueryCondition();
            sheinQueryCondition.setId(smtReturnId);
            List<SheinReturnOrder> shinReturnOrders = sheinReturnOrderService.querySheinReturnOrderAndItems(sheinQueryCondition, null);
            if (CollectionUtils.isEmpty(shinReturnOrders)) {
                throw new RuntimeException("未找到中转仓["+deliveryType+"]发货单");
            }
            SheinReturnOrder sheinReturnOrder = mergeShineReturn(shinReturnOrders);
            if (sheinReturnOrder!=null && sheinReturnOrder.getExpressNo().contains(",")) {
                List<String> expressNoList = CommonUtils.splitList(sheinReturnOrder.getExpressNo(), ",");
                if (CollectionUtils.isNotEmpty(expressNoList)) {
                    isSplit=true;
                    apvItemMap = queryFbaReturnSkuQuery(expressNoList, apvNo);
                }
            }

            List<String> shipmentIdList = sheinReturnOrder.getItemList()
                    .stream()
                    .filter(item ->StringUtils.isNotBlank(request.getSku()) && item.getReturnId()!=null && request.getSku().equals(item.getSku()) && item.getReturnId().equals(request.getSmtId()))
                    .map(SheinReturnOrderItem::getOrderNo)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(shipmentIdList)) {
                //推送退仓信息到OMS
                WhFbaAllocationQueryCondition fbaQueryCondition = new WhFbaAllocationQueryCondition();
                fbaQueryCondition.setShipmentIdList(shipmentIdList);
                List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(fbaQueryCondition, null);
                List<PushOmsPacData> omsPacDataList = new ArrayList<>();
                List<WhFbaAllocation> updateList = new ArrayList<>();
                for (WhFbaAllocation whFbaAllocation : whFbaAllocations) {
                    WhFbaAllocation update = new WhFbaAllocation();
                    update.setId(whFbaAllocation.getId());
                    update.setIsReturn(true);
                    updateList.add(update);
                    omsPacDataList.add(new PushOmsPacData(whFbaAllocation.getTrackingNumber(), whFbaAllocation.getShipmentId(),true));
                }

                if (CollectionUtils.isNotEmpty(updateList)) {
                    whFbaAllocationService.batchUpdateWhFbaAllocation(updateList);
                    //推送订单
                    whFbaAllocationService.pushTransferReturnOrder(omsPacDataList);
                }
            }

            map =sheinReturnOrder.getMergeItemSkuList().stream()
                    .filter(item -> item.getReturnId()!=null && item.getReturnId().equals(request.getSmtId()))
                    .collect(Collectors.toMap(SheinReturnOrderItem::getSku,i->Optional.ofNullable(i.getReturnQuantity()).orElse(0), Integer::sum));

            if (SmtReturnOrderStatus.PENDING.intCode().equals(sheinReturnOrder.getStatus())){
                SheinReturnOrder updateOrder = new SheinReturnOrder();
                updateOrder.setId(sheinReturnOrder.getId());
                updateOrder.setHandleDate(new Timestamp(System.currentTimeMillis()));
                updateOrder.setStatus(SmtReturnOrderStatus.UNPACKED.intCode());
                sheinReturnOrderService.updateSheinReturnOrder(updateOrder);
            }
        }



        //拼多多标记退仓 推送oms
        if (deliveryType.equals(BatchReturnType.TEMU.getName()) && !isUnmatchedPackage){
            TemuReturnPackageQueryCondition query = new TemuReturnPackageQueryCondition();
            query.setId(request.getId());
            List<TemuReturnPackage> orderList = temuReturnPackageService.queryTemuReturnPackages(query, null);
            if (CollectionUtils.isNotEmpty(orderList)) {
                TemuReturnPackage temuReturnPackage = orderList.get(0);
                smtReturnId = temuReturnPackage.getId();
                List<String> statusList = Arrays.asList(TemuReturnStatus.WAIT_RECEIVE.getName(), TemuReturnStatus.OUT_OF_STORAGE.getName());
                if (statusList.contains(temuReturnPackage.getPackageStatus())) {
                    TemuReturnPackage updateOrder = new TemuReturnPackage();
                    updateOrder.setId(temuReturnPackage.getId());
                    updateOrder.setPackageStatus(TemuReturnStatus.WMS_RETURNED.getName());
                    updateOrder.setWmsReturnTime(new Timestamp(System.currentTimeMillis()));
                    temuReturnPackageService.updateTemuReturnPackage(updateOrder);
                    temuReturnPackage.setPushType(1);
                    amqMessageService.createAmqMessage(AssembleMessageDataUtils.pushOmsTemuReturnInfo(temuReturnPackage));
                }

                map =temuReturnPackage.getItemList().stream()
                        .collect(Collectors.toMap(TemuReturnPackageItem::getSku,i->Optional.ofNullable(i.getQuantity()).orElse(0), Integer::sum));

            }
        }
        //已有明细中转仓明细
        Integer finalSmtReturnId = smtReturnId;
        Map<String, WhIntermediateReturnApvItem> returnApvMap = intermediateReturn.getIntermediateReturnApvItems()
                .stream()
                .filter(i -> {
                    if(Objects.nonNull(finalSmtReturnId)) {
                        return Objects.equals(finalSmtReturnId, i.getSmtReturnId());
                    }
                    return StringUtils.equals(i.getApvNo(), apvNo);
                })
                .collect(Collectors.toMap(WhIntermediateReturnApvItem::getSku, i -> i, (v1, v2) -> v1));
        WhIntermediateReturn finalIntermediateReturn = intermediateReturn;

        List<WhIntermediateReturnApvItem> createItemList = new ArrayList<>();
        if (MapUtils.isEmpty(map)) {
           throw new RuntimeException("没有可处理的明细,请联系开发人员!");
        }
        Map<String, Integer> finalApvItemMap = apvItemMap;
        Boolean finalIsSplit = isSplit;
        map.forEach((sku, returnQuantity )->{
            Integer integer = finalApvItemMap.get(sku);
            if (integer!=null && integer>0) {
                returnQuantity-=integer;
                request.setReturnQuantity(returnQuantity);
            }

            WhIntermediateReturnApvItem whIntermediateReturnApvItem = returnApvMap.get(sku);
            WhIntermediateReturnApvItem crateItem = new WhIntermediateReturnApvItem();
            crateItem.setIntermediateReturnId(finalIntermediateReturn.getId());
            crateItem.setSmtReturnId(finalSmtReturnId);
            crateItem.setApvNo(apvNo);
            crateItem.setSku(sku);
            crateItem.setAccount(request.getAccount());
            crateItem.setWarehouseId(BatchReturnType.getCodeByName(deliveryType));
            //批次号不存在明细并且不是当前提交的sku
            if (whIntermediateReturnApvItem==null && !sku.equals(request.getSku())){
                crateItem.setStatus(WhBatchReturnSkuStatus.SPLIT.intCode());

                //是拆包包裹，并且还没有退件或在其它批次退件
                if (finalIsSplit && (integer==null || integer<=0)){
                    crateItem.setReturnQuantity(0);
                }else{
                    crateItem.setReturnQuantity(returnQuantity);
                }
                createItemList.add(crateItem);

            }

            //批次号不存在明细并且是当前提交的sku
            if (whIntermediateReturnApvItem==null && sku.equals(request.getSku())){
                crateItem.setStatus(WhBatchReturnSkuStatus.WAITING_GRID.intCode());
                crateItem.setReturnQuantity(request.getReturnQuantity());
                crateItem.setGoodQuantity(request.getGoodQuantity());
                crateItem.setBadQuantity(request.getBadQuantity());
                crateItem.setShortageQuantity( Optional.ofNullable(request.getReturnQuantity()).orElse(0)-Optional.ofNullable(request.getBadQuantity()).orElse(0)-Optional.ofNullable(request.getGoodQuantity()).orElse(0));
                createItemList.add(crateItem);
            }
            //批次号存在明细并且是当前提交的sku
            if (whIntermediateReturnApvItem!=null && sku.equals(request.getSku())){
                WhIntermediateReturnApvItem updateItem =  new WhIntermediateReturnApvItem();
                updateItem.setId(whIntermediateReturnApvItem.getId());
                updateItem.setStatus(WhBatchReturnSkuStatus.WAITING_GRID.intCode());
                updateItem.setReturnQuantity(Optional.ofNullable(request.getReturnQuantity()).orElse(0));
                updateItem.setGoodQuantity(Optional.ofNullable(request.getGoodQuantity()).orElse(0));
                updateItem.setBadQuantity(Optional.ofNullable(request.getBadQuantity()).orElse(0));
                updateItem.setShortageQuantity(Optional.ofNullable(request.getReturnQuantity()).orElse(0)- Optional.ofNullable(request.getGoodQuantity()).orElse(0)-Optional.ofNullable(request.getBadQuantity()).orElse(0));
                whIntermediateReturnApvItemService.updateWhIntermediateReturnApvItem(updateItem);
            }
        });

        if (CollectionUtils.isNotEmpty(createItemList)) {
            whIntermediateReturnApvItemService.batchCreateWhIntermediateReturnApvItem(createItemList);
        }
        SystemLogUtils.INTERMEDIATE_BATCH_RETURN_LOG.log(intermediateReturn.getId(),String.format("未匹配包裹："+isUnmatchedPackage+" "+deliveryType+" 退仓sku"+ request.getSku() + "退回良品数量" + request.getGoodQuantity() + " 不良品数量" + request.getBadQuantity()));
        return ApiResult.newSuccess();
    }

    /**
     * 分配SMT已扫描数量
     * @param request
     */
    private void alloSmtReceivedQty(WhIntermediateReturnRequest request, Integer returnId){
        if (returnId == null) {
            return;
        }
        SmtReturnOrderItemQueryCondition query = new SmtReturnOrderItemQueryCondition();
        query.setReturnId(returnId);
        query.setBarcode(request.getSku());
        List<SmtReturnOrderItem> itemList = smtReturnOrderItemDao.querySmtReturnOrderItemList(query, null);
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        if (itemList.size() == 1) {
            // 单据sku只有一条记录
            SmtReturnOrderItem item = itemList.get(0);
            SmtReturnOrderItem updateItem = new SmtReturnOrderItem();
            updateItem.setId(item.getId());
            updateItem.setGoodQuantity(Optional.ofNullable(item.getGoodQuantity()).orElse(0) + Optional.ofNullable(request.getGoodQuantity()).orElse(0));
            updateItem.setBadQuantity(Optional.ofNullable(item.getBadQuantity()).orElse(0) + Optional.ofNullable(request.getBadQuantity()).orElse(0));
            smtReturnOrderItemDao.updateSmtReturnOrderItem(updateItem);
            return;
        }
        itemList.sort(Comparator.comparing(SmtReturnOrderItem::getReturnQuantity).reversed());
        List<SmtReturnOrderItem> updateList = new ArrayList<>();
        Integer goodQty = Optional.ofNullable(request.getGoodQuantity()).orElse(0);
        Integer badQty = Optional.ofNullable(request.getBadQuantity()).orElse(0);
        for (SmtReturnOrderItem item : itemList) {
            if (goodQty <= 0 && badQty <= 0) {
                break;
            }
            Integer returnQty = Optional.ofNullable(item.getReturnQuantity()).orElse(0);
            Integer itemGoodQty = Optional.ofNullable(item.getGoodQuantity()).orElse(0);
            Integer itemBadQty = Optional.ofNullable(item.getBadQuantity()).orElse(0);
            int lessQty = returnQty - itemGoodQty - itemBadQty;
            if (returnQty <= 0 || lessQty <= 0) {
                // 无退货数量或已分配完
                continue;
            }
            SmtReturnOrderItem updateItem = new SmtReturnOrderItem();
            updateItem.setId(item.getId());
            updateItem.setGoodQuantity(0);
            updateItem.setBadQuantity(0);
            if (goodQty > 0) {
                if (goodQty > lessQty) {
                    updateItem.setGoodQuantity(itemGoodQty + lessQty);
                    goodQty -= lessQty;
                    lessQty = 0;
                } else {
                    updateItem.setGoodQuantity(itemGoodQty + goodQty);
                    lessQty -= goodQty;
                    goodQty = 0;
                }
            }
            if (badQty > 0 && lessQty > 0) {
                if (badQty > lessQty) {
                    updateItem.setBadQuantity(itemBadQty + lessQty);
                    badQty -= lessQty;
                } else {
                    updateItem.setBadQuantity(itemBadQty + badQty);
                    badQty = 0;
                }
            }
            updateList.add(updateItem);
        }
        if (goodQty >0 || badQty>0){
            SmtReturnOrderItem item = updateList.get(0);
            item.setGoodQuantity(Optional.ofNullable(item.getGoodQuantity()).orElse(0) + goodQty);
            item.setBadQuantity(Optional.ofNullable(item.getBadQuantity()).orElse(0) + badQty);
        }
        smtReturnOrderItemDao.batchUpdateSmtReturnOrderItem(updateList);
    }

    //合并smt退仓数据
    public SmtReturnOrder mergeSmtReturn(List<SmtReturnOrder> smtReturnOrderList){
        SmtReturnOrder smtReturnOrder = smtReturnOrderList.stream().min(Comparator.comparing(SmtReturnOrder::getId)).orElse(new SmtReturnOrder());
        List<SmtReturnOrderItem> smtReturnOrderItems = smtReturnOrderList
                .stream()
                .map(SmtReturnOrder::getItemList)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        smtReturnOrder.setItemList(smtReturnOrderItems);
        return smtReturnOrder;

    }

    public SheinReturnOrder mergeShineReturn(List<SheinReturnOrder> sheinReturnOrders){
        SheinReturnOrder sheinReturnOrder = sheinReturnOrders.stream().min(Comparator.comparing(SheinReturnOrder::getId)).orElse(new SheinReturnOrder());
        List<SheinReturnOrderItem> sheinReturnOrderItems = sheinReturnOrders
                .stream()
                .map(SheinReturnOrder::getItemList)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        sheinReturnOrder.setItemList(sheinReturnOrderItems);
        return sheinReturnOrder;

    }

    @Override
    public ApiResult<?> completeIntermediateReturn(String orderNo) {
        WhIntermediateReturnQueryCondition whIntermediateReturnQueryCondition = new WhIntermediateReturnQueryCondition();
        whIntermediateReturnQueryCondition.setOrderNo(orderNo);
        List<WhIntermediateReturn> intermediateReturns = queryWhIntermediateReturnAndItemList(whIntermediateReturnQueryCondition, null);
        if (CollectionUtils.isEmpty(intermediateReturns)){
            return ApiResult.newError("未找到中转仓退货批次信息！");
        }
        WhIntermediateReturn intermediateReturn = intermediateReturns.get(0);
        if (WhBatchReturnStatus.WAITING_GRID.intCode().equals(intermediateReturn.getStatus())){
            return ApiResult.newError("该批次已完成！");
        }
        WhIntermediateReturn update = new WhIntermediateReturn();
        update.setId(intermediateReturn.getId());
        update.setStatus(WhBatchReturnStatus.WAITING_GRID.intCode());
        updateWhIntermediateReturn(update);

        List<WhIntermediateReturnApvItem> updatedItems = new ArrayList<>();
        List<Integer> deleteItems =  new ArrayList<>();
        for (WhIntermediateReturnApvItem intermediateReturnApvItem : intermediateReturn.getIntermediateReturnApvItems()) {
            if (!WhBatchReturnSkuStatus.SPLIT.intCode().equals(intermediateReturnApvItem.getStatus())) continue;
            WhIntermediateReturnApvItem updateItem = new WhIntermediateReturnApvItem();
            updateItem.setId(intermediateReturnApvItem.getId());
            updateItem.setStatus(WhBatchReturnSkuStatus.WAITING_GRID.intCode());
            updatedItems.add(updateItem);

            Integer returnQuantity = Optional.ofNullable(intermediateReturnApvItem.getReturnQuantity()).orElse(0);
            Integer goodQuantity = Optional.ofNullable(intermediateReturnApvItem.getGoodQuantity()).orElse(0);
            if (returnQuantity<=0 && goodQuantity<=0) {
                deleteItems.add(intermediateReturnApvItem.getId());
            }

        }
        if (CollectionUtils.isNotEmpty(updatedItems)) {
            whIntermediateReturnApvItemService.batchUpdateWhIntermediateReturnApvItem(updatedItems);
        }
        if (CollectionUtils.isNotEmpty(deleteItems)) {
            for (Integer deleteItem : deleteItems) {
                whIntermediateReturnApvItemService.deleteWhIntermediateReturnApvItem(deleteItem);
            }
        }
        return ApiResult.newSuccess();
    }

    //查询中转仓退仓sku信息
    public void queryFbaReturnSku(WhIntermediateReturnDto returnDto,String apvNo){
        List<WhIntermediateSkuReturnDto> skuReturnList = returnDto.getSkuReturnList();
        List<WhIntermediateReturnApvItem> skuItemsSubmit = returnDto.getSkuItemsSubmit()
                .stream()
                .filter(item -> !WhBatchReturnSkuStatus.SPLIT.intCode().equals(item.getStatus()))
                .sorted(Comparator.comparing(WhIntermediateReturnApvItem::getId, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
        returnDto.setSkuItemsSubmit(skuItemsSubmit);
        Map<String, WhIntermediateReturnApvItem> submitMap = skuItemsSubmit.stream().collect(Collectors.toMap(item -> item.getSku() + item.getApvNo(), item -> item, (v1, v2) -> v2));
        if (CollectionUtils.isEmpty(skuReturnList)) {
            return;
        }
        List<String> skuList = skuReturnList.stream().map(WhIntermediateSkuReturnDto::getSku).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }
        WhSkuQueryCondition queryCondition=new WhSkuQueryCondition();
        queryCondition.setSkus(skuList);
        List<WhSku> whSkuList = whSkuDao.queryWhSkuList(queryCondition, null);
        Map<String, WhSku> skuMap = whSkuList.stream().collect(Collectors.toMap(WhSku::getSku, whSku -> whSku));


        WhSkuExtendQueryCondition whSkuExtendQueryCondition = new WhSkuExtendQueryCondition();
        whSkuExtendQueryCondition.setSkuList(skuList);
        List<WhSkuExtend> whSkuExtends = whSkuExtendService.queryWhSkuExtends(whSkuExtendQueryCondition, null);
        Map<String, String> skuExtendMap = whSkuExtends
                .stream()
                .filter(s->StringUtils.isNotBlank(s.getSku()) &&StringUtils.isNotBlank(s.getPackageAttribute()) )
                .collect(Collectors.toMap(WhSkuExtend::getSku, WhSkuExtend::getPackageAttribute, (v1, v2) -> v2));
        List<String> expSkuList = GetSkuQcCategoryDescUtil.getExpSkuList(skuList);

        for (WhIntermediateSkuReturnDto whIntermediateSkuReturnDto : skuReturnList) {
            if (skuMap.get(whIntermediateSkuReturnDto.getSku())!=null) {
                WhSku whSku = skuMap.get(whIntermediateSkuReturnDto.getSku());
                whIntermediateSkuReturnDto.setImageUrl(whSku.getImageUrl());
                whIntermediateSkuReturnDto.setName(whSku.getName());
                whIntermediateSkuReturnDto.setLocation(whSku.getLocationNumber());
            }
            if (skuExtendMap.get(whIntermediateSkuReturnDto.getSku())!=null) {
                whIntermediateSkuReturnDto.setPackAttr(skuExtendMap.get(whIntermediateSkuReturnDto.getSku()));
            }
            if (expSkuList.contains(whIntermediateSkuReturnDto.getSku())) {
                whIntermediateSkuReturnDto.setShelfLife(true);
            }
            if (MapUtils.isNotEmpty(submitMap) && submitMap.get(whIntermediateSkuReturnDto.getSku() + apvNo)!=null){
                whIntermediateSkuReturnDto.setSubmitComplete(true);
                whIntermediateSkuReturnDto.setGoodQuantity(submitMap.get(whIntermediateSkuReturnDto.getSku() + apvNo).getGoodQuantity());
            }
        }
        List<WhIntermediateSkuReturnDto> skuReturnDtoList = skuReturnList.stream()
                .sorted(Comparator.comparing(
                        WhIntermediateSkuReturnDto::getSortId,
                        Comparator.nullsLast(Comparator.naturalOrder())
                ))
                .collect(Collectors.toList());
        returnDto.setSkuReturnList(skuReturnDtoList);

    }


    @Override
    public void completeWhBatchReturn(WhIntermediateReturn whBatchReturn, boolean error) {
        if (whBatchReturn != null) {
            WhIntermediateReturn update = new WhIntermediateReturn();
            update.setId(whBatchReturn.getId());
            if (error && !WhBatchReturnStatus.EXCEPTION_COMPLETE.intCode().equals(whBatchReturn.getStatus())) {
                update.setStatus(WhBatchReturnStatus.EXCEPTION_COMPLETE.intCode());
                SystemLogUtils.INTERMEDIATE_BATCH_RETURN_LOG.log(whBatchReturn.getId(), "异常完成");
            } else {
                update.setStatus(WhBatchReturnStatus.COMPLETE.intCode());
                SystemLogUtils.INTERMEDIATE_BATCH_RETURN_LOG.log(whBatchReturn.getId(), "上架完成");
            }
            updateWhIntermediateReturn(update);
        }
    }
}