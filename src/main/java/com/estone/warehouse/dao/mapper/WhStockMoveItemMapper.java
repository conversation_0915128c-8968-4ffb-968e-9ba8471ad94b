package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.WhStockMoveItem;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class WhStockMoveItemMapper implements <PERSON><PERSON>apper<WhStockMoveItem> {

    public WhStockMoveItem mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhStockMoveItem entity = new WhStockMoveItem();
        entity.setId(rs.getObject(WhStockMoveItemDBField.ID) == null ? null : rs.getInt(WhStockMoveItemDBField.ID));
        entity.setMoveId(rs.getObject(WhStockMoveItemDBField.MOVE_ID) == null ? null : rs.getInt(WhStockMoveItemDBField.MOVE_ID));
        entity.setSku(rs.getString(WhStockMoveItemDBField.SKU));
        entity.setLocationNumber(rs.getString(WhStockMoveItemDBField.LOCATION_NUMBER));
        entity.setStore(rs.getString(WhStockMoveItemDBField.STORE));
        entity.setSite(rs.getString(WhStockMoveItemDBField.SITE));
        entity.setOutLocation(rs.getObject(WhStockMoveItemDBField.OUT_LOCATION) == null ? null : rs.getInt(WhStockMoveItemDBField.OUT_LOCATION));
        entity.setInLocation(rs.getObject(WhStockMoveItemDBField.IN_LOCATION) == null ? null : rs.getInt(WhStockMoveItemDBField.IN_LOCATION));
        entity.setQuantity(rs.getObject(WhStockMoveItemDBField.QUANTITY) == null ? null : rs.getInt(WhStockMoveItemDBField.QUANTITY));
        entity.setCreateBy(rs.getObject(WhStockMoveItemDBField.CREATE_BY) == null ? null : rs.getInt(WhStockMoveItemDBField.CREATE_BY));
        entity.setCreateDate(rs.getTimestamp(WhStockMoveItemDBField.CREATE_DATE));
        return entity;
    }
}