package com.estone.checkin.bean;

import com.estone.checkin.enums.ExceptionMarkReasonEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved. 
 * Project Name: wms
 * Package Name: com.estone.checkin.bean
 * File Name: ExceptionMarkConfig.java
 * Description: 入库异常标记原因配置（极简版本，支持灵活扩展）
 * Author: Amoi
 * Date: 2025-12-20
 * ---------------------------------------------------------------------------
 */
@Data
public class ExceptionMarkConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标记原因列表，只存储显示名称
     * 例如：["待确认", "质量问题", "包装损坏", "商品破损"]
     */
    private List<String> markReasonList;

    /**
     * 默认构造函数，不再初始化配置，改为空列表
     */
    public ExceptionMarkConfig() {
        this.markReasonList = new ArrayList<>();
    }

    /**
     * 添加标记原因
     * @param reasonName 原因名称
     */
    public void addMarkReason(String reasonName) {
        if (reasonName != null && !reasonName.trim().isEmpty()) {
            if (!this.markReasonList.contains(reasonName)) {
                this.markReasonList.add(reasonName);
            }
        }
    }

    /**
     * 删除标记原因
     * @param reasonName 原因名称
     * @return 是否删除成功
     */
    public boolean removeMarkReason(String reasonName) {
        if (reasonName != null && !reasonName.trim().isEmpty() && this.markReasonList != null) {
            return this.markReasonList.remove(reasonName);
        }
        return false;
    }

    /**
     * 检查原因是否存在
     * @param reasonName 原因名称
     * @return 是否存在
     */
    public boolean containsReason(String reasonName) {
        return this.markReasonList != null && this.markReasonList.contains(reasonName);
    }

} 