package com.estone.checkin.bean;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved. 
 * Project Name: wms
 * Package Name: com.estone.checkin.bean
 * File Name: ExceptionMarkRequest.java
 * Description: 入库异常标记请求实体
 * Author: Amoi
 * Date: 2025-12-20
 * ---------------------------------------------------------------------------
 */
@Data
public class ExceptionMarkRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 异常单ID
     */
    @NotNull(message = "异常单ID不能为空")
    private Integer exceptionId;

    /**
     * 标记原因代码
     */
    @NotNull(message = "标记原因不能为空")
    private String markReason;

    /**
     * 标记备注
     */
    private String markRemark;

    /**
     * 自定义原因（当markReason为CUSTOM时使用）
     */
    private String customReason;

    /**
     * 获取最终的标记原因代码
     * 如果是自定义原因，返回处理后的代码
     * @return 最终的标记原因代码
     */
    public String getFinalMarkReason() {
        if ("CUSTOM".equals(this.markReason) && this.customReason != null && !this.customReason.trim().isEmpty()) {
            return "CUSTOM_" + this.customReason.trim();
        }
        return this.markReason;
    }

    /**
     * 验证请求参数
     * @return 是否有效
     */
    public boolean isValid() {
        if (this.exceptionId == null || this.markReason == null || this.markReason.trim().isEmpty()) {
            return false;
        }
        
        // 如果是自定义原因，检查自定义原因是否为空
        if ("CUSTOM".equals(this.markReason)) {
            return this.customReason != null && !this.customReason.trim().isEmpty();
        }
        
        return true;
    }

    /**
     * 获取错误信息
     * @return 错误信息，无错误返回null
     */
    public String getValidationError() {
        if (this.exceptionId == null) {
            return "异常单ID不能为空";
        }
        
        if (this.markReason == null || this.markReason.trim().isEmpty()) {
            return "标记原因不能为空";
        }
        
        if ("CUSTOM".equals(this.markReason)) {
            if (this.customReason == null || this.customReason.trim().isEmpty()) {
                return "自定义原因不能为空";
            }
            if (this.customReason.trim().length() > 100) {
                return "自定义原因长度不能超过100个字符";
            }
        }
        
        return null;
    }
} 