package com.estone.checkin.action;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import com.estone.checkin.bean.ExceptionMarkConfig;
import com.estone.checkin.utils.ExceptionMarkConfigUtil;
import com.estone.common.util.model.ApiResult;

import lombok.extern.slf4j.Slf4j;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved. 
 * Project Name: wms
 * Package Name: com.estone.checkin.action
 * File Name: ExceptionMarkConfigController.java
 * Description: 入库异常标记配置Controller（极简版本，支持灵活扩展）
 * Author: Amoi
 * Date: 2025-12-20
 * ---------------------------------------------------------------------------
 */
@RestController
@RequestMapping(value = "exceptionMarkConfig")
@Slf4j
public class ExceptionMarkConfigController {

    /**
     * 获取当前可用的标记原因选项（用于前端下拉）
     * @return ApiResult<List<String>>
     */
    @PostMapping("/getAvailableReasons")
    @ResponseBody
    public ApiResult<List<String>> getAvailableReasons() {
        try {
            log.info("获取可用标记原因选项");

            ExceptionMarkConfig config = ExceptionMarkConfigUtil.getExceptionMarkConfig();
            return ApiResult.newSuccess(config.getMarkReasonList());

        } catch (Exception e) {
            log.error("获取可用标记原因选项失败", e);
            return ApiResult.newError("获取可用标记原因选项失败: " + e.getMessage());
        }
    }

    /**
     * 添加自定义标记原因
     * @param reasonName 原因名称
     * @return ApiResult<Boolean>
     */
    @PostMapping("/addCustomReason")
    @ResponseBody
    public ApiResult<Boolean> addCustomReason(@RequestParam String reasonName) {
        try {
            log.info("添加自定义标记原因: {}", reasonName);

            // 参数校验
            if (StringUtils.isBlank(reasonName)) {
                return ApiResult.newError("原因名称不能为空");
            }
            
            if (reasonName.length() > 100) {
                return ApiResult.newError("原因名称长度不能超过100个字符");
            }

            // 获取当前配置
            ExceptionMarkConfig config = ExceptionMarkConfigUtil.getExceptionMarkConfig();
            
            // 检查名称是否已存在
            if (config.containsReason(reasonName)) {
                return ApiResult.newError("原因名称已存在");
            }
            
            // 添加自定义原因
            config.addMarkReason(reasonName);

            // 保存配置
            String saveResult = ExceptionMarkConfigUtil.setExceptionMarkConfig(config);
            if (saveResult != null) {
                return ApiResult.newError(saveResult);
            }

            log.info("添加自定义标记原因成功: {}", reasonName);
            return ApiResult.newSuccess(true);

        } catch (Exception e) {
            log.error("添加自定义标记原因失败", e);
            return ApiResult.newError("添加自定义标记原因失败: " + e.getMessage());
        }
    }

    /**
     * 删除自定义标记原因
     * @param reasonName 原因名称
     * @return ApiResult<Boolean>
     */
    @PostMapping("/removeCustomReason")
    @ResponseBody
    public ApiResult<Boolean> removeCustomReason(@RequestParam String reasonName) {
        try {
            log.info("删除自定义标记原因: {}", reasonName);

            // 参数校验
            if (StringUtils.isBlank(reasonName)) {
                return ApiResult.newError("原因名称不能为空");
            }

            // 获取当前配置
            ExceptionMarkConfig config = ExceptionMarkConfigUtil.getExceptionMarkConfig();
            
            // 检查名称是否存在
            if (!config.containsReason(reasonName)) {
                return ApiResult.newError("原因名称不存在");
            }
            
            // 删除原因
            boolean removed = config.removeMarkReason(reasonName);
            if (!removed) {
                return ApiResult.newError("删除失败");
            }

            // 保存配置
            String saveResult = ExceptionMarkConfigUtil.setExceptionMarkConfig(config);
            if (saveResult != null) {
                return ApiResult.newError(saveResult);
            }

            log.info("删除自定义标记原因成功: {}", reasonName);
            return ApiResult.newSuccess(true);

        } catch (Exception e) {
            log.error("删除自定义标记原因失败", e);
            return ApiResult.newError("删除自定义标记原因失败: " + e.getMessage());
        }
    }
}