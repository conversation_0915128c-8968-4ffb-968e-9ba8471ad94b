package com.estone.checkin.action;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.annotation.Resource;

import com.estone.warehouse.bean.*;
import com.estone.warehouse.service.WhLocationService;
import com.estone.warehouse.service.WhStockService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.estone.allocation.bean.WhApvAllocation;
import com.estone.allocation.bean.WhApvAllocationItem;
import com.estone.allocation.bean.WhApvAllocationItemQueryCondition;
import com.estone.allocation.bean.WhApvAllocationQueryCondition;
import com.estone.allocation.service.WhApvAllocationService;
import com.estone.allocation.util.WarehouseProperties;
import com.estone.apv.util.ApvPackUtils;
import com.estone.checkin.bean.*;
import com.estone.checkin.domain.WhAllocationCheckInDo;
import com.estone.checkin.domain.WhCheckInDo;
import com.estone.checkin.enums.*;
import com.estone.checkin.service.WhAllocationCheckInExceptionService;
import com.estone.checkin.service.WhAllocationCheckInItemService;
import com.estone.checkin.service.WhAllocationCheckInService;
import com.estone.checkin.service.WhPurchaseOrderService;
import com.estone.checkin.utils.GetSkuQcCategoryDescUtil;
import com.estone.common.enums.WarehousePropertyEnum;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.sku.bean.*;
import com.estone.sku.enums.*;
import com.estone.sku.service.UniqueSkuExpRelationService;
import com.estone.sku.domain.WhSkuDo;
import com.estone.sku.enums.ProcessType;
import com.estone.sku.enums.UniqueSkuFrom;
import com.estone.sku.enums.UniqueSkuStep;
import com.estone.sku.service.WhSkuQcCategoryService;
import com.estone.sku.service.WhSkuService;
import com.estone.sku.service.WhUniqueSkuService;
import com.estone.sku.utils.UniqueSkuUtils;
import com.estone.sku.service.WhUuidService;
import com.estone.system.param.bean.SystemParam;
import com.estone.warehouse.enums.BoxType;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * @ClassName: AllocationCheckInScanController
 * @Description: 调拨入库扫描
 * <AUTHOR>
 * @date 2019年3月18日
 * @version 0.0.2
 *
 */
@Controller
@RequestMapping(value = "allocationCheckin/scans")
@Slf4j
public class AllocationCheckInScanController {
    @Resource
    private WhAllocationCheckInService whAllocationCheckInService;

    @Resource
    private WhAllocationCheckInItemService whAllocationCheckInItemService;

    @Resource
    private WhBoxService whBoxService;


    @Resource
    private WhSkuService whSkuService;


    @Resource
    private WhApvAllocationService whApvAllocationService;

    @Resource
    private WhAllocationCheckInExceptionService whAllocationCheckInExceptionService;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;
    @Resource
    private WhUuidService whUuidService;
    @Resource
    private UniqueSkuExpRelationService uniqueSkuExpRelationService;

    @Resource
    private WhPurchaseOrderService whPurchaseOrderService;

    @Resource
    private WhStockService whStockService;

    @Resource
    private WhLocationService whLocationService;

    // 进入扫描调拨箱号页面
    @RequestMapping(value = "toAllocationScan", method = { RequestMethod.GET })
    public String toAllocationScan(@ModelAttribute("domain") WhAllocationCheckInDo domain) {
        log.info("allocationCheckin/scans/toAllocationScan");
        return "checkin/check_scan_allocation_view";
    }

    // 箱号扫描(查询调拨单)
    @RequestMapping(value = "allocationScan", method = { RequestMethod.GET })
    public String allocationScan(@ModelAttribute("domain") WhAllocationCheckInDo domain,
            @RequestParam("bagNo") String bagNo) {
        log.info("allocationCheckin/scans/allocationScan: bagNo[" + bagNo + "]");
        domain.setExceptionTypes(ExceptionType.getSelectJson(null, null));
        domain.setBagNo(bagNo);

        // 验证是否完成点数
        WhApvAllocationItemQueryCondition itemQuery = new WhApvAllocationItemQueryCondition();
        itemQuery.setPutBy(DataContextHolder.getUserId());
        itemQuery.setPutStatus(0);
        WhApvAllocationItem allocationItem = whApvAllocationService.queryWhApvAllocationItem(itemQuery);
        if (null != allocationItem && !bagNo.equals(allocationItem.getBoxNo())) {
            domain.setMessageInfo("上次扫描的箱号：" + allocationItem.getBoxNo() + "未点击完成点数");
            return "checkin/check_scan_allocation";
        }

        WhApvAllocationQueryCondition allocationQuery = new WhApvAllocationQueryCondition();
        allocationQuery.setBoxNo(bagNo);
        List<WhApvAllocation> apvAllocations = new ArrayList<>();// 调拨单
        apvAllocations = whApvAllocationService.queryApvAllocationDetailList(allocationQuery, null);
        domain.setApvAllocations(apvAllocations);

        if (CollectionUtils.isEmpty(apvAllocations)) {
            domain.setMessageInfo("没有找到相关调拨单！");
            return "checkin/check_scan_allocation";
        }

        List<String> skus = new ArrayList<>();
        Iterator<WhApvAllocation> apvAllocationIter = apvAllocations.iterator();
        while (apvAllocationIter.hasNext()) {
            WhApvAllocation apvAllocation = apvAllocationIter.next();
            /**
             * 调拨单状态 1=待提交 2=待拣货 3=拣货中 4=待装箱 5=待审核 6=已出库 7=运输中 8=已推送 9=已签收
             * 10=部分入库 11=已入库
             */
            // 9=已签收 10=部分入库 11=已入库; 只处理已签收、部分入库的
            Integer allocationStatus = apvAllocation.getAllocationStatus();
            if (allocationStatus != 9 && allocationStatus != 10) {
                apvAllocationIter.remove();
                continue;
            }

            // 生成入库单只显示非完全入库的
            List<WhApvAllocationItem> items = new ArrayList<>(apvAllocation.getAllocationItems());

            apvAllocation.getAllocationItems().clear();
            for (WhApvAllocationItem item : items) {

                Integer putBy = item.getPutBy();
                if (null != putBy && !DataContextHolder.getUserId().equals(putBy)) {
                    domain.setApvAllocations(null);
                    domain.setMessageInfo("该箱号已被其它账号扫描，不可重复扫描！扫描人：" + TaglibUtils.getEmployeeNameByUserId(putBy));
                    return "checkin/check_scan_allocation";
                }


                // 排查异常单,过滤掉已废弃
                WhAllocationCheckInExceptionQueryCondition exQuery = new WhAllocationCheckInExceptionQueryCondition();
                exQuery.setAllocationNo(apvAllocation.getAllocationNo());
                exQuery.setSku(item.getSku());
                exQuery.setStatusList(List.of(AllocationExceptionStatus.UNCONFIRM.intCode(),
                        AllocationExceptionStatus.COMPLETE.intCode()));
                List<WhAllocationCheckInException> whAllocationCheckInExceptions = whAllocationCheckInExceptionService.queryWhAllocationCheckInExceptions(exQuery, null);
                int exceptionCount = Optional.ofNullable(whAllocationCheckInExceptions).orElse(new ArrayList<>())
                        .stream()
                        .mapToInt(i -> Optional.ofNullable(i.getQuantity()).orElse(0)).sum();

                int count =CollectionUtils.isNotEmpty(whAllocationCheckInExceptions)?whAllocationCheckInExceptions.size():0;

                boolean isDiscard = false;

                // 查询入库单
                WhAllocationCheckInQueryCondition query = new WhAllocationCheckInQueryCondition();
                query.setAllocationOrderNo(apvAllocation.getAllocationNo());
                query.setSku(item.getSku());
                List<WhAllocationCheckIn> whAllocationCheckIns = whAllocationCheckInService
                        .queryWhAllocationCheckIns(query, null);
                Integer inedQuantity = 0;// 已入库数量
                if (CollectionUtils.isNotEmpty(whAllocationCheckIns)) {
                    for (WhAllocationCheckIn whAllocationCheckIn : whAllocationCheckIns) {

                        /*
                         * WAITING_QC("等待QC", "3"), QC_NG("QC不良品","7"),
                         * WAITING_UP("等待上架", "9"), UPING("上架中","11"),
                         * UPERROR("上架失败", "12"),CONFIRMED("已上架", "13"),
                         * DISCARDED("已废弃", "20")
                         */

                        Integer quantity = 0; // 点数良品数量
                        Integer exceptionQuantity = 0; // 点数不良品数量
                        Integer qcQuantity = 0; // QC良品数量
                        Integer qcExceptionQuantity = 0; // QC不良品数量
                        Integer upQuantity = 0; // 上架数量

                        Integer status = whAllocationCheckIn.getStatus();
                        WhAllocationCheckInItem whAllocationCheckInItem = whAllocationCheckIn
                                .getWhAllocationCheckInItem();
                        if (CheckInStatus.WAITING_QC.intCode().equals(status)) {
                            if (whAllocationCheckInItem != null) {
                                quantity = whAllocationCheckInItem.getQuantity() == null ? 0
                                        : whAllocationCheckInItem.getQuantity();
                                exceptionQuantity = exceptionCount;
                            }
                        }
                        if (CheckInStatus.WAITING_UP.intCode().equals(status) || CheckInStatus.UPING.intCode().equals(status)
                                || CheckInStatus.UPERROR.intCode().equals(status)) {
                            if (whAllocationCheckInItem != null) {
                                qcQuantity = whAllocationCheckInItem.getQcQuantity() == null ? 0
                                        : whAllocationCheckInItem.getQcQuantity();
                                qcExceptionQuantity = whAllocationCheckInItem.getQcExceptionQuantity() == null ? 0
                                        : whAllocationCheckInItem.getQcExceptionQuantity();
                            }
                        }
                        if (CheckInStatus.CONFIRMED.intCode().equals(status)) {
                            upQuantity = whAllocationCheckInItem.getUpQuantity() == null ? 0
                                    : whAllocationCheckInItem.getUpQuantity();
                        }
                        inedQuantity += quantity + exceptionQuantity + qcQuantity + qcExceptionQuantity + upQuantity;
                        if (CheckInStatus.DISCARDED.intCode().equals(status)) {
                            isDiscard = true;
                        }
                    }
                }


                if (count > 0 && (!isDiscard && inedQuantity <= 0)) {
                    continue;
                }

                Integer boxNum = item.getBoxNum();// 调拨装箱数量
                log.info("apvAllocation: allocationNo[" + apvAllocation.getAllocationNo() + "],sku[" + item.getSku()
                        + "], boxNum[" + boxNum + "], inedQuantity[" + inedQuantity + "]");

                // 完成入库不用显示
                if (boxNum != null && boxNum > inedQuantity) {
                    apvAllocation.getAllocationItems().add(item);
                    if (!skus.contains(item.getSku())) {
                        item.setInedQuantity(inedQuantity);
                        skus.add(item.getSku());
                    }
                }
            }

            // 已入库的不用出现
            if (CollectionUtils.isEmpty(apvAllocation.getAllocationItems())) {
                apvAllocationIter.remove();
            }
        }

        if (CollectionUtils.isEmpty(domain.getApvAllocations())) {
            if (null != allocationItem) {
                List<WhApvAllocation> allocationList = new ArrayList<WhApvAllocation>();
                WhApvAllocation whApvAllocation = new WhApvAllocation();
                whApvAllocation.setAllocationNo(allocationItem.getAllocationNo());
                allocationList.add(whApvAllocation);
                domain.setApvAllocations(allocationList);
            }
            else {
                // 该调拨单已入库
                domain.setMessageInfo("该调拨单未签收或已入库!");
            }
        }
        else {
            Map<String,WhSku> skuMap = new HashMap<>();
            // 查找是否使用原包装，产品标识信息
            Map<String, WhSkuWithPmsInfo> whSkuWithPmsInfoMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(skus)) {
                WhSkuQueryCondition query = new WhSkuQueryCondition();
                query.setSkus(skus);
                List<WhSku> whSkus = whSkuService.queryWhSkus(query, null);

                List<WhSkuWithPmsInfo> whSkuWithPmsInfoList = ApvPackUtils.getPmsSkuInfoList(skus);
                if (CollectionUtils.isNotEmpty(whSkuWithPmsInfoList)) {
                    for (WhSkuWithPmsInfo whSkuWithPmsInfo : whSkuWithPmsInfoList) {
                        whSkuWithPmsInfoMap.put(whSkuWithPmsInfo.getSku(), whSkuWithPmsInfo);
                    }
                }
                if (CollectionUtils.isNotEmpty(whSkus)) {
                    for (WhSku whSku : whSkus) {
                        whSku.setFloorLocation(ProcessType.getIntCode(whSku.getSkuAlias(), whSku.getFloorLocation()));
                        skuMap.put(whSku.getSku(),whSku);
                    }
                }
            }
            // 保存可入库数据
            for (WhApvAllocation apvAllocation : domain.getApvAllocations()) {
                for (WhApvAllocationItem item : apvAllocation.getAllocationItems()) {
                    if (whSkuWithPmsInfoMap != null && whSkuWithPmsInfoMap.get(item.getSku()) != null) {
                        item.setUseOlderPackage(whSkuWithPmsInfoMap.get(item.getSku()).getIsOriginalPackage());// 设置是否带原包装发货
                        item.setProductFlag(whSkuWithPmsInfoMap.get(item.getSku()).getSkuLabelName());// 设置产品标识
                    }
                    item.setSkuQcCategoryDesc(GetSkuQcCategoryDescUtil.getSkuQcCategoryDesc(item.getSku()));//产品系统质检备注
                    item.setWhSku(skuMap.get(item.getSku()));
                }
            }
        }

        // 设置调拨单点数入库扫描人
        if (null == allocationItem && CollectionUtils.isNotEmpty(domain.getApvAllocations())) {
            List<WhApvAllocationItem> updateAllocationItems = new ArrayList<WhApvAllocationItem>();
            for (WhApvAllocation apvAllocation : apvAllocations) {
                for (WhApvAllocationItem item : apvAllocation.getAllocationItems()) {
                    WhApvAllocationItem updateAllocationItem = new WhApvAllocationItem();
                    updateAllocationItem.setAllocationItemId(item.getAllocationItemId());
                    updateAllocationItem.setPutBy(DataContextHolder.getUserId());
                    updateAllocationItem.setPutTime(new Timestamp(System.currentTimeMillis()));
                    updateAllocationItems.add(updateAllocationItem);
                }
            }
            whApvAllocationService.batchUpdateApvAllocationItem(updateAllocationItems);
        }

        return "checkin/check_scan_allocation";
    }

    @GetMapping(value = "uuid")
    @ResponseBody
    public ResponseJson scanUUid( @RequestParam("orderNo") String orderNo,
                                 @RequestParam("uuidSku") String uuidSku) {
        log.info("校验唯一码存在性: uuidSku[{}]", uuidSku);
        ResponseJson response = new ResponseJson(StatusCode.FAIL);

        try {
            // 参数格式校验
            if (StringUtils.isBlank(uuidSku) || !uuidSku.contains("=")) {
                response.setMessage("唯一码格式错误");
                return response;
            }
            // 分割sku和uuid
            String[] parts = StringUtils.split(uuidSku, "=");
            String sku = parts[0];
            String uuid = parts[1];

            WhApvAllocationItemQueryCondition itemQuery = new WhApvAllocationItemQueryCondition();
            itemQuery.setAllocationNo(orderNo);
            itemQuery.setSku(sku);
            WhApvAllocationItem whApvAllocationItem = whApvAllocationService.queryWhApvAllocationItem(itemQuery);
            if (whApvAllocationItem == null) {
                response.setMessage("调拨单中不存在唯一码对应的sku");
                return response;
            }

            WhUniqueSkuQueryCondition query = new WhUniqueSkuQueryCondition();
            query.setUniqueSku(uuidSku);
            List<WhUniqueSku> whUniqueSkus = whUniqueSkuService.queryWhUniqueSkusAndLogs(query, null);
            if (CollectionUtils.isNotEmpty(whUniqueSkus)) {
                WhUniqueSku whUniqueSku = whUniqueSkus.get(0);
                if (UniqueSkuFrom.ALLOCATION_CHECK_IN_REPLENISH.intCode().equals(whUniqueSku.getSourceFrom())) {
                    response.setStatus(StatusCode.SUCCESS);
                    return response;
                }
                if (UniqueSkuStep.SCRAP.intCode().equals(whUniqueSku.getStep())
                        && UniqueSkuFrom.ALLOCATION_CHECK_IN.intCode().equals(whUniqueSku.getSourceFrom())) {
                    response.setStatus(StatusCode.SUCCESS);
                    return response;
                }
            }
            // 获取目标系统URL（示例配置需在WarehouseProperties中添加）
            /** 同步目的仓sku属性url */
            String url = WarehouseProperties.getApiUrl("/foreigns/checkUniqueCode?uuidSku="+uuidSku);
            //String url =  "http://localhost:8282/wms/foreigns/checkUniqueCode"+"?uuidSku="+uuidSku;

            ResponseJson remoteResponse = HttpExtendUtils.get(url, "", ResponseJson.class, 30000, 30000);

            if (remoteResponse == null || !StatusCode.SUCCESS.equals(remoteResponse.getStatus())) {
                response.setMessage(remoteResponse != null ?
                        remoteResponse.getMessage() : "调出仓服务不可用");
            }else {
                response.setStatus(StatusCode.SUCCESS);
            }
        } catch (Exception e) {
            log.error("远程校验唯一码异常", e);
            response.setMessage("系统异常，请稍后重试");
        }

        return response;
    }



    // 生成入库单
    @RequestMapping(value = "checkIn", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson createCheckIn(@ModelAttribute("domain") WhAllocationCheckInDo domain) {
        log.info("allocationCheckin/scans/checkIn");
        ResponseJson response = new ResponseJson();
        WhAllocationCheckIn whAllocationCheckIn = domain.getWhAllocationCheckIn();
        WhAllocationCheckInItem whAllocationCheckInItem = whAllocationCheckIn.getWhAllocationCheckInItem();
        // 如果良品数量大于0，则走入库流程
        if (whAllocationCheckIn != null && whAllocationCheckInItem != null
                && whAllocationCheckInItem.getQuantity() != null && whAllocationCheckInItem.getQuantity() > 0) {

            if (CollectionUtils.isEmpty(domain.getUuidList()) || domain.getUuidList().size() < whAllocationCheckInItem.getQuantity()){
                response.setMessage("扫描唯一码数量不等于良品数，请刷新页面重新入库");
                return response;
            }
            WhAllocationCheckInQueryCondition query = new WhAllocationCheckInQueryCondition();
            query.setAllocationOrderNo(whAllocationCheckIn.getAllocationOrderNo());
            query.setSku(whAllocationCheckInItem.getSku());
            List<WhAllocationCheckIn> whAllocationCheckInList = whAllocationCheckInService
                    .queryWhAllocationCheckIns(query, null);
            if (CollectionUtils.isNotEmpty(whAllocationCheckInList)) {
                Integer inedQuantity = 0;
                for (WhAllocationCheckIn checkIn : whAllocationCheckInList) {

                    Integer quantity = 0; // 点数良品数量
                    Integer exceptionQuantity = 0; // 点数不良品数量
                    Integer qcQuantity = 0; // QC良品数量
                    Integer qcExceptionQuantity = 0; // QC不良品数量
                    Integer upQuantity = 0; // 上架数量

                    Integer status = whAllocationCheckIn.getStatus();
                    WhAllocationCheckInItem item = checkIn.getWhAllocationCheckInItem();
                    if (CheckInStatus.WAITING_QC.intCode().equals(status)) {
                        if (whAllocationCheckInItem != null) {
                            quantity = whAllocationCheckInItem.getQuantity() == null ? 0
                                    : whAllocationCheckInItem.getQuantity();
                            exceptionQuantity = whAllocationCheckInItem.getExceptionQuantity() == null ? 0
                                    : whAllocationCheckInItem.getExceptionQuantity();
                        }
                    }
                    if (CheckInStatus.WAITING_UP.intCode().equals(status) || CheckInStatus.UPING.intCode().equals(status)
                            || CheckInStatus.UPERROR.intCode().equals(status)) {
                        if (whAllocationCheckInItem != null) {
                            qcQuantity = whAllocationCheckInItem.getQcQuantity() == null ? 0
                                    : whAllocationCheckInItem.getQcQuantity();
                            qcExceptionQuantity = whAllocationCheckInItem.getQcExceptionQuantity() == null ? 0
                                    : whAllocationCheckInItem.getQcExceptionQuantity();
                        }
                    }
                    if (CheckInStatus.CONFIRMED.intCode().equals(status)) {
                        upQuantity = whAllocationCheckInItem.getUpQuantity() == null ? 0
                                : whAllocationCheckInItem.getUpQuantity();
                    }
                    inedQuantity += quantity + exceptionQuantity + qcQuantity + qcExceptionQuantity + upQuantity;
                }
                // 超过调拨数量不提交，提示重新扫描后再试
                if (inedQuantity + whAllocationCheckInItem.getQuantity() > whAllocationCheckInItem.getBoxQuantity()) {
                    response.setMessage("累计入库数量超过调拨数量");
                    return response;
                }
            }

            // 下一步，等待QC
            whAllocationCheckIn.setStatus(CheckInStatus.WAITING_QC.intCode());

            // 转换签收时间 2019-03-18 16:43:15.0
            String recievedTimeStr = whAllocationCheckIn.getRecievedTimeStr();
            if (StringUtils.isNotBlank(recievedTimeStr)) {
                Timestamp recievedTime = null;
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    Date date = simpleDateFormat.parse(recievedTimeStr);
                    recievedTime = new Timestamp(date.getTime());
                }
                catch (ParseException e) {
                    log.error(e.getMessage(), e);
                }
                whAllocationCheckIn.setRecievedTime(recievedTime);
            }
            if (CollectionUtils.isNotEmpty(domain.getUuidList())) {
                domain.getUuidList().removeIf(uuid -> !StringUtils.equalsIgnoreCase(StringUtils.split(uuid, "=")[0]
                        ,whAllocationCheckIn.getWhAllocationCheckInItem().getSku()));
            }
            try {
                whAllocationCheckInService.createWhAllocationCheckInAndWhAllocationCheckInItem(CommonUtils.splitList(whAllocationCheckIn.getWhAllocationCheckInItem().getSku(),","),
                        whAllocationCheckIn,domain.getUuidList());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                response.setMessage(e.getMessage());
                return response;
            }
        }
        else {
            // 创建异常
            Integer exceptionQuantity = whAllocationCheckIn.getWhAllocationCheckInItem().getExceptionQuantity();
            if (null != exceptionQuantity && exceptionQuantity > 0) {
                WhAllocationCheckInException checkInException = new WhAllocationCheckInException();
                checkInException.setUuId(UUID.randomUUID().toString());
                checkInException.setInId(whAllocationCheckInItem.getInId());
                checkInException.setAllocationNo(whAllocationCheckIn.getAllocationOrderNo());
                checkInException.setSku(whAllocationCheckInItem.getSku());
                checkInException.setExceptionFrom(ExceptionFrom.CHECK_IN_EXCEPTION.intCode());
                checkInException.setExceptionType(whAllocationCheckInItem.getExceptionTypeStr());
                checkInException.setQuantity(whAllocationCheckInItem.getExceptionQuantity());
                checkInException.setStatus(AllocationExceptionStatus.UNCONFIRM.intCode());
                Integer deliveryWarehouseId = WarehousePropertyEnum.HHD.intCode();
                Integer destWarehouseId = CacheUtils.getLocalWarehouseId();
                if (destWarehouseId.equals(WarehousePropertyEnum.HHD.intCode())) {
                    deliveryWarehouseId = WarehousePropertyEnum.NN.intCode();
                }
                checkInException.setDeliveryWarehouseId(deliveryWarehouseId);
                checkInException.setDestWarehouseId(destWarehouseId);
                checkInException.setCreatedBy(DataContextHolder.getUserId());
                checkInException.setCreationDate(new Timestamp(System.currentTimeMillis()));
                whAllocationCheckInExceptionService.createWhAllocationCheckInException(checkInException);
            }
        }

        List<WhAllocationCheckIn> inStocks = new ArrayList<>();
        inStocks.add(whAllocationCheckIn);
        response.getBody().put("inStocks", inStocks);
        return response;
    }

    /**
     *
     * @Title: printQRCode
     * @Description: 单个入库单打印
     *
     * @param domain
     * @param inId
     * @return
     */
    @RequestMapping(value = "qrcode", method = { RequestMethod.GET })
    public String printQRCode(@ModelAttribute("domain") WhCheckInDo domain, @RequestParam("inId") Integer inId,
                              @RequestParam("quantity") Integer quantity) {
        // 判空inId
        if (inId == null) {
            return "checkin/check_scan_allocation";
        }
        WhAllocationCheckInQueryCondition query = new WhAllocationCheckInQueryCondition();
        query.setInId(inId);
        List<WhAllocationCheckIn> whAllocationCheckInList = whAllocationCheckInService
                .queryWhAllocationCheckIns(query, null);
       // 判空集合
        if (CollectionUtils.isEmpty(whAllocationCheckInList)) {
            return "checkin/check_scan_allocation";
        }
        WhAllocationCheckIn whAllocationCheckIn = whAllocationCheckInList.get(0);
        WhAllocationCheckInItem whAllocationCheckInItem = whAllocationCheckIn.getWhAllocationCheckInItem();
        WhSkuQueryCondition query_sku = new WhSkuQueryCondition();
        query_sku.setSku(whAllocationCheckInItem.getSku());
        WhSku whSku = whSkuService.queryWhSku(query_sku);

        List<Integer> businessIdList = new ArrayList<Integer>();
        List<String> skuList = new ArrayList<String>();
        List<Integer> quantityList = new ArrayList<Integer>();
        List<String> locationList = new ArrayList<String>();
        List<String> nameList = new ArrayList<String>();
        List<Integer> warehouseIdList = new ArrayList<>();

        String sku = whAllocationCheckInItem.getSku();

        String location = whAllocationCheckInItem.getLocation();
        if (StringUtils.isBlank(location) && sku != null) {
            location = whSku.getLocationNumber();
        }

        skuList.add(sku);
        quantityList.add(quantity);
        locationList.add(location);
        businessIdList.add(inId);

        if (whSku != null && StringUtils.isNotBlank(whSku.getName())) {
            int length = whSku.getName().length() > 14 ? 14 : whSku.getName().length();
            nameList.add(whSku.getName().substring(0, length));
        }
        if (whAllocationCheckIn != null && whAllocationCheckIn.getWarehouseId() != null) {
            warehouseIdList.add(whAllocationCheckIn.getWarehouseId());
        }
        domain.setPrintAgain(true);
        return printQRCode(domain, businessIdList, skuList, quantityList, locationList, nameList, warehouseIdList,whAllocationCheckIn.getAllocationOrderNo());
    }

    public String printQRCode(WhCheckInDo domain, List<Integer> businessIdList,
                           List<String> skuList, List<Integer> quantityList, List<String> locationList, List<String> nameList,
                              List<Integer> warehouseIdList, String allocationOrderNo) {
        if (CollectionUtils.isNotEmpty(skuList) && CollectionUtils.isNotEmpty(businessIdList)) {
            domain.setPrintSkuUser(DataContextHolder.getUsername());

            if (StringUtils.isNotBlank(allocationOrderNo)) {
                WhCheckIn whCheckIn=domain.getWhCheckIn()==null?new WhCheckIn():domain.getWhCheckIn();
                //查询保质期或售后结算
                whApvAllocationService.createExpBatchAndBindUuid(whCheckIn,skuList.get(0),allocationOrderNo,businessIdList.get(0));
                domain.setWhCheckIn(whCheckIn);
            }

            boolean noLabel = domain.isNoLabel();

            for (int i = 0, len = skuList.size(); i < len; i++) {
                String sku = skuList.get(i);
                try {

                    if (RedissonLockUtil.tryLock(PrintSkuQrCodeRedisLock.EXISTS_SKU_QR_CODE.getKey() + sku, 1,
                            PrintSkuQrCodeRedisLock.EXISTS_SKU_QR_CODE.getTimeout())) {
                        Integer quantity = quantityList.get(i) == null ? 0 : quantityList.get(i);
                        //不贴标业务只打印一个唯一码
                        if (noLabel)
                            quantity = 1;

                        String location = null;
                        if (CollectionUtils.isNotEmpty(locationList) && locationList.size() > i) {
                            location = locationList.get(i);
                        }

                        String skuName = null;
                        if (CollectionUtils.isNotEmpty(nameList) && nameList.size() > i) {
                            if (StringUtils.isNotBlank(nameList.get(i))) {
                                int length = nameList.get(i).length() > 14 ? 14 : nameList.get(i).length();
                                skuName = nameList.get(i).substring(0, length);
                            }
                        }

                        Integer businessId = null;
                        if (CollectionUtils.isNotEmpty(businessIdList) && businessIdList.size() > i) {
                            businessId = businessIdList.get(i);
                        }
                        if (businessId != null) {
                            String uuid = sku;
                            String code = "";
                            int uuidCode = 0;
                            String dateStr = DateUtils.dateToString(new Date(), "yyMMdd");

                            //String historySkuUuid = StringRedisUtils.get(RedisKeys.getPrintSkuQrCodeKey(sku));
                            String historySkuUuid = UniqueSkuUtils.getMaxUuid(sku);
                            if (StringUtils.isNotBlank(historySkuUuid)) {
                                String historyDate = historySkuUuid.substring(0, 6);
                                uuidCode = Integer.parseInt(historySkuUuid.substring(6));
                                if (!historyDate.equals(dateStr)) {
                                    // 不是当天，删除redis数据,重新排序
                                    //StringRedisUtils.del(RedisKeys.getPrintSkuQrCodeKey(sku));
                                    UniqueSkuUtils.del(sku);
                                    uuidCode = 0;
                                }
                            }
                            Integer warehouseId = null;
                            if (CollectionUtils.isNotEmpty(warehouseIdList) && warehouseIdList.size() > i) {
                                warehouseId = warehouseIdList.get(i);
                            }
                            log.info("打印二维码: businessId[" + businessId + "], sku[" + sku + "], location[" + location
                                    + "], quantity[" + quantity + "]");

                            String maxUuid = null;
                            for (int j = 0; j < quantity; j++) {
                                PrintCheckInSku printCheckInSku = new PrintCheckInSku();
                                int incremen = j + 1 + uuidCode;
                                String incremenStr;
                                if (WarehousePropertyEnum.NN.intCode() == CacheUtils.getLocalWarehouseId() && incremen < 100000){
                                    incremenStr = String.format("1%05d", incremen);// 字符串格式化.
                                } else {
                                    incremenStr = String.format("%06d", incremen);// 字符串格式化.
                                }
                                code = dateStr + incremenStr;// 日期+序列号
                                String uuidSku = uuid + "=" + code;// 唯一码：SKU=日期+序列号
                                printCheckInSku.setBusinessId(businessId);// 入库单ID
                                printCheckInSku.setSku(sku);
                                printCheckInSku.setSkuName(skuName);

                                if (StringUtils.isNotBlank(location)) {
                                    int length = location.length() >= 12 ? location.length() : 11;
                                    if (length > 11) {
                                        location = location.substring(0, length - 6);
                                    }
                                }
                                printCheckInSku.setLocation(location);

                                printCheckInSku.setType(SkuBusinessType.CHECK_IN.intCode());// 入库单
                                if (domain.getWhCheckIn() != null) {
                                    WhCheckIn whCheckIn = domain.getWhCheckIn();
                                    Integer status = whCheckIn.getStatus();
                                    if (whCheckIn.getQcUser() != null
                                            && CheckInStatus.WAITING_QC.intCode().equals(status)) {
                                        printCheckInSku.setType(SkuBusinessType.CHECK.intCode());// 入库单QC中
                                    }
                                }
                                printCheckInSku.setWarehouseId(warehouseId);
                                printCheckInSku.setUuid(uuidSku);
                                printCheckInSku.setCode(code);
                                if (domain.isPrintAgain()) {
                                    printCheckInSku.setSourceFrom(UniqueSkuFrom.CHECK_REPLENISH.intCode());
                                }else if (StringUtils.isNotBlank(allocationOrderNo)) {
                                    printCheckInSku.setSourceFrom(UniqueSkuFrom.ALLOCATION_CHECK_IN.intCode());
                                }else {
                                    printCheckInSku.setSourceFrom(UniqueSkuFrom.CHECK_IN.intCode());
                                }
                                domain.getPrintSku().add(printCheckInSku);

                                // 更新当前redis中sku的最新码
                                //StringRedisUtils.set(RedisKeys.getPrintSkuQrCodeKey(sku), dateStr + incremen);
                                maxUuid = code;
                            }
                            UniqueSkuUtils.updateMaxUuid(sku, maxUuid);
                            List<WhUniqueSku> whUniqueSkus = UniqueSkuUtils.buildWhUniqueSku(domain.getPrintSku());
                            if (CollectionUtils.isNotEmpty(whUniqueSkus)) {
                                try {
                                    // 保存唯一码保质期明细
                                    List<UniqueSkuExpRelation> addList = UniqueSkuUtils
                                            .buildUniqueSkuExpRelation(whUniqueSkus, domain.getWhCheckIn());
                                    uniqueSkuExpRelationService.batchCreateUniqueSkuExpRelation(addList);
                                    // 判断是否是售后结算SKU
                                    if ((domain.getWhCheckIn() != null && StringUtils.isNotBlank(domain.getWhCheckIn().getPurchaseOrderNo()))
                                            || (domain.getWhCheckIn()!=null && StringUtils.isNotBlank(allocationOrderNo))) {

                                        boolean afterSale = domain.getWhCheckIn().getAfterSaleQty() != null && domain.getWhCheckIn().getAfterSaleQty() > 0;

                                        if (!afterSale) {
                                            WhPurchaseOrderQueryCondition purchaseQuery = new WhPurchaseOrderQueryCondition();
                                            purchaseQuery
                                                    .setPurchaseOrderNo(domain.getWhCheckIn().getPurchaseOrderNo());
                                            purchaseQuery.setSku(sku);
                                            List<WhPurchaseOrder> whPurchaseOrders = whPurchaseOrderService
                                                    .queryWhPurchaseOrderAndItems(purchaseQuery, null);
                                            if (CollectionUtils.isNotEmpty(whPurchaseOrders)
                                                    && StringUtils.isNotEmpty(whPurchaseOrders.get(0).getJsonData())) {
                                                WhPurchaseOrder whPurchaseOrder = whPurchaseOrders.get(0);
                                                String purchaseOrderType = whPurchaseOrder.getPurchaseOrderType();
                                                List<String> unAfterSaleOrderTypes = Arrays.asList(PurchaseOrderType.NCGZS.getCode(),PurchaseOrderType.NCGCW.getCode(),PurchaseOrderType.NCGHH.getCode());
                                                if (StringUtils.isNotEmpty(whPurchaseOrder.init().getPayMethod())
                                                        && StringUtils.contains(whPurchaseOrder.init().getPayMethod(), "售后")
                                                        && !unAfterSaleOrderTypes.contains(purchaseOrderType)) {
                                                    afterSale = true;
                                                }
                                            }
                                        }
                                        if (afterSale) {
                                            for (WhUniqueSku uniqueSkus : whUniqueSkus) {
                                                uniqueSkus.addTag(UniqueSkuTagEnum.SALED_BALANCE);
                                            }
                                        }
                                        if (domain.getWhCheckIn()!=null && domain.getWhCheckIn().isShelfStraightHair()) {
                                            for (WhUniqueSku uniqueSkus : whUniqueSkus) {
                                                uniqueSkus.addTag(UniqueSkuTagEnum.SALE_DIRECT);
                                            }
                                            domain.setShelfStraightHair(true);
                                        }

                                        if (domain.getWhCheckIn()!=null && domain.getWhCheckIn().isFreeCheck()) {
                                            for (WhUniqueSku uniqueSkus : whUniqueSkus) {
                                                uniqueSkus.addTag(UniqueSkuTagEnum.NO_CHECK);
                                            }
                                            domain.setFreeCheck(true);
                                        }

                                    }
                                    whUniqueSkuService.batchCreateWhUniqueSku(whUniqueSkus);
                                }
                                catch (Exception e) {
                                    log.error(e.getMessage(), e);
                                    if (CollectionUtils.isNotEmpty(businessIdList) && businessIdList.size() > i) {
                                        domain.setReturnExpressId(businessIdList.get(i));
                                    }
                                    domain.getPrintSku().clear();
                                }
                            }
                        }
                    }
                    else {
                        if (domain.isPrintAgain()) {
                            return "sku/checkInBatchPrintQRcodeAgain";
                        }
                        if (CollectionUtils.isNotEmpty(businessIdList) && businessIdList.size() > i) {
                            domain.setReturnExpressId(businessIdList.get(i));
                        }
                        return "sku/checkInBatchPrintQRcode";
                    }
                }
                catch (Exception e) {
                    log.error(e.getMessage());
                }
                finally {
                    RedissonLockUtil.unlock(PrintSkuQrCodeRedisLock.EXISTS_SKU_QR_CODE.getKey() + sku);
                }

            }
        }
        if (domain.isPrintAgain()) {
            return "sku/checkInBatchPrintQRcodeAgain";
        }
        return "sku/checkInBatchPrintQRcode";
    }

    /**
     * 
     * @Title: obtainWhBox
     * @Description: 提货扫描
     *
     * @param domain
     * @return
     */
    @RequestMapping(value = "obtainWhBox", method = { RequestMethod.GET })
    public String obtainWhBox(@ModelAttribute("domain") WhAllocationCheckInDo domain) {
        log.info("allocationCheckin/scans/obtainWhBox");
        return "checkin/allocation_obtain_scan_box_view";
    }

    @RequestMapping(value = "checkBox", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson checkBox(@ModelAttribute("domain") WhAllocationCheckInDo domain,
            @RequestParam(value = "boxNo", required = false) String boxNo) {
        log.info("allocationCheckin/scans/checkBox: boxNo[" + boxNo + "]");
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        List<WhAllocationCheckInItem> whAllocationCheckInItems = null;
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        if (whBox != null && whBox.getType().equals(BoxType.ALLOCATION_CHECKIN.intCode())
                && StringUtils.isNotBlank(whBox.getRelationNo())) {
            WhAllocationCheckInItemQueryCondition query = new WhAllocationCheckInItemQueryCondition();
            query.setInId(Integer.valueOf(whBox.getRelationNo()));
            whAllocationCheckInItems = whAllocationCheckInItemService.queryWhAllocationCheckInItems(query, null);
        }
        if (CollectionUtils.isNotEmpty(whAllocationCheckInItems)) {
            WhAllocationCheckIn whAllocationCheckIn = whAllocationCheckInService
                    .getWhAllocationCheckIn(whAllocationCheckInItems.get(0).getInId());
            if (CheckInStatus.WAITING_UP.intCode().equals(whAllocationCheckIn.getStatus())
                    || CheckInStatus.UPERROR.intCode().equals(whAllocationCheckIn.getStatus())) {// 上架失败可以重新提货
                response.setMessage(JSON.toJSONString(whAllocationCheckInItems.get(0)));
                response.setStatus(StatusCode.SUCCESS);
            }
            else if (CheckInStatus.QC_NG.intCode().equals(whAllocationCheckIn.getStatus())) {
                response.setMessage("不良品，无法提货");
            }
            else if (whAllocationCheckIn.getStatus() < CheckInStatus.WAITING_UP.intCode()) {
                response.setMessage("未QC，无法提货");
            }
            else {
                response.setMessage("不是待上架或上架失败状态，无法提货");
            }
        }
        else {
            response.setMessage("未绑定,无提货信息");
        }
        log.info("response: " + response);
        return response;
    }

    /**
     * 
     * @Title: upIng
     * @Description: 提货扫描-上架中
     *
     * @param domain
     * @param uuid
     * @return
     */
    @RequestMapping(value = "upIng", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson upIng(@ModelAttribute("domain") WhAllocationCheckInDo domain,
            @RequestParam("uuid") String uuidSku, @RequestParam("obtainUser") Integer obtainUser) {
        log.info("allocationCheckin/scans/upIng: uuidSku[" + uuidSku + "], obtainUser[" + obtainUser + "]");
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (StringUtils.isBlank(uuidSku) || !uuidSku.contains("=")) {
            response.setMessage("扫描sku唯一码");
        }


        String sku = StringUtils.split(uuidSku, "=")[0];
        String uuid = StringUtils.split(uuidSku, "=")[1];
        WhUniqueSkuQueryCondition uuidQuery = new WhUniqueSkuQueryCondition();
        uuidQuery.setSku(sku);
        uuidQuery.setUuid(uuid);
        WhUniqueSku whUnique = whUniqueSkuService.queryWhUniqueSku(uuidQuery);
        if (null == whUnique ||  whUnique.getRelationId()==null) {
            response.setMessage("未绑定,无提货信息");
            return response;
        }
        Integer inId = whUnique.getRelationId();

        WhAllocationCheckIn whAllocationCheckIn = whAllocationCheckInService.getWhAllocationCheckIn(inId);
        if (whAllocationCheckIn != null && (CheckInStatus.WAITING_UP.intCode().equals(whAllocationCheckIn.getStatus())
                || (CheckInStatus.UPERROR.intCode().equals(whAllocationCheckIn.getStatus())))) {
            whAllocationCheckIn.setObtainUser(obtainUser);// 提货员
            try {
                return whAllocationCheckInService.updateAndUpIng(whAllocationCheckIn);
            }catch (Exception e){
                log.error("提货扫描失败！", e);
                response.setMessage("提货扫描失败，原因：" + e.getMessage());
                return response;
            }
        }else {
            response.setMessage("入库单状态不对！");
        }
        return response;
    }

    @RequestMapping(value = "createCheckIn/checkBox", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson createCheckInTocheckBox(@ModelAttribute("domain") WhAllocationCheckInDo domain,
            @RequestParam("boxNo") String boxNo, @RequestParam("type") String type) {
        log.info("allocationCheckin/scans/createCheckIn/checkBox: boxNo[" + boxNo + "]");
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        if (whBox != null) {
            if (StringUtils.isNotBlank(whBox.getRelationNo())) {
                response.setMessage("此周转筐已使用,请重新输入");
                return response;
            }
            if (StringUtils.isNotBlank(type) && type.equals("checkIn")
                    && !BoxType.ALLOCATION_CHECKIN.intCode().equals(whBox.getType())) {
                response.setMessage("此周转筐不是调拨入库专用,请重新输入");
                return response;
            }
            response.setMessage(JSON.toJSONString(whBox));
            response.setStatus(StatusCode.SUCCESS);

        }
        else {
            response.setMessage("无此周转筐,请重新输入");
        }
        log.info("response: " + response);
        return response;
    }

    /**
     * 点数完成，批量生成少sku异常单
     * 
     * @param bagNo
     * @param secretKey
     * @return
     */
    @RequestMapping(value = "batchCreateWhCheckInException", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson batchCreateWhCheckInException(@RequestParam(value = "bagNo") String bagNo,
            @RequestParam(value = "secretKey", required = false) String secretKey) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        if (StringUtils.isBlank(bagNo)) {
            response.setMessage("箱号为空！");
            return response;
        }

        if (StringUtils.isNotBlank(secretKey)) {
            // 验证入库主管密钥
            SystemParam systemParam = CacheUtils.SystemParamGet("ALLOCATION.PUTAWAY_SECRET_KEY");

            if (null == systemParam) {
                response.setMessage("未配置主管密钥，请联系管理员");
                return response;
            }

            String paramValue = systemParam.getParamValue();
            if (StringUtils.isBlank(paramValue)) {
                response.setMessage("主管密钥配置为空，请联系管理员");
                return response;
            }

            if (!secretKey.equals(paramValue)) {
                response.setMessage("密码错误，请重新输入！");
                return response;
            }
        }

        String redisKey = RedisKeys.getPushAllocationKey("BATCHCREATEWHCHECKINEXCEPTION");

        try {
            if (StringRedisUtils.exists(redisKey)) {
                response.setMessage("有账号正在操作该箱号，请稍后重试！");
                return response;
            }

            StringRedisUtils.set(redisKey, DataContextHolder.getUsername(), 180L);

            WhApvAllocationQueryCondition allocationQuery = new WhApvAllocationQueryCondition();
            allocationQuery.setBoxNo(bagNo);
            List<WhApvAllocation> apvAllocations = new ArrayList<>();// 调拨单
            apvAllocations = whApvAllocationService.queryApvAllocationDetailList(allocationQuery, null);

            if (CollectionUtils.isEmpty(apvAllocations)) {
                response.setMessage("没有找到相关调拨单！");
                return response;
            }

            boolean isPutSuccess = false;

            Set<String> allocationNoSet = new HashSet<String>();// 调拨单号

            List<WhApvAllocationItem> allAllocationItemList = new ArrayList<WhApvAllocationItem>();

            // 该箱号所转的所有sku
            Map<String, WhApvAllocationItem> allocationMap = new HashMap<String, WhApvAllocationItem>();
            for (WhApvAllocation apvAllocation : apvAllocations) {
                List<WhApvAllocationItem> allocationItems = apvAllocation.getAllocationItems();
                allAllocationItemList.addAll(allocationItems);
                for (WhApvAllocationItem allocationItem : allocationItems) {
                    if (StringUtils.isNotBlank(allocationItem.getAllocationNo())) {
                        allocationNoSet.add(allocationItem.getAllocationNo());
                    }
                    Integer putStatus = allocationItem.getPutStatus();
                    if (null != putStatus && putStatus == 1) {
                        isPutSuccess = true;
                    }
                    String sku = allocationItem.getSku();
                    if (StringUtils.isNotBlank(sku)) {
                        allocationMap.put(sku, allocationItem);
                    }
                }
            }

            if (isPutSuccess) {
                response.setStatus(StatusCode.SUCCESS);
                response.setMessage("已完成点数");
                return response;
            }

            if (CollectionUtils.isEmpty(allocationMap.keySet())) {
                response.setMessage("未查询到该箱号绑定的sku！");
                return response;
            }

            // 查询调拨入库单
            WhAllocationCheckInQueryCondition checkQuery = new WhAllocationCheckInQueryCondition();
            checkQuery.setBagNo(bagNo);
            List<WhAllocationCheckIn> checkInList = whAllocationCheckInService.queryWhAllocationCheckIns(checkQuery,
                    null);
            for (WhAllocationCheckIn allocationCheckIn : checkInList) {
                WhAllocationCheckInItem allocationCheckInItem = allocationCheckIn.getWhAllocationCheckInItem();
                if (null != allocationCheckInItem && StringUtils.isNotBlank(allocationCheckInItem.getSku())) {
                    allocationMap.remove(allocationCheckInItem.getSku());
                }
            }

            // 查询调拨入库异常单
            if (CollectionUtils.isNotEmpty(allocationNoSet) && CollectionUtils.isNotEmpty(allocationMap.keySet())) {
                WhAllocationCheckInExceptionQueryCondition exQuery = new WhAllocationCheckInExceptionQueryCondition();
                exQuery.setAllocationNoList(new ArrayList<String>(allocationNoSet));
                exQuery.setSkuList(new ArrayList<String>(allocationMap.keySet()));
                List<WhAllocationCheckInException> exceptionList = whAllocationCheckInExceptionService
                        .queryWhAllocationCheckInExceptions(exQuery, null);
                for (WhAllocationCheckInException checkInException : exceptionList) {
                    allocationMap.remove(checkInException.getSku());
                }
            }

            if (CollectionUtils.isNotEmpty(allocationMap.keySet())) {
                if (StringUtils.isNotBlank(secretKey)) {
                    // 生成点数入库异常单
                    Integer destWarehouseId = CacheUtils.getLocalWarehouseId();
                    for (String sku : allocationMap.keySet()) {
                        WhApvAllocationItem item = allocationMap.get(sku);
                        if (null != item && null != item.getBoxNum() && item.getBoxNum() > 0) {
                            WhAllocationCheckInException checkInException = new WhAllocationCheckInException();
                            checkInException.setUuId(UUID.randomUUID().toString());
                            checkInException.setAllocationNo(item.getAllocationNo());
                            checkInException.setSku(item.getSku());
                            checkInException.setExceptionFrom(ExceptionFrom.CHECK_IN_EXCEPTION.intCode());
                            checkInException.setExceptionType(ExceptionType.LESS_SKU.getCode());
                            checkInException.setQuantity(item.getBoxNum());
                            checkInException.setStatus(AllocationExceptionStatus.UNCONFIRM.intCode());
                            Integer deliveryWarehouseId = 1;
                            if (destWarehouseId.equals(1)) {
                                deliveryWarehouseId = 2;
                            }
                            checkInException.setDeliveryWarehouseId(deliveryWarehouseId);
                            checkInException.setDestWarehouseId(destWarehouseId);
                            checkInException.setCreatedBy(DataContextHolder.getUserId());
                            checkInException.setCreationDate(new Timestamp(System.currentTimeMillis()));
                            whAllocationCheckInExceptionService.createWhAllocationCheckInException(checkInException);
                        }
                    }
                }
                else {
                    response.setMessage(
                            JSON.toJSONString(allocationMap.keySet()) + "未点数，请确认箱内SKU已全部点数，如果确认已经全部点数，请找入库主管输入秘钥确认。");
                    response.setExceptionCode("waitManagerConfirm");
                    return response;
                }
            }

            // 更新调拨单item putStatus为已入库
            List<WhApvAllocationItem> updateAllocationItems = new ArrayList<WhApvAllocationItem>();
            for (WhApvAllocationItem allocationItem : allAllocationItemList) {
                WhApvAllocationItem updateAllocationItem = new WhApvAllocationItem();
                updateAllocationItem.setAllocationItemId(allocationItem.getAllocationItemId());
                updateAllocationItem.setPutBy(DataContextHolder.getUserId());
                updateAllocationItem.setPutStatus(1);
                updateAllocationItem.setPutTime(new Timestamp(System.currentTimeMillis()));
                updateAllocationItems.add(updateAllocationItem);
            }
            whApvAllocationService.batchUpdateApvAllocationItem(updateAllocationItems);

            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("操作成功");
            return response;
        }
        catch (Exception e) {
            log.error("完成点数异常", e);
            response.setMessage("完成点数失败：" + e.getMessage());
            return response;
        }
        finally {
            StringRedisUtils.del(redisKey);
        }
    }

    @PostMapping("/printUuid")
    @ResponseBody
    public ModelAndView printUuid(@ModelAttribute("domain") WhSkuDo domain, @RequestParam("sku") String sku,
            @RequestParam("quantity") Integer quantity,
            @RequestParam(value = "checkBatchNo", required = false) String checkBatchNo) {
        ModelAndView modelAndView = new ModelAndView("sku/batchPrintSkuQRcode");
        try {
            ApiResult<List<PrintCheckInSku>> apiResult = whUuidService.printUuid(sku, quantity, checkBatchNo);
            if (apiResult.isSuccess() && CollectionUtils.isNotEmpty(apiResult.getResult())){
                domain.getPrintSku().addAll(apiResult.getResult());
                // 判断是否保质期SKU
                List<String> expSkuList = GetSkuQcCategoryDescUtil.getExpSkuList(Collections.singletonList(sku));
                if (CollectionUtils.isNotEmpty(expSkuList)){
                    domain.setSkuTagsSelect(UniqueSkuTagEnum.SHELF_LIFE.getName());
                }
            }
            return modelAndView;
        }
        catch (Exception e) {
            log.error("打印唯一码失败" + e.getMessage(), e);
            return modelAndView;
        }
    }

    @RequestMapping(value = "printLocationLabel", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson printLocationLabel(@RequestParam String locationNumber, @RequestParam Integer stockId) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (StringUtils.isBlank(locationNumber) || null == stockId){
            response.setMessage("不存在拣货库位");
            return response;
        }
        String location = generateLabelHtml(locationNumber, stockId);
        if (StringUtils.isBlank(location)) {
            response.setMessage("不存在拣货库位!");
            return response;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("htmlContent", location);
        response.setStatus(StatusCode.SUCCESS);
        response.setBody(result);
        return response;
    }
    private String generateLabelHtml(String locationNumber, Integer stockId) {
        WhStockQueryCondition queryCondition = new WhStockQueryCondition();
        queryCondition.setId(stockId);
        WhStock whStock = whStockService.queryWhStock(queryCondition);
        if (Objects.nonNull(whStock) && StringUtils.isNotBlank(whStock.getLocationNumber())){
            locationNumber=whStock.getLocationNumber();
        }
        WhLocationQueryCondition locationQueryCondition = new WhLocationQueryCondition();
        locationQueryCondition.setLocation(locationNumber);
        List<WhLocation> locations = whLocationService.queryWhLocations(locationQueryCondition, null);
        if (CollectionUtils.isEmpty(locations)) {
            return null;
        }
        WhLocation whLocation = locations.get(0);
        String shelf = "";
        if(StringUtils.isNotBlank(locationNumber)){
            String[] vals = locationNumber.split("-");
            if (vals.length >= 3) {
                shelf = "-"+vals[2];
            }
        }
        return whLocation.getLocationRegion()+whLocation.getLocationAisle() + shelf;
    }

}
