package com.estone.checkin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved. 
 * Project Name: wms
 * Package Name: com.estone.checkin.enums
 * File Name: ExceptionMarkReasonEnum.java
 * Description: 入库异常标记原因枚举（简化版本，支持灵活扩展）
 * Author: Amoi
 * Date: 2025-12-20
 * ---------------------------------------------------------------------------
 */
@Getter
@AllArgsConstructor
public enum ExceptionMarkReasonEnum {

    /**
     * 待确认
     */
    TO_CONFIRM("待确认"),

    /**
     * 需补货
     */
    NEED_REPLENISH("需补货"),

    /**
     * 质量问题
     */
    QUALITY_ISSUE("质量问题"),

    /**
     * 包装损坏
     */
    PACKAGE_DAMAGE("包装损坏"),

    /**
     * 数量异常
     */
    QUANTITY_EXCEPTION("数量异常"),

    /**
     * 待退货
     */
    TO_RETURN("待退货"),

    /**
     * 库位错误
     */
    LOCATION_ERROR("库位错误"),

    /**
     * SKU不符
     */
    SKU_MISMATCH("SKU不符"),

    /**
     * 供应商问题
     */
    SUPPLIER_ISSUE("供应商问题"),

    /**
     * 运输损坏
     */
    TRANSPORT_DAMAGE("运输损坏"),
    ;

    /**
     * 原因名称
     */
    private final String name;


    /**
     * 获取所有显示名称列表
     * @return 所有显示名称的列表
     */
    public static String[] getAllDisplayNames() {
        return Arrays.stream(values())
                .map(ExceptionMarkReasonEnum::getName)
                .toArray(String[]::new);
    }

} 