package com.estone.checkin.enums;

/**
 *
 * @ClassName: ExceptionMarkWay
 * @Description: 异常处理方式
 * <AUTHOR>
 * @date 2018年11月19日
 * @version 0.0.2
 *
 */
public enum ExceptionMarkWay {

    MARK("异常标记","99"),

    CANCEL_MARK("取消标记","100");

    private String code;

    private String name;

    private ExceptionMarkWay(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        ExceptionMarkWay[] values = values();
        for (ExceptionMarkWay type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

}
