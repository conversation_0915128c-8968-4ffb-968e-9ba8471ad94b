package com.estone.apv.dao.mapper;

import com.estone.apv.bean.SmPackingException;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class SmPackingExceptionMapper implements <PERSON>Mapper<SmPackingException> {
    private boolean querySkuLocation = false;

    public SmPackingExceptionMapper() {

    }

    public SmPackingExceptionMapper(boolean querySkuLocation) {
        this.querySkuLocation = querySkuLocation;
    }

    public SmPackingException mapRow(ResultSet rs, int rowNum) throws SQLException {
        SmPackingException entity = new SmPackingException();
        entity.setId(rs.getObject(SmPackingExceptionDBField.ID) == null ? null : rs.getInt(SmPackingExceptionDBField.ID));
        entity.setApvNo(rs.getString(SmPackingExceptionDBField.APV_NO));
        entity.setBoxNo(rs.getString(SmPackingExceptionDBField.BOX_NO));
        entity.setPickTaskNo(rs.getString(SmPackingExceptionDBField.PICK_TASK_NO));
        entity.setSku(rs.getString(SmPackingExceptionDBField.SKU));
        entity.setQuantity(rs.getObject(SmPackingExceptionDBField.QUANTITY) == null ? 0 : rs.getInt(SmPackingExceptionDBField.QUANTITY));
        entity.setScanQuantity(rs.getObject(SmPackingExceptionDBField.SCAN_QUANTITY) == null ? 0 : rs.getInt(SmPackingExceptionDBField.SCAN_QUANTITY));
        entity.setLessQuantity(rs.getObject(SmPackingExceptionDBField.LESS_QUANTITY) == null ? 0 : rs.getInt(SmPackingExceptionDBField.LESS_QUANTITY));
        entity.setBindDate(rs.getTimestamp(SmPackingExceptionDBField.BIND_DATE));
        entity.setBindUser(rs.getObject(SmPackingExceptionDBField.BIND_USER) == null ? null : rs.getInt(SmPackingExceptionDBField.BIND_USER));
        entity.setOrderStatus(rs.getObject(SmPackingExceptionDBField.ORDER_STATUS) == null ? null : rs.getInt(SmPackingExceptionDBField.ORDER_STATUS));
        entity.setTaskType(rs.getObject(SmPackingExceptionDBField.TASK_TYPE) == null ? null : rs.getInt(SmPackingExceptionDBField.TASK_TYPE));

        entity.setBoxStatus(rs.getObject(SmPackingExceptionDBField.BOX_STATUS) == null ? null : rs.getInt(SmPackingExceptionDBField.BOX_STATUS));
        entity.setIsTask(rs.getObject(SmPackingExceptionDBField.IS_TASK) == null ? null : rs.getBoolean(SmPackingExceptionDBField.IS_TASK));
        entity.setOrderType(rs.getObject(SmPackingExceptionDBField.ORDER_TYPE) == null ? null : rs.getInt(SmPackingExceptionDBField.ORDER_TYPE));
        entity.setPickBy(rs.getObject(SmPackingExceptionDBField.PICK_BY) == null ? null : rs.getInt(SmPackingExceptionDBField.PICK_BY));
        entity.setPickDate(rs.getTimestamp(SmPackingExceptionDBField.PICK_DATE));
        entity.setPickQuantity(rs.getObject(SmPackingExceptionDBField.PICK_QUANTITY) == null ? null : rs.getInt(SmPackingExceptionDBField.PICK_QUANTITY));

        entity.setFirstPickBy(rs.getObject(SmPackingExceptionDBField.FIRST_PICK_BY) == null ? null : rs.getInt(SmPackingExceptionDBField.FIRST_PICK_BY));
        entity.setFirstPickDate(rs.getTimestamp(SmPackingExceptionDBField.FIRST_PICK_DATE));
        entity.setStockId(rs.getObject(SmPackingExceptionDBField.STOCK_ID) == null ? 0 : rs.getInt(SmPackingExceptionDBField.STOCK_ID));

        if (querySkuLocation) {
            entity.setLocationNumber(rs.getString("location_number"));
            entity.setApvStatus(rs.getObject("apv_status") == null ? null : rs.getInt("apv_status"));

        }
        return entity;
    }
}