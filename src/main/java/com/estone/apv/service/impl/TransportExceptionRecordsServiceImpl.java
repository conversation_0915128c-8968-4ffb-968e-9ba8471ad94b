package com.estone.apv.service.impl;

import com.estone.apv.bean.TransportExceptionRecords;
import com.estone.apv.bean.TransportExceptionRecordsQueryCondition;
import com.estone.apv.dao.TransportExceptionRecordsDao;
import com.estone.apv.service.TransportExceptionRecordsService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("transportExceptionRecordsService")
@Slf4j
public class TransportExceptionRecordsServiceImpl implements TransportExceptionRecordsService {
    @Resource
    private TransportExceptionRecordsDao transportExceptionRecordsDao;

    @Override
    public TransportExceptionRecords getTransportExceptionRecords(Integer id) {
        TransportExceptionRecords transportExceptionRecords = transportExceptionRecordsDao.queryTransportExceptionRecords(id);
        return transportExceptionRecords;
    }

    @Override
    public TransportExceptionRecords getTransportExceptionRecordsDetail(Integer id) {
        TransportExceptionRecords transportExceptionRecords = transportExceptionRecordsDao.queryTransportExceptionRecords(id);
        // 关联查询
        return transportExceptionRecords;
    }

    @Override
    public TransportExceptionRecords queryTransportExceptionRecords(TransportExceptionRecordsQueryCondition query) {
        Assert.notNull(query, "query is null!");
        TransportExceptionRecords transportExceptionRecords = transportExceptionRecordsDao.queryTransportExceptionRecords(query);
        return transportExceptionRecords;
    }

    @Override
    public List<TransportExceptionRecords> queryAllTransportExceptionRecordss() {
        return transportExceptionRecordsDao.queryTransportExceptionRecordsList();
    }

    @Override
    public List<TransportExceptionRecords> queryTransportExceptionRecordss(TransportExceptionRecordsQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = transportExceptionRecordsDao.queryTransportExceptionRecordsCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<TransportExceptionRecords>();
            }
        }
        List<TransportExceptionRecords> transportExceptionRecordss = transportExceptionRecordsDao.queryTransportExceptionRecordsList(query, pager);
        return transportExceptionRecordss;
    }

    @Override
    public void createTransportExceptionRecords(TransportExceptionRecords transportExceptionRecords) {
        try {
            transportExceptionRecordsDao.createTransportExceptionRecords(transportExceptionRecords);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateTransportExceptionRecords(List<TransportExceptionRecords> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                transportExceptionRecordsDao.batchCreateTransportExceptionRecords(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteTransportExceptionRecords(Integer id) {
        try {
            transportExceptionRecordsDao.deleteTransportExceptionRecords(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateTransportExceptionRecords(TransportExceptionRecords transportExceptionRecords) {
        try {
            transportExceptionRecordsDao.updateTransportExceptionRecords(transportExceptionRecords);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateTransportExceptionRecords(List<TransportExceptionRecords> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                transportExceptionRecordsDao.batchUpdateTransportExceptionRecords(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
}