package com.estone.apv.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.apv.bean.*;
import com.estone.apv.common.*;
import com.estone.apv.dao.ApvTrackDao;
import com.estone.apv.dao.WhApvDao;
import com.estone.apv.enums.ApvOversizeStatus;
import com.estone.apv.enums.ApvTaxTypeEnum;
import com.estone.apv.service.*;
import com.estone.checkin.bean.WhCheckInItem;
import com.estone.checkin.service.OutStockMatchHandelService;
import com.estone.common.enums.WarehousePropertyEnum;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.common.util.model.ResultModel;
import com.estone.core.mq.Queues;
import com.estone.core.mq.RabbitMqExchange;
import com.estone.foreign.bean.WhApvOrderInfoDTO;
import com.estone.foreign.bean.WhApvSkuInfoDTO;
import com.estone.pac.enums.PacOrderUploadStatus;
import com.estone.pac.utils.PacSendUtil;
import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.bean.WhPickingTaskQueryCondition;
import com.estone.picking.enums.PickingTaskStatus;
import com.estone.picking.service.WhPickingTaskService;
import com.estone.scan.deliver.bean.DeliverRecordDTO;
import com.estone.scan.deliver.bean.WhShippingMethod;
import com.estone.scan.deliver.service.WhShippingMethodService;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.common.AmqMessageModuleName;
import com.estone.system.rabbitmq.producer.RabbitmqProducerService;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.user.bean.SaleUser;
import com.estone.transfer.bean.FirstOrder;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.warehouse.bean.*;
import com.estone.warehouse.enums.BoxType;
import com.estone.warehouse.service.*;
import com.estone.warehouse.util.EasyExcelUtils;
import com.google.common.collect.Lists;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import com.whq.tool.util.IdGenerateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

@Slf4j
@Service("whApvService")
public class WhApvServiceImpl implements WhApvService {
    @Resource
    private WhApvDao whApvDao;

    @Resource
    private WhApvStatusService whApvStatusService;

    @Resource
    private ApvTrackDao apvTrackDao;

    @Resource
    private ApvTrackService apvTrackService;

    @Resource
    private ApvOversizeService apvOversizeService;

    @Resource
    private WhWarehouseService whWarehouseService;

    @Resource
    private RabbitmqProducerService rabbitmqProducerService;

    @Resource
    private ApvStatusUpdateService apvStatusUpdateService;

    @Resource
    private WhStockService whStockService;

    @Resource
    private FrozenStockService frozenStockService;

    @Resource
    private WhShippingMethodService whShippingMethodService;
    
    @Resource
    private OutStockMatchHandelService outStockMatchHandelService;

    private final static String REPAY_SKU = "repaySKU-";

    private final static ExecutorService executors = ExecutorUtils.newFixedThreadPool(5);

    private static final ThreadPoolExecutor countExecutors = ExecutorUtils.newFixedThreadPool(10);

    private static final ExecutorService distributionExecutors = ExecutorUtils.newFixedThreadPool(10);

    /**
     * 查询订单分布统计，每次查询的条数
     */
    private final static int SCROLL_NUMBER = 150000;

    @Resource
    private WhPickingTaskService pickingTaskService;

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private ApvExpressService apvExpressService;

    @Resource
    private AmqMessageService amqMessageService;

    @Resource
    private AmqpTemplate amqpTemplate;

    @Resource
    private WhApvItemService whApvItemService;

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    public WhApv getWhApv(Integer id) {
        WhApv whApv = whApvDao.queryWhApv(id);
        return whApv;
    }

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    public WhApv getWhApvDetail(Integer id) {
        WhApv whApv = whApvDao.queryWhApv(id);
        // 关联查询
        return whApv;
    }

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    public WhApv queryWhApv(WhApvQueryCondition query) {
        Assert.notNull(query);
        WhApv whApv = whApvDao.queryWhApv(query);
        return whApv;
    }

    @Override
    public List<WhApv> queryWhApvListByPickingTask(WhApvQueryCondition query) {
        return whApvDao.queryWhApvListByPickingTask(query);
    }

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    public List<WhApv> queryAllWhApvs() {
        return whApvDao.queryWhApvList();
    }

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    public List<WhApv> queryWhApvs(WhApvQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whApvDao.queryWhApvCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhApv>();
            }
        }
        List<WhApv> whApvs = whApvDao.queryWhApvList(query, pager);
        return whApvs;
    }

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    public void createWhApv(WhApv whApv) {
        try {
            whApvDao.createWhApv(whApv);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    public void batchCreateWhApv(List<WhApv> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whApvDao.batchCreateWhApv(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    public void deleteWhApv(Integer id) {
        try {
            whApvDao.deleteWhApv(id);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    public boolean updateWhApv(WhApv whApv) {
        try {
            if (whApvDao.updateWhApv(whApv) > 0) {
                return true;
            }
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
        return false;
    }

    public boolean deliverWhApv(Integer id, Double actualWeight) {
        WhApv whApv = new WhApv();
        whApv.setId(id);
        whApv.setStatus(ApvStatus.DELIVER.intCode());
        whApv.setActualWeight(actualWeight);
        if (whApvDao.updateWhApv(whApv) > 0) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addOverSizeLabel(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return 0;
        }

        List<Integer> excludeApvStatus = Arrays.asList(ApvStatus.CANCEL.intCode(),ApvStatus.DELIVER.intCode(),ApvStatus.LOADED.intCode());
        List<Integer> excludeShipStatus = Arrays.asList(ApvOrderType.EXPRESS_BUSINESS.intCode(), ApvOrderType.EXPRESS_NOT_STOCKED.intCode());
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setExcludeShipStatusList(excludeShipStatus);
        query.setApvIds(ids);
        List<WhApv> whApvList = this.queryWhApvs(query, null);
        if (CollectionUtils.isNotEmpty(whApvList)){
            whApvList = whApvList.stream()
                    .filter(whApv -> !excludeApvStatus.contains(whApv.getStatus()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(whApvList)){
            return 0;
        }

        List<WhApv> updateWhApvList = new ArrayList<>();
        List<ApvOversize> createOversizeList = new ArrayList<>();

        for (WhApv whApv : whApvList) {
            WhApv updateApv = new WhApv();
            updateApv.setId(whApv.getId());
            updateApv.setBuyerCheckout(whApv.getBuyerCheckout());
            if (updateApv.addBuyerCheckout(ApvTaxTypeEnum.OVERSIZE)){
                updateWhApvList.add(updateApv);
                ApvOversize oversize = new ApvOversize();
                oversize.setApvNo(whApv.getApvNo());
                oversize.setStatus(ApvOversizeStatus.UN_PUSH.intCode());
                createOversizeList.add(oversize);
            }
        }

        this.batchUpdateWhApv(updateWhApvList);
        apvOversizeService.batchCreateApvOversize(createOversizeList);
        updateWhApvList.forEach(whApv -> SystemLogUtils.APVLOG.log(whApv.getId(), "手动添加"+ApvTaxTypeEnum.OVERSIZE.getName()+"标签"));
        return updateWhApvList.size();
    }

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    public void batchUpdateWhApv(List<WhApv> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whApvDao.batchUpdateWhApv(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public boolean passBasketScan(Integer apvId, Integer type, String boxNo) {
        ApvStatus apvType = ApvStatus.EXCESSIVE_PARTS_TOUCHING;
        boolean scanPkycBoxNo = StringUtils.isNotEmpty(boxNo) && StringUtils.startsWith(boxNo, BoxType.PKYC.getShortCode());
        if (scanPkycBoxNo)
            apvType = ApvStatus.CHECK_PRINT;
        if (type == 0) {
            WhApvQueryCondition query = new WhApvQueryCondition();
            query.setId(apvId);
            WhApv apv = whApvDao.queryWhApv(query);
            apvType = ApvStatus.build(apv.getStatus().toString());
        }
        WhApv whApv = new WhApv();
        whApv.setId(apvId);

        // 等待发货
        whApv.setStatus(ApvStatus.WAITING_DELIVER.intCode());

        // 打单日期
        whApv.setPrintDate(new Timestamp(System.currentTimeMillis()));

        // 下一个流程[等待发货]
        int successModified = whApvDao.updateWhApvStatusByStatus(whApv, apvType.intCode());
        if (successModified > 0) {

            WhApv queryWhApv = whApvDao.queryWhApv(apvId);

            // 包装完成
            ApvTrack apvTrack = new ApvTrack();
            apvTrack.setApvNo(queryWhApv.getApvNo());
            apvTrack.setPackUser(DataContextHolder.getUserId());
            apvTrack.setPackFinishTime(new Timestamp(System.currentTimeMillis()));
            apvTrackService.updateApvTrackByApvNo(apvTrack);

            // 推送pms 包装
            pushPackingFinish(queryWhApv);

            //TODO 包装完成，推送状态到菜鸟
            PacSendUtil.wmsOrderStatusUpload(queryWhApv.getApvNo(), PacOrderUploadStatus.WMS_PACKAGE.getCode());


            if (type == 0) {
                SystemLogUtils.APVLOG.log(whApv.getId(), "单品包装发货单状态变更", new String[][]{
                        {"历史状态", ApvStatus.getNameByCode(apvType.getCode())}, {"更改状态", whApv.getStatusCn()}});
            } else {
                SystemLogUtils.APVLOG.log(whApv.getId(), "多件包装发货单状态变更", new String[][]{
                        {"历史状态", ApvStatus.getNameByCode(apvType.getCode())}, {"更改状态", whApv.getStatusCn()}});
            }

            // 解绑周转筐
            smPackFinishUnbindBoxNo(queryWhApv.getApvNo());

            return true;
        }

        return false;
    }

    /**
     * 单品多件包装完成解绑周转筐
     *
     * @param apvNo
     */
    public void smPackFinishUnbindBoxNo(String apvNo) {
        log.info("================start smPackFinishUnbindBoxNo ===============");
        ExecutorUtils.execute(executors, () -> {
            if (StringUtils.isEmpty(apvNo))
                return;
            WhApvQueryCondition apvQuery = new WhApvQueryCondition();
            apvQuery.setApvNo(apvNo);
            List<WhApv> whApvs = whApvDao.queryWhApvAndItemList(apvQuery, null);
            if (CollectionUtils.isEmpty(whApvs)) return;

            Integer taskId = whApvs.get(0).getTaskId();
            if (taskId == null) return;

            WhPickingTaskQueryCondition query = new WhPickingTaskQueryCondition();
            query.setId(taskId);
            query.setTaskStatus(PickingTaskStatus.COMPLETED.intCode());
            List<WhPickingTask> whPickingTasks = pickingTaskService.queryWhPickingTasks(query, null);
            if (CollectionUtils.isEmpty(whPickingTasks))
                return;
            WhPickingTask task = whPickingTasks.get(0);
            String boxNo = task.getBoxNo();
            if (StringUtils.isEmpty(boxNo))
                return;
            // 根据任务号捞取未包装完成的
            apvQuery = new WhApvQueryCondition();
            apvQuery.setTaskNo(task.getTaskNo());
            apvQuery.setStatus(ApvStatus.EXCESSIVE_PARTS_TOUCHING.intCode());
            int unFinish = whApvDao.queryWhApvAndItemListCount(apvQuery);
            if (unFinish > 0)
                return;
            // 全部包完，解绑周转筐
            WhBoxQueryCondition boxQuery = new WhBoxQueryCondition();
            boxQuery.setBoxNo(boxNo);
            boxQuery.setRelationNo(String.valueOf(task.getId()));
            WhBox whBox = whBoxService.queryWhBox(boxQuery);
            if (whBox != null)
                whBoxService.updateWhBoxOfUnbinding(boxNo,
                        new String[][]{{"多件包装完成解绑周转筐", String.valueOf(task.getId())}});
        }, "SM-PACK-FINISH-UNBIND-BOXNO");
    }


    @Override
    public boolean passMoreProductScan(Integer apvId) {
        WhApv whApv = new WhApv();
        whApv.setId(apvId);

        // 等待发货
        whApv.setStatus(ApvStatus.WAITING_DELIVER.intCode());

        // 打单日期
        whApv.setPrintDate(new Timestamp(System.currentTimeMillis()));

        // 下一个流程[等待发货]
        int successModified = whApvDao.updateWhApvStatusByStatus(whApv, ApvStatus.CHECK_PRINT.intCode());
        if (successModified > 0) {

            WhApv queryWhApv = whApvDao.queryWhApv(apvId);

            // 包装完成
            ApvTrack apvTrack = new ApvTrack();
            apvTrack.setApvNo(queryWhApv.getApvNo());
            apvTrack.setPackUser(DataContextHolder.getUserId());
            apvTrack.setPackFinishTime(new Timestamp(System.currentTimeMillis()));
            apvTrackService.updateApvTrackByApvNo(apvTrack);

            // 推送pms 包装
            pushPackingFinish(queryWhApv);
            //TODO 包装完成，推送状态到菜鸟
            PacSendUtil.wmsOrderStatusUpload(queryWhApv.getApvNo(), PacOrderUploadStatus.WMS_PACKAGE.getCode());

            SystemLogUtils.APVLOG.log(whApv.getId(), "多品发货单状态变更",
                    new String[][]{{"历史状态", ApvStatus.getNameByCode(ApvStatus.CHECK_PRINT.getCode())},
                            {"更改状态", whApv.getStatusCn()}});

            return true;
        }

        return false;
    }

    @Override
    public int updateWhApvListStatusAndSignPaymentByIds(List<Integer> ids, int status, boolean signPayment,
                                                        Integer beforeStatus) {
        int tempInt = 0;
        if (CollectionUtils.isNotEmpty(ids)) {
            try {
                tempInt = whApvDao.updateWhApvListStatusAndSignPaymentByIds(ids, status, signPayment, beforeStatus);
                for (Integer id : ids) {
                    SystemLogUtils.APVLOG.log(id, "批量修改APV状态",
                            new String[][]{{"APV状态", ApvStatus.getNameByCode(String.valueOf(status)) + ",是否生成任务状态="
                                    + (signPayment ? "已生成" : "未生成")}});
                }
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
        return tempInt;
    }

    @Override
    public String batchAllotWhApv(WhApvQueryCondition query, Pager page) {
        query.setMoveStatus(true); // 移动状态查询缩减字段优化速度
        // 不是缺货直发请求的排除缺货订单
        if (StringUtils.isBlank(query.getAllotType()) || !ApvAllotStock.LACK_MERCHANDISE.equals(query.getAllotType())) {
            query.setExcludeShipStatusList(Arrays.asList(ApvOrderType.LACK_MERCHANDISE.intCode()));
        }
        List<WhApv> whApvs = whApvDao.queryWhApvAndItemList(query, page);

        if (CollectionUtils.isEmpty(whApvs)) {
            return "数据不存在或数据已分配！";
        }

        int error = 0;
        List<WhApv> updateApvs = new ArrayList<>();
        for (WhApv whApv : whApvs) {
            whApv.setBatchAllot(true);
            if ((!ApvStatus.WAITING_ALLOT.equals(whApv.getStatus()) && !ApvStatus.STOCKOUT_NOT.equals(whApv.getStatus()))
                    || CollectionUtils.isEmpty(whApv.getWhApvItems()) || whApv.existBuyerCheckout(ApvTaxTypeEnum.INTERCEPTOR)) {
                error++;
                log.warn(whApv.getApvNo() + "该订单不能匹配库存，订单被拦截或者已被更改!");
            } else {
                whApv.setSignPayment(false);
                updateApvs.add(whApv);
            }
        }

        // 扣库存并改状态
        if (StringUtils.isNotBlank(query.getAllotType())
                && ApvAllotStock.LACK_MERCHANDISE.equals(query.getAllotType())) {
            apvStatusUpdateService.allotOutStockApv(updateApvs);
        }
        else {
            apvStatusUpdateService.allot(updateApvs);
        }
        for (WhApv whApv : updateApvs) {
            if (!ApvStatus.ALLOT.equals(whApv.getStatus())) {
                error++;
                log.warn(whApv.getApvNo() + "该订单匹配库存失败!");
            }
        }
        return "总条数" + whApvs.size() + "!" + "修改成功条数：" + (whApvs.size() - error) + "! 修改失败条数：" + error;
    }

    @Override
    public String allotWhApv(WhApvQueryCondition query, Pager page) {
        query.setMoveStatus(true); // 移动状态查询缩减字段优化速度
        // 不是缺货直发请求的排除缺货订单
        if (StringUtils.isBlank(query.getAllotType()) || !ApvAllotStock.LACK_MERCHANDISE.equals(query.getAllotType())) {
            query.setExcludeShipStatusList(Arrays.asList(ApvOrderType.LACK_MERCHANDISE.intCode()));
        }
        List<WhApv> whApvs = whApvDao.queryWhApvAndItemList(query, page);

        if (CollectionUtils.isEmpty(whApvs)) {
            return "数据不存在或数据已分配！";
        }

        int error = 0;
        List<WhApv> updateApvs = new ArrayList<>();
        for (WhApv whApv : whApvs) {
            whApv.setBatchAllot(true);
            if ((!ApvStatus.WAITING_ALLOT.equals(whApv.getStatus()) && !ApvStatus.STOCKOUT_NOT.equals(whApv.getStatus()))
                    || CollectionUtils.isEmpty(whApv.getWhApvItems()) || whApv.existBuyerCheckout(ApvTaxTypeEnum.INTERCEPTOR)) {
                error++;
                log.warn(whApv.getApvNo() + "该订单不能匹配库存，订单被拦截或者已被更改!");
            } else {
                whApv.setSignPayment(false);
                updateApvs.add(whApv);
            }
        }

        // 扣库存并改状态
        apvStatusUpdateService.allot(updateApvs);
        for (WhApv whApv : updateApvs) {
            if (!ApvStatus.ALLOT.equals(whApv.getStatus())) {
                error++;
                log.warn(whApv.getApvNo() + "该订单匹配库存失败!");
            }
        }
        return "总条数" + whApvs.size() + "!" + "修改成功条数：" + (whApvs.size() - error) + "! 修改失败条数：" + error;
    }

    @Override
    public String batchWaitingAllotWhApv(WhApvQueryCondition query, Pager page, String remark) {
//        query.setMoveStatus(true); // 移动状态查询缩减字段优化速度
        List<WhApv> whApvs = whApvDao.queryWhApvAndItemList(query, page);

        if (CollectionUtils.isEmpty(whApvs)) {
            return "数据不存在！";
        }

        int error = 0;
        List<WhApv> updateApvs = new ArrayList<>();
        for (WhApv whApv : whApvs) {
            Integer status = whApv.getStatus();
            String salesRecordNumber = whApv.getSalesRecordNumber();
            if (StringUtils.isNotBlank(salesRecordNumber) && salesRecordNumber.startsWith("NCD")
                && status!=null && ApvStatus.CHECK_PRINT.intCode()>=status
                && status>ApvStatus.CANCEL.intCode()) {
                whApv.setCancelReason(remark);
                if (apvStatusUpdateService.splitApvStatus(whApv,false)) {
                    continue;
                }
            }

            if (ApvStatus.DELIVER.equals(whApv.getStatus()) || ApvStatus.CANCEL.equals(whApv.getStatus())
                    || ApvStatus.WAITING_ALLOT.equals(whApv.getStatus()) || ApvStatus.STOCKOUT_NOT.equals(whApv.getStatus())) {
                error++;
                log.warn(whApv.getApvNo() + "该订单数据无效或者已被更改!");
            } else {
                whApv.setSignPayment(false);
                updateApvs.add(whApv);
            }
        }

        // 退库存并改状态
        apvStatusUpdateService.waitingAllot(updateApvs, remark);
        for (WhApv whApv : updateApvs) {
            if (!ApvStatus.WAITING_ALLOT.equals(whApv.getStatus())) {
                error++;
                log.warn(whApv.getApvNo() + "该订单移动到待分配失败!");
            }
        }
        return "总条数" + whApvs.size() + "!" + "修改成功条数：" + (whApvs.size() - error) + "! 修改失败条数：" + error;
    }

    @Override
    public String batchUpdateWhApvStatus(WhApvQueryCondition query, Pager page, Integer status) {
        query.setMoveStatus(true); // 移动状态查询缩减字段优化速度
        // (移动单件、多件、多品合单) 过滤掉物流单号=发货单号的这类数据，PMS会推给WMS更新新的真实物流单号
        if (ArrayUtils.contains(ApvStatus.getTouchingStatus(), status)) {
            // TODO WMS-295任务，暂时注释掉
            query.setIsSpecialApv(true);
        }
        // 排除缺货订单
        query.setExcludeShipStatusList(Arrays.asList(ApvOrderType.LACK_MERCHANDISE.intCode()));
        List<WhApv> whApvs = whApvDao.queryWhApvAndItemList(query, page);

        if (CollectionUtils.isEmpty(whApvs) || status == null) {
            return "数据不存在！";
        }

        List<Integer> apvIds = new ArrayList<>();

        String message = "总条数" + whApvs.size() + "!";
        List<WhApv> updateApvs = new ArrayList<>();
        for (WhApv whApv : whApvs) {
            boolean isSmt =whApv.getPlatform()!=null && ApvPlatform.SMT.intCode() == whApv.getPlatform();
            if (whApv.getWhApvLock() != null && whApv.getWhApvLock().getStatus() == ApvLockType.WHLOCK.intCode()) {
                message += whApv.getId() + "该订单已锁定!";
            } else if (!ApvStatus.ALLOT.equals(whApv.getStatus())) {
                // 指定已分配才能移动状态
                message += whApv.getId() + "该订单已取消!";
            } else if (isOverduedays(whApv.getLogisticsCompany(), whApv.getShipmentTime(),isSmt)) {
                String dayStr = isSmt ? "一天" : "四天";
                // 指定已分配才能移动状态
                message += whApv.getId() + "该订单追踪号还有"+ dayStr+"过期自动取消!";
                apvIds.add(whApv.getId());
            } else {
                WhApv updateApv = new WhApv();
                updateApv.setId(whApv.getId());
                updateApv.setStatus(status);
                updateApvs.add(updateApv);

                SystemLogUtils.APVLOG.log(whApv.getId(), "发货单状态变更", new String[][]{{"历史状态", whApv.getStatusCn()},
                        {"更改状态", ApvStatus.getNameByCode(status.toString())}});
            }
        }
        message += "修改成功条数：" + updateApvs.size() + "! 修改失败条数：" + (whApvs.size() - updateApvs.size());
        log.warn(message);
        // 取消追踪号超时
        ResponseJson responseJson = new ResponseJson();
        whApvStatusService.moveCancel(apvIds, responseJson);
        message += responseJson.getMessage();
        // 修改状态
        batchUpdateWhApv(updateApvs);
        return message;
    }

    private boolean isOverduedays(String logisticsCompany, Timestamp shippingOrderNoSyncTime,boolean isSmt) {
        if (StringUtils.isBlank(logisticsCompany) || shippingOrderNoSyncTime == null) {
            return false;
        }
        WhShippingMethod shippingMethod = whShippingMethodService
                .getWhShippingMethodDetailByShippingMethodCode(logisticsCompany);
        if (shippingMethod != null && shippingMethod.getOverduedays() != null && shippingOrderNoSyncTime != null) {
            log.warn("----检查追踪号是否过期-----sfmCode[" + shippingMethod.getCode() + "]追踪号时间["
                    + shippingOrderNoSyncTime + "]");
            Double timeOutDay = shippingMethod.getOverduedays();// 超时时间(天)
            int day = isSmt ? 24 : 4 * 24 ;
            if (timeOutDay > 0) {
                int overHour = DateUtils.getHoursBetween(shippingOrderNoSyncTime, new Date());
                if (overHour > timeOutDay * 24 - day) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public int queryWhApvAndItemListCount(WhApvQueryCondition queryCondition) {
        return whApvDao.queryWhApvAndItemListCount(queryCondition);
    }

    @Override
    public List<WhApv> queryWhApvAndItemList(WhApvQueryCondition queryCondition, Pager pager) {
        if (Integer.valueOf(ApvOrderType.REISSUE_PARTS.intCode()).equals(queryCondition.getShipStatus())) {// 补发配件
            WhWarehouse whWarehouse = whWarehouseService.queryOriginalWhWarehouse(true);
            if (null != whWarehouse) {
                if (Integer.valueOf(WarehousePropertyEnum.HHD.intCode()).equals(whWarehouse.getId())) {
                    queryCondition.setSkuReissueParts(REPAY_SKU+WarehousePropertyEnum.MJ.intCode());
                } else if (Integer.valueOf(WarehousePropertyEnum.MJ.intCode()).equals(whWarehouse.getId())) {
                    queryCondition.setSkuReissueParts(REPAY_SKU+WarehousePropertyEnum.HHD.intCode());
                }
            }
        }

        //导出时不执行这段逻辑
        if (pager != null && queryCondition != null && !queryCondition.isDownload()) {
            int count = whApvDao.queryWhApvAndItemListCount(queryCondition);
            // 导出限制 1000000 不能导出;
//            if (queryCondition != null && queryCondition.isDownload()
//                    && "1".equalsIgnoreCase(queryCondition.getExportType()) && count >= 1000000) {
//                return null;
//            }
//            if ("1".equalsIgnoreCase(queryCondition.getExportType())) {
//                pager.setPageSize(count);
//            }
            pager.setTotalCount(count);

            if (count == 0) {
                return new ArrayList<WhApv>();
            }
        }
        return whApvDao.queryWhApvAndItemList(queryCondition, pager);

    }

    // 查询2020-01-01前历史数据
    @Override
    public List<WhApv> queryWhApvAndItemList2019(WhApvQueryCondition queryCondition, Pager pager) {
        if (Integer.valueOf(ApvOrderType.REISSUE_PARTS.intCode()).equals(queryCondition.getShipStatus())) {// 补发配件
            WhWarehouse whWarehouse = whWarehouseService.queryOriginalWhWarehouse(true);
            if (null != whWarehouse) {
                if (Integer.valueOf(WarehousePropertyEnum.HHD.intCode()).equals(whWarehouse.getId())) {
                    queryCondition.setSkuReissueParts(REPAY_SKU+WarehousePropertyEnum.MJ.intCode());
                } else if (Integer.valueOf(WarehousePropertyEnum.MJ.intCode()).equals(whWarehouse.getId())) {
                    queryCondition.setSkuReissueParts(REPAY_SKU+WarehousePropertyEnum.HHD.intCode());
                }
            }
        }
        if (pager != null) {
            int count = whApvDao.queryWhApvAndItemListCount2019(queryCondition);
            // 导出限制 1000000 不能导出;
            if (queryCondition != null && queryCondition.isDownload()
                    && "1".equalsIgnoreCase(queryCondition.getExportType()) && count >= 1000000) {
                return null;
            }
            if ("1".equalsIgnoreCase(queryCondition.getExportType())) {
                pager.setPageSize(count);
            }
            pager.setTotalCount(count);

            if (count == 0) {
                return new ArrayList<WhApv>();
            }
        }
        return whApvDao.queryWhApvAndItemList2019(queryCondition, pager);
    }


    @Override
    public List<Map<String, Object>> getPcsByTime(Date startTime, Date endTime) {
        return whApvDao.queryLocalPcsByTime(startTime, endTime);
    }

    @Override
    public List<Integer> queryWhApvAndItemListApvIds(WhApvQueryCondition queryCondition) {
        if (Integer.valueOf(ApvOrderType.REISSUE_PARTS.intCode()).equals(queryCondition.getShipStatus())) {// 补发配件
            WhWarehouse whWarehouse = whWarehouseService.queryOriginalWhWarehouse(true);
            if (null != whWarehouse) {
                if (Integer.valueOf(WarehousePropertyEnum.HHD.intCode()).equals(whWarehouse.getId())) {
                    queryCondition.setSkuReissueParts(REPAY_SKU+WarehousePropertyEnum.MJ.intCode());
                } else if (Integer.valueOf(WarehousePropertyEnum.MJ.intCode()).equals(whWarehouse.getId())) {
                    queryCondition.setSkuReissueParts(REPAY_SKU+WarehousePropertyEnum.HHD.intCode());
                }
            }
        }
        return whApvDao.queryWhApvAndItemListApvIds(queryCondition);
    }

    @Override
    public List<WhApv> queryWhApvDeliverOrderStandardWeightList(WhApvQueryCondition queryCondition, Pager pager) {
        if (pager != null) {
            int count = whApvDao.queryWhApvDeliverOrderStandardWeightListCount(queryCondition);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhApv>();
            }
        }
        return whApvDao.queryWhApvDeliverOrderStandardWeightList(queryCondition, pager);

    }

    @Override
    public void calculateApvStandardWeightByApvNo(String apvNo) {
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setApvNo(apvNo);
        query.setStatusToStr("17,18");// 已交运、已装车
        List<WhApv> whApvs = this.queryWhApvDeliverOrderStandardWeightList(query, null);
        if (CollectionUtils.isNotEmpty(whApvs)) {
            calculateApvStandardWeight(whApvs.get(0));
        }
    }

    @Override
    public int calculateApvStandardWeight(WhApv apv) {
        int result = 0;
        apv.calculationStandardWeight();
        // 如果存在历史记录清空
        apv.setApvTrack(null);
        if (apv.getActualWeight() != null && apv.getSkuStandardWeightCount() != null) {
            ApvTrack apvTrack = new ApvTrack();
            apvTrack.setApvNo(apv.getApvNo());
            apvTrack.setStandardWeightCount(apv.getSkuStandardWeightCount());
            apvTrack.setStandardWeightDiff(apv.getSkuStandardWeightDiff());
            apvTrack.setCalculatedStandardWeight(true);
            apvTrack.setStandardWeightFull(apv.isStandardWeightFull());
            apvTrack.setGoodsWeightCount(apv.getSkuGoodsWeightCount());
            apvTrack.setGoodsWeightDiff(apv.getSkuGoodsWeightDiff());

//            Map<String, Double> esSmtShipBillDetailList = esSmtShipBillDetailService.getEsSmtShipBillDetailList(apv.getTrackingNumber());
//            Double bigDecimal = esSmtShipBillDetailList.get(apv.getTrackingNumber());
//            apvTrack.setBillWeightCount(bigDecimal);
//            double actualWeight = apv.getActualWeight()==null?0d:apv.getActualWeight();
//            apvTrack.setBillWeightDiff(actualWeight-(bigDecimal==null?0d:bigDecimal));

            apvTrackService.updateApvTrackByApvNo(apvTrack);
            result = 1;
        }
        return result;
    }

    @Override
    public int updateWhApvStatusByStatus(WhApv entity, Integer status) {
        try {
            int updateWhApvStatusByStatus = whApvDao.updateWhApvStatusByStatus(entity, status);
            return updateWhApvStatusByStatus;
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public ResponseJson pushPackingFinish(WhApv whApv) {

        ResponseJson rsp = new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);

        if (whApv == null || StringUtils.isEmpty(whApv.getApvNo())) {
            rsp.setMessage("参数错误！");
            return rsp;
        }

        try {
            pushApvPack2Oms(whApv);

            rsp.setStatus(StatusCode.SUCCESS);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        return rsp;
    }

    private void pushApvPack2Oms(WhApv whApv) {
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setApvNo(whApv.getApvNo());
        WhApv entity = whApvDao.queryWhApv(query);

        if (null != entity) {
            whApv.setStatus(entity.getStatus());

            SaleUser saleUser = new SaleUser();
            saleUser.setUsername("SYSTEM");
            saleUser.setName("系统");
            try {
                ServletRequestAttributes servletRequest = (ServletRequestAttributes) RequestContextHolder
                        .currentRequestAttributes();
                HttpServletRequest request = servletRequest.getRequest();
                saleUser = (SaleUser) request.getSession().getAttribute("currentUser");
            } catch (Exception e) {
            }

            rabbitmqProducerService.pushApvStatusToOms(saleUser, whApv);
        }
    }

    @Cacheable(value = "PLATFORM_CACHE")
    @Override
    public List<SaleChannel> getSaleChannels() {
        return whApvDao.getSaleChannels();
    }

    @Override
    public Map<String, Object> getApvQuantityByStatus(String warehouse) {
        Map<String, Object> resultMap = new HashMap<>();
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setWarehouseStr(warehouse);
        query.setReadOnly(true);
        List<Map<String, Object>> list = whApvDao.queryWhApvStatusCount(query);
        JSONArray legendData = new JSONArray();
        JSONArray seriesData = new JSONArray();
        for (Map<String, Object> map : list) {
            String name = ApvStatus.build(map.get("status").toString()).getName();
            String count = map.get("statusCount").toString();
            legendData.add(name);
            seriesData.add(JSON.parse("{name: '" + name + "', value: '" + count + "'}"));
        }
        resultMap.put("legendData", legendData);
        resultMap.put("seriesData", seriesData);
        return resultMap;
    }

    @Override
    public Map<String, Object> getApvQuantityByDate(String beginDate, String endDate, String warehouse) {
        Map<String, Object> result = new HashMap<String, Object>();
        // 推送数量
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setFromCreateDate(beginDate);
        query.setToCreateDate(endDate);
        query.setWarehouseStr(warehouse);
        int createQuantity = whApvDao.queryWhApvAndItemListCount(query);
        query.setStatus(2);
        query.setWarehouseStr(warehouse);
        int cancel = whApvDao.queryWhApvAndItemListCount(query);
        result.put("createQuantity", createQuantity - cancel);

        // 交运量
        int deliverQuantity = apvTrackService.getDeliverQuantityByDate(beginDate, endDate, warehouse);
        result.put("deliverQuantity", deliverQuantity);
        return result;
    }

    @Override
    public Map<String, Object> getWmsPushQuantityAndDeliverQuantityByCondition(String beginDate, String endDate,
                                                                               String warehouse) {
        Map<String, Object> resultMap = new HashMap<>();
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setWarehouseStr(warehouse);
        query.setReadOnly(true);
        List<Map<String, Object>> list = apvTrackDao.getWmsPushQuantityAndDeliverQuantityByCondition(beginDate, endDate,
                warehouse);
        JSONArray axisData = new JSONArray();
        JSONArray seriesPushData = new JSONArray();
        JSONArray seriesDeliverData = new JSONArray();
        JSONArray seriesLoadedData = new JSONArray();
        JSONArray subPushAndDeliverAndLoadedData = new JSONArray();
        for (Map<String, Object> map : list) {
            String timeStr = map.get("date").toString();
            String createQty = map.get("createQty").toString();
            String deliverQty = map.get("deliverQty").toString();
            String cancelQty = map.get("cancelQty").toString();
            String loadedQty = map.get("loadedQty").toString();
            Integer pushQty = Integer.parseInt(StringUtils.isEmpty(createQty) ? "0" : createQty)
                    - Integer.parseInt(StringUtils.isEmpty(cancelQty) ? "0" : cancelQty);
            Integer subPushAndDeliverAndLoadedQty = pushQty
                    - Integer.parseInt(StringUtils.isEmpty(deliverQty) ? "0" : deliverQty)
                    - Integer.parseInt(loadedQty == null ? "0" : loadedQty);
            axisData.add(timeStr);
            seriesPushData.add(pushQty);
            seriesDeliverData.add(Integer.parseInt(deliverQty == null ? "0" : deliverQty));
            seriesLoadedData.add(Integer.parseInt(loadedQty == null ? "0" : loadedQty));
            subPushAndDeliverAndLoadedData.add(subPushAndDeliverAndLoadedQty);
        }
        resultMap.put("seriesPushData", seriesPushData);
        resultMap.put("seriesDeliverData", seriesDeliverData);
        resultMap.put("axisData", axisData);
        resultMap.put("subPushAndDeliverAndLoadedData", subPushAndDeliverAndLoadedData);
        resultMap.put("seriesLoadedData", seriesLoadedData);
        return resultMap;
    }

    /**
     * 定时迁移订单数据到历史表 start
     */
    @Override
    public void insertThreeMonthsAgoDataFromApvToHistory() {
        whApvDao.insertThreeMonthsAgoDataFromApvToApvHistory();
        whApvDao.insertThreeMonthsAgoDataFromApvItemToApvItemHistory();
    }

    @Override
    public void deleteThreeMonthsAgoDataFromApvAndApvItem() {
        whApvDao.deleteThreeMonthsAgoDataFromApv();
        whApvDao.deleteThreeMonthsAgoDataFromApvItem();
    }
    // 定时迁移订单数据到历史表 end

    @Override
    public List<WhRecord> countApvInventory(List<WhRecord> list) {
        return whApvDao.countApvInventory(list);
    }

    @Override
    public List<WhApv> queryAllotStockApv(WhApvQueryCondition query, Pager pager) {
        return whApvDao.queryAllotStockApv(query, pager);
    }

    @Override
    public int[] updateApvStatusByPrimaryKey(List<WhApv> whApvs) {
        return whApvDao.updateApvStatusByPrimaryKey(whApvs);
    }

    @Override
    public List<WhApv> queryPushOrderConditionList(WhApvQueryCondition query) {
        return whApvDao.queryPushOrderConditionList(query);
    }

    @Override
    public List<WhApv> queryWhApvAndItemListForPickingByTaskId(Integer taskId) {
        return whApvDao.queryWhApvAndItemListForPickingByTaskId(taskId);
    }

    @Override
    public WhApv querySecurityApvByOrderNo(String orderNo) {
        return whApvDao.querySecurityApvByOrderNo(orderNo);
    }

    @Override
    public Boolean updateWhApvTrackingNumberById(Integer apvId) {
        Boolean result = true;
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setId(apvId);
        WhApv apv = queryWhApv(query);

        if (apv.getApvNo().equals(apv.getTrackingNumber())) {
            result = false;
            try {
                String url = CacheUtils.SystemParamGet("TMS_PARAM.SYNC_TRACKING_NUMBER_URL").getParamValue();
                ApiResult apiResult = HttpUtils.get(url + apv.getApvNo(), "", ApiResult.class);
                if (apiResult.isSuccess()) {
                    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(apiResult.getResult()));
                    String trackingNumber = jsonObject.get("shippingOrderNo").toString();
                    if (StringUtils.isNotBlank(trackingNumber)) {
                        apv.setTrackingNumber(trackingNumber);
                        apv.setShipmentTime(new Timestamp(System.currentTimeMillis()));
                        whApvDao.updateWhApv(apv);
                        result = true;
                    }
                }
            } catch (Exception e) {
                log.error("同步TMS追踪号失败，apvNo=" + apv.getApvNo(), e);
            }
        }

        return result;
    }

    @Override
    public int updateWhApvPickQuantity(List<Integer> idList) {
        return whApvDao.updateWhApvPickQuantity(idList);
    }

    @Override
    public String allotAndCancelByApvIds(List<Integer> apvIds) {
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setApvIds(apvIds);
        query.setMoveStatus(true); // 移动状态查询缩减字段优化速度
        List<WhApv> whApvs = whApvDao.queryWhApvAndItemList(query, null);

        if (CollectionUtils.isEmpty(whApvs)) {
            return "! 取消发货条数：0";
        }
        List<WhApv> updateApvs = new ArrayList<>();
        for (WhApv whApv : whApvs) {
            whApv.setSignPayment(false);
            if ((!ApvStatus.WAITING_ALLOT.equals(whApv.getStatus()) && !ApvStatus.STOCKOUT_NOT.equals(whApv.getStatus()))
                    || CollectionUtils.isEmpty(whApv.getWhApvItems()) || whApv.existBuyerCheckout(ApvTaxTypeEnum.INTERCEPTOR)) {
                log.warn(whApv.getApvNo() + "该订单不能匹配库存，订单被拦截或者已被更改!");
            } else {
                updateApvs.add(whApv);
            }
        }
        ApvAllotUpdateStockService apvAllotUpdateStockService = SpringUtils.getBean(ApvAllotUpdateStockService.class);
        int cancel = 0;
        if (CollectionUtils.isNotEmpty(updateApvs)) {
            // 统计SKU并查询库存
            Set<String> localSkus = new HashSet<>();
            for (WhApv apv : updateApvs) {
                for (WhApvItem item : apv.getWhApvItems()) {
                        localSkus.add(item.getSku());
                }
            }
            Map<String, Integer> stockMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(localSkus)) {
                WhStockQueryCondition querySku = new WhStockQueryCondition();
                querySku.setSkus(new ArrayList<>(localSkus));
                List<WhStock> whStocks = whStockService.queryWhStocks(querySku, null);
                if (CollectionUtils.isNotEmpty(whStocks)) {
                    // 根据SKU转Map对象
                    Map<Integer, WhStock> whStockMap = whStocks.stream().collect(Collectors.toMap(WhStock::getId, o -> o, (o1, o2) -> o2));
                    FrozenStockQueryCondition frozenQuery = new FrozenStockQueryCondition();
                    frozenQuery.setStockIdList(whStockMap.keySet().stream().collect(Collectors.toList()));
                    List<FrozenStock> frozenStocks = frozenStockService.queryFrozenStocks(frozenQuery, null);
                    Map<Integer, Integer> frozenStockMap = frozenStocks.stream().collect(Collectors
                            .toMap(FrozenStock::getStockId, FrozenStock::getFrozenQuantity, (o1, o2) -> o2));
                    for (Integer id : whStockMap.keySet()) {
                        WhStock whStock = whStockMap.get(id);
                        if (whStock != null) {
                            // 可用、取消、已拣返架、拣货缺货、冻结
                            Integer surplusQuantity = whStock.getSurplusQuantity() == null ? 0 : whStock.getSurplusQuantity();
                            Integer cancelQuantity = whStock.getCancelQuantity() == null ? 0 : whStock.getCancelQuantity();
                            Integer pickReturn = whStock.getPickReturnQuantity() == null ? 0 : whStock.getPickReturnQuantity();
                            Integer pickNot = whStock.getPickNotQuantity() == null ? 0 : whStock.getPickNotQuantity();
                            Integer frozenQuantity = frozenStockMap.get(id) == null ? 0 : frozenStockMap.get(id);
                            // TODO 冻结库存
                            Integer existQty = stockMap.get(whStock.getSku()) == null ? 0 : stockMap.get(whStock.getSku());
                            stockMap.put(whStock.getSku(), (existQty + surplusQuantity + cancelQuantity + pickReturn + pickNot + frozenQuantity));
                        }
                    }
                }
            }

            for (WhApv apv : updateApvs) {
                boolean normal = true;
                for (WhApvItem item : apv.getWhApvItems()) {
                    // 本仓才判断库位
                    if (StringUtils.isNotBlank(item.getWhSku().getLocationNumber())) {
                        String[] skuLocationArry = StringUtils.split(item.getWhSku().getLocationNumber(), ",");
                        boolean allElementsExist = Arrays.stream(skuLocationArry)
                                .allMatch(element -> Arrays.asList(Constant.locations).contains(element));
                        if (allElementsExist) {
                            log.warn("异常订单，不允许匹配！apvNo：" + apv.getApvNo());
                            normal = false;
                            // TODO 不处理
                            SystemLogUtils.APVLOG.log(apv.getId(), "匹配待返架库存失败", new String[][]{{"原因", "异常订单:" + item.getSku()}});
                            break;
                        }
                    }

                    if (stockMap.get(item.getSku()) == null) {
                        log.warn("异常订单，" + item.getSku() + "查不到库存不允许匹配！apvNo：" + apv.getApvNo());
                        normal = false;
                        // TODO 不处理
                        SystemLogUtils.APVLOG.log(apv.getId(), "匹配待返架库存失败", new String[][]{{"原因", item.getSku() + "查不到库存"}});
                        break;
                    }
                }
                if (normal) {
                    boolean enough = true;
                    for (WhApvItem item : apv.getWhApvItems()) {
                        Integer quantity = stockMap.get(item.getSku());
                        Integer saleQuantity = item.getSaleQuantity();
                        if (quantity < saleQuantity) {
                            enough = false;
                            log.warn("订单检查 SKU: " + item.getSku() + ", 匹配库存失败! afterSurplus: " + quantity);
                            break;
                        }
                    }
                    if (enough) {
                        for (WhApvItem item : apv.getWhApvItems()) {
                            Integer quantity = stockMap.get(item.getSku());
                            Integer saleQuantity = item.getSaleQuantity();
                            stockMap.put(item.getSku(), quantity - saleQuantity);
                        }
                        // TODO 匹配待返架成功
                        //满足数量的订单则提示分配不成功，待返架库存
                        SystemLogUtils.APVLOG.log(apv.getId(), "匹配库存失败", new String[][]{{"原因", "待返架库存"}});
                    } else {
                        // TODO 取消发货
                        apv.setCancelReason("SKU匹配库存缺货");
                        apvAllotUpdateStockService.cancelApv(apv);
                        cancel++;
                    }
                }
            }
        }
        return "! 取消发货条数：" + cancel;
    }

    @Override
    public List<Integer> queryCurrentTaskNormal(String taskNo) {
        if (StringUtils.isNotBlank(taskNo)) {
            return whApvDao.queryCurrentTaskNormal(taskNo);
        }
        return null;
    }

    @Override
    public List<Integer> queryCurrentReXiaoTaskNormal(String taskNo) {
        if (StringUtils.isNotBlank(taskNo)) {
            return whApvDao.queryCurrentReXiaoTaskNormal(taskNo);
        }
        return null;
    }

    /**
     * 优选仓分配库存
     */
    @Override
    public void pacStockAllotWhApv() {
        WhApvQueryCondition query = new WhApvQueryCondition();
        // 待分配 缺货
        query.setStatusList(Arrays.asList(ApvStatus.STOCKOUT_NOT.intCode(), ApvStatus.WAITING_ALLOT.intCode()));
        query.setOrderBy(ApvOrderBy.CREATE_DATE_ASC.intCode());
        // query.setApvNo("YSTNLBX0327427786646392");
        query.setShipStatusList(ApvOrderType.getOptimalIntCode());
        try {
            batchAllotWhApv(query, null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public WhApv matchPacOrderInfoList(WhApvOrderInfoDTO whApvOrderDTO) {

        if (StringUtils.isBlank(whApvOrderDTO.getPlatformOrderId())
                || CollectionUtils.isEmpty(whApvOrderDTO.getSkuInfoList())
                || whApvOrderDTO.getInterceptor() == null) {
            throw new RuntimeException("参数异常");
        }
        //去重、过滤平台单号或SKU或SKU数量为空的数据
        List<WhApvSkuInfoDTO> skuInfoList = whApvOrderDTO.getSkuInfoList();
        boolean bool = skuInfoList.stream()
                .anyMatch(a -> StringUtils.isBlank(a.getSku()) || a.getSaleQuantity() == null);
        if (bool) {
            throw new RuntimeException("sku、sku数量不能为空！");
        }

        Map<String, WhApvSkuInfoDTO> stringWhApvSkuInfoDTOMap = whApvOrderDTO.buildGroupSkuInfos();
        if (stringWhApvSkuInfoDTOMap == null) {
            throw new RuntimeException("sku、sku数量不能为空！");
        }
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setPlatformOrderId(whApvOrderDTO.getPlatformOrderId());
        query.setSkuList(new ArrayList(stringWhApvSkuInfoDTOMap.keySet()));
        query.setMoveStatus(true); // 移动状态查询缩减字段优化速度
        List<WhApv> whApvList = whApvDao.queryWhApvAndItemList(query, null);

        if (CollectionUtils.isEmpty(whApvList)) {
            //优选仓订单仓库取消后平台单号后加后缀，需要要模糊查询，取消订单需返回成功
            WhApvQueryCondition queryCondition = new WhApvQueryCondition();
            queryCondition.setShipStatusList(Arrays.asList(ApvOrderType.OPTIMAL.intCode(), ApvOrderType.OPTIMAL_JB.intCode()));
            queryCondition.setFuzzyPlatformOrderId(whApvOrderDTO.getPlatformOrderId());
            query.setSkuList(new ArrayList(stringWhApvSkuInfoDTOMap.keySet()));
            query.setMoveStatus(true); // 移动状态查询缩减字段优化速度
            whApvList = whApvDao.queryWhApvAndItemList(queryCondition, null);
        }

        if (CollectionUtils.isEmpty(whApvList)) throw new RuntimeException("对应发货单数据不存在！");
        ;

        for (WhApv whApv : whApvList) {
            boolean allMatch = whApv.getWhApvItems().stream().allMatch(i ->
                    stringWhApvSkuInfoDTOMap.get(i.getSku()) != null
                            && stringWhApvSkuInfoDTOMap.get(i.getSku()).getSaleQuantity().equals(i.getSaleQuantity())
            );
            if (allMatch) return whApv;
        }
        throw new RuntimeException("未匹配到待发货单据！");
    }

    //
    @Override
    public void batchInterceptorWhApv(WhApv whApv, Boolean isInterceptor, String interceptorType) {
        if (whApv == null || CollectionUtils.isEmpty(whApv.getWhApvItems())) {
            throw new RuntimeException("满足条件数据不存在！");
        }
        whApv.setSignPayment(false);
        // 退库存并改状态
        List<WhApv> whApvList = new ArrayList<>();
        if (isInterceptor) {
            //优选仓.发货拦截取消订单
            if (whApv.getShipStatus() != null
                    && ApvOrderType.getOptimalIntCode().contains(whApv.getShipStatus())
                    && StringUtils.equalsIgnoreCase(ApvTaxTypeEnum.INTERCEPTOR.getCode(), interceptorType)) {
                apvStatusUpdateService.cancel(whApv);
                if (!PacSendUtil.wmsOrderStatusUpload1(StringUtils.substring(whApv.getApvNo(), 4),
                        PacOrderUploadStatus.WMS_REJECT.getCode(), false)) {
                    throw new RuntimeException("调优选仓拒单接口失败！稍后重试");
                }
                SystemLogUtils.APVLOG.log(whApv.getId(), "拦截订单成功");
                return;
            }
            //其他
               /* if (!ApvStatus.WAITING_ALLOT.equals(whApv.getStatus())) {
                    try {
                        String result = apvWaitingAllotUpdateStockService.waitingAllot(whApv);
                        if (StringUtils.isNotBlank(result)) {
                           // SystemLogUtils.APVLOG.log(whApv.getId(), "拦截订单失败：" + result);
                            throw new RuntimeException(result);
                        }
                    } catch (Exception e) {
                        log.error(whApv.getApvNo()+"拦截订单失败："+e.getMessage(),e );
                        SystemLogUtils.APVLOG.log(whApv.getId(), "拦截订单失败：" + e.getMessage());
                        throw new RuntimeException(e.getMessage());
                    }
                }*/
            if (whApv.addBuyerCheckout(ApvTaxTypeEnum.build(interceptorType))) {
                WhApv updateWhApv = new WhApv();
                updateWhApv.setId(whApv.getId());
                updateWhApv.setBuyerCheckout(whApv.getBuyerCheckout());
                updateWhApv.setInterceptor(true);
                whApvList.add(updateWhApv);
                SystemLogUtils.APVLOG.log(whApv.getId(), "拦截订单成功");
            }
        } else {
            if (whApv.remBuyerCheckout(ApvTaxTypeEnum.INTERCEPTOR)
                    || whApv.remBuyerCheckout(ApvTaxTypeEnum.QC_INTERCEPTOR)) {
                WhApv updateWhApv = new WhApv();
                updateWhApv.setId(whApv.getId());
                updateWhApv.setBuyerCheckout(whApv.getBuyerCheckout());
                updateWhApv.setInterceptor(true);
                whApvList.add(updateWhApv);
                SystemLogUtils.APVLOG.log(whApv.getId(), "取消订单拦截");
            }
        }
        whApvDao.updateApvStatusByPrimaryKey(whApvList);
    }


    @Override
    public List<Map<String, Object>> queryStandardWeightDistribution(WhApvQueryCondition whApvQueryCondition) {
        whApvQueryCondition.setStatusToStr("17,18");// 已交运、已装车
        int count = whApvDao.queryDistributionApvCount(whApvQueryCondition);
        if (count == 0) {
            return new ArrayList<>();
        }

        if (Objects.nonNull(whApvQueryCondition.getDistributionType())) {
            Integer distributionType = whApvQueryCondition.getDistributionType();
            if (distributionType == 1 || distributionType == 2) {
                List<Map<String, Object>> records = whApvDao.queryDistribution(whApvQueryCondition, null);
                if (CollectionUtils.isEmpty(records)) {
                    return new ArrayList<>();
                }
                this.statisticDistribution(records);
                return records;
            }

            if (distributionType == 3) {
                List<Integer> ids = whApvDao.queryAllTrackId(whApvQueryCondition);
                if (CollectionUtils.isEmpty(ids)) {
                    return new ArrayList<>();
                }
                // 用于存储分页汇总结果
                List<Map<String, Object>> result = new ArrayList<>();
                List<Future<?>> futureList = new ArrayList<>();
                for (List<Integer> idList : Lists.partition(ids, SCROLL_NUMBER)) {
                    Future<?> future = distributionExecutors.submit(() -> {
                        WhApvQueryCondition queryCondition = new WhApvQueryCondition();
                        BeanUtils.copyProperties(whApvQueryCondition, queryCondition);
                        queryCondition.setApvIds(idList);
                        List<Map<String, Object>> records = whApvDao.queryDistribution(queryCondition, null);
                        if (CollectionUtils.isEmpty(records)) {
                            return;
                        }
                        List<Map<String, Object>> tempResult = this.convergeDistribution(records);
                        this.aggregateResultList(tempResult, result);
                    });
                    futureList.add(future);
                }
                for (Future<?> future : futureList) {
                    try {
                        future.get();
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        Thread.currentThread().interrupt();
                    }
                }
                return result;
            }
        }

        return new ArrayList<>();
    }

    /**
     * @param additionalList 当次查询数据
     * @param resultList     汇总结果
     */
    private void aggregateResultList(List<Map<String, Object>> additionalList, List<Map<String, Object>> resultList) {
        if (CollectionUtils.isEmpty(additionalList)) {
            return;
        }

        if (CollectionUtils.isEmpty(resultList)) {
            synchronized (this) {
                if (CollectionUtils.isEmpty(resultList)) {
                    resultList.addAll(additionalList);
                    return;
                }
            }
        }

        // 处理非行统计和列统计的值。行统计和列统计的Map对象，其有特殊的地方在于存在rowSum以及colSum的key
        Map<String, Object> resultMap = resultList.stream()
                .filter(v -> !v.containsKey("rowSum") && !v.containsKey("colSum"))
                .map(v -> v.entrySet())
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(v -> v.getKey(), v -> v.getValue()));

        List<Map.Entry<String, Object>> additionalEntryList = additionalList.stream()
                .filter(v -> !v.containsKey("rowSum") && !v.containsKey("colSum"))
                .map(v -> v.entrySet())
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        for (Map.Entry<String, Object> entry : additionalEntryList) {
            String key = entry.getKey();
            LongAdder val = (LongAdder) entry.getValue();
            ((LongAdder) resultMap.get(key)).add(val.sum());
        }
        // 处理统计行和统计列
        resultMap = resultList.stream()
                .filter(v -> v.containsKey("rowSum"))
                .map(v -> v.entrySet())
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(v -> v.getKey(), v -> v.getValue()));
        additionalEntryList = additionalList.stream()
                .filter(v -> v.containsKey("rowSum"))
                .map(v -> v.entrySet())
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        for (Map.Entry<String, Object> entry : additionalEntryList) {
            String key = entry.getKey();
            LongAdder val = (LongAdder) entry.getValue();
            ((LongAdder) resultMap.get(key)).add(val.sum());
        }

        resultMap = resultList.stream()
                .filter(v -> v.containsKey("colSum"))
                .map(v -> v.entrySet())
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(v -> v.getKey(), v -> v.getValue()));
        additionalEntryList = additionalList.stream()
                .filter(v -> v.containsKey("colSum"))
                .map(v -> v.entrySet())
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        for (Map.Entry<String, Object> entry : additionalEntryList) {
            String key = entry.getKey();
            LongAdder val = (LongAdder) entry.getValue();
            ((LongAdder) resultMap.get(key)).add(val.sum());
        }
    }

    @Override
    public void downloadDistribution(WhApvQueryCondition whApvQueryCondition) {
        List<Map<String, Object>> data = this.queryStandardWeightDistribution(whApvQueryCondition);
        if (CollectionUtils.isEmpty(data)) {
            log.info("无分布相关数据!");
            return;
        }
        long startTime = System.currentTimeMillis();
        List<List<String>> exportList = new ArrayList<>();
        File file = null;
        String path = EasyExcelUtils.EXPORT_BASE_PATH;
        String fileName = "标准重量差分布" + System.currentTimeMillis() + ".xlsx";
        ExcelWriter excelWriter = null;
        Integer distributionType = whApvQueryCondition.getDistributionType();
        String[] headers = this.getDistributionFileHeaders(data, distributionType);
        try {
            file = new File(path + "/" + fileName);
            excelWriter = EasyExcel.write(fileName).head(EasyExcelUtils.getHead(headers)).build();

            log.warn("download file name: " + fileName);

            //转化为list
            exportList = getExportList(data, headers, distributionType);

            //写入第一页
            WriteSheet writeSheet = EasyExcel.writerSheet("第1页").build();
            excelWriter.write(exportList, writeSheet);
            log.warn("---task execute end 耗时：---" + (System.currentTimeMillis() - startTime) / 1000);
        } catch (Exception e) {
            log.warn(e.getMessage());
        } finally {
            //关流
            if (excelWriter != null) {
                excelWriter.finish();
            }

            try {
                if (file != null) {
                    //上传文件服务器 & 发送webSocket消息
                    log.warn("本地暂存文件路径================>" + file.getPath());
                    FtpUtils.exportFileForSocket(file, IdGenerateUtils.nextId());
                }
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    /*
    * 可重复导，最终一致性*/
    @Override
    public ResponseJson doImportTrackingNo(String[] titles, List<Integer> expressIntCode, MultipartFile multiPartFile) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        ResultModel<WhApv> resultModel;
        try {
            resultModel = POIUtils.readExcel(titles, multiPartFile, row -> {
                int cellnum = 0;
                WhApv whApv = new WhApv();
                whApv.setSalesRecordNumber(POIUtils.cellValue2Str(row.getCell(cellnum++)));
                whApv.setApvNo(POIUtils.cellValue2Str(row.getCell(cellnum++)));
                whApv.setTrackingNumber(POIUtils.cellValue2Str(row.getCell(cellnum++)));
                return whApv;
            }, false);

            if (resultModel.isSuccess()) {
                for (WhApv whApv : resultModel.getList()) {
                    String apvNo = whApv.getApvNo();
                    String salesRecordNumber = whApv.getSalesRecordNumber();
                    String trackingNumber = whApv.getTrackingNumber();
                    if (StringUtils.isBlank(apvNo) && StringUtils.isBlank(salesRecordNumber)) {
                        continue;
                    }
                    if (StringUtils.isBlank(trackingNumber)) {
                        continue;
                    }
                    WhApvQueryCondition query = new WhApvQueryCondition();
                    query.setSalesRecordNumber(salesRecordNumber);
                    query.setApvNo(apvNo);
                    query.setExpress(true);
                    List<WhApv> apvList = queryWhApvAndItemList(query, null);
                    if (CollectionUtils.isEmpty(apvList)) {
                        continue;
                    }
                    WhApv queryWhApv = apvList.get(0);
                    if (null != queryWhApv) {
                        // 大包同步更新apv_express表信息
                        if (ApvOrderType.BALE.intCode() == queryWhApv.getShipStatus()){
                            ApvExpress apvExpress = queryWhApv.getApvExpress();
                            if (apvExpress != null) {
                                ApvExpress updateApvExpress = new ApvExpress();
                                updateApvExpress.setId(apvExpress.getId());
                                updateApvExpress.setTrackingNumber(whApv.getTrackingNumber());
                                updateApvExpress.setShippingOrderNo(whApv.getTrackingNumber());
                                apvExpressService.updateApvExpress(updateApvExpress);
                            }
                        }
                        whApv.setId(queryWhApv.getId());
                        whApv.setSalesRecordNumber(null);
                        whApv.setApvNo(null);
                        boolean result = updateWhApv(whApv);

                        if (result && "OMS".equals(queryWhApv.getShipService())
                                && expressIntCode.contains(queryWhApv.getShipStatus())
                                && StringUtils.isNotBlank(queryWhApv.getApvNo())
                                && StringUtils.isNotBlank(trackingNumber)) {
                            log.warn("快递类型出库单追踪号推送到OMS: apvNo:" + queryWhApv.getApvNo() + " -- 订单类型:"
                                    + queryWhApv.getShipStatus() + " --- trackingNumber:"
                                    + whApv.getTrackingNumber());
                            rabbitmqProducerService.pushDomesticExpressNo(queryWhApv, trackingNumber);
                        }
                    }
                }
                response.setStatus(StatusCode.SUCCESS);
            } else {
                response.setMessage(resultModel.getMsg());
                return response;
            }
        } catch (Exception e) {
            response.setMessage(e.getMessage());
            return response;
        }
        return response;
    }

    /**
     * 用于得到分布数据的标题
     *
     * @param data             分布数据对象
     * @param distributionType 分布类型
     * @return 分布数据的标题字符集
     */
    private String[] getDistributionFileHeaders(List<Map<String, Object>> data, Integer distributionType) {
        if (CollectionUtils.isEmpty(data)) {
            return new String[0];
        }
        List<String> headers = new ArrayList<>();
        String distributionTypeStr = "";
        if (distributionType == 1) {
            distributionTypeStr = "标准重量差";
        } else if (distributionType == 2) {
            distributionTypeStr = "标准重量差百分比";
        } else if (distributionType == 3) {
            distributionTypeStr = "标准重量差\\标准重量差百分比";
        }
        headers.add(distributionTypeStr);
        if (distributionType == 1 || distributionType == 2) {
            for (Map<String, Object> record : data) {
                Object header = record.get("groupDay");
                headers.add(String.valueOf(header) + "(订单数)");
                headers.add(String.valueOf(header) + "(占比)");
            }
        } else if (distributionType == 3) {
            for (int i = 1; i <= 20; i++) {
                String header = "(";
                if (i == 1) {
                    header = "[";
                }
                header += String.valueOf((i - 1) * 5) + "%," + String.valueOf(i * 5) + "%" + "]";
                headers.add(header);
            }
            headers.add("(100%, ∞)");
            headers.add("合计");
        }

        return headers.toArray(new String[headers.size()]);
    }


    /**
     * 用于将记录转化为数据导出对象
     *
     * @param records          记录对象
     * @param selectHeaders    导出的标题项
     * @param distributionType 分布类型
     * @return 数据导出对象结果集
     */
    private List<List<String>> getExportList(List<Map<String, Object>> records, String[] selectHeaders,
                                             Integer distributionType) {
        if (CollectionUtils.isEmpty(records) || ArrayUtils.isEmpty(selectHeaders)) {
            log.info("无需要导出数据!");
            return new ArrayList<>();
        }

        List<List<String>> data = new ArrayList<>();

        if (distributionType == 1 || distributionType == 2) {
            for (int i = 1; i <= 21; i++) {
                List<String> exportLine = new ArrayList<>();
                Integer start = (i - 1) * 5;
                Integer end = i * 5;
                // 标准分布差该行的值
                String distribution = "(";
                if (i == 1) {
                    distribution = "[";
                }
                distribution += start;
                if (distributionType == 2) {
                    distribution += "%";
                }
                distribution += ",";
                if (i != 21) {
                    distribution += end;
                } else {
                    distribution += "∞";
                }
                if (distributionType == 2 && i != 21) {
                    distribution += "%";
                }
                if (i != 21) {
                    distribution += "]";
                } else {
                    distribution += ")";
                }
                exportLine.add(distribution);

                String key = start + "_";
                if (i != 21) {
                    key += end;
                }
                final String percentage = "_percentage";
                for (Map<String, Object> item : records) {
                    Object orderCount = item.get(key);
                    if (Objects.nonNull(orderCount)) {
                        exportLine.add(POIUtils.transferObj2Str(orderCount));
                    }
                    Object percentageObj = item.get(key + percentage);
                    if (Objects.nonNull(percentageObj)) {
                        exportLine.add(POIUtils.transferObj2Str(percentageObj));
                    }
                }
                data.add(exportLine);
            }

            //末尾行合计数据
            List<String> exportLine = new ArrayList<>();
            exportLine.add("合计");
            for (Map<String, Object> item : records) {
                Object count = item.get("count");
                if (Objects.nonNull(count)) {
                    exportLine.add(POIUtils.transferObj2Str(count));
                }
                Object countPercentage = item.get("count_percentage");
                if (Objects.nonNull(countPercentage)) {
                    exportLine.add(POIUtils.transferObj2Str(countPercentage));
                }
            }
            data.add(exportLine);
        } else if (distributionType == 3) {

            Map<String, Object> colMap = null;
            for (int i = 1; i <= 21; i++) {
                List<String> exportLine = new ArrayList<>();
                String leftStandardWeightDiffStr = String.valueOf((i - 1) * 5);
                String rightStandardWeightDiffStr = "";
                if (i != 21) {
                    rightStandardWeightDiffStr = String.valueOf(i * 5);
                }
                String rangeKey = leftStandardWeightDiffStr;
                if (StringUtils.isNotBlank(rightStandardWeightDiffStr)) {
                    rangeKey += "_" + rightStandardWeightDiffStr;
                }

                // 用于得到第一列的值
                String col = "(";
                if (i == 1) {
                    col = "[";
                }
                col += leftStandardWeightDiffStr + ",";
                if (StringUtils.isNotBlank(rightStandardWeightDiffStr)) {
                    col += rightStandardWeightDiffStr + "]";
                } else {
                    col += "∞)";
                }
                exportLine.add(POIUtils.transferObj2Str(col));

                // 用于得到竖着的合计行
                Map<String, Object> rowMap = null;
                for (int j = 1; j <= 21; j++) {
                    String leftStandardWeightDiffPercentageStr = String.valueOf((j - 1) * 5);
                    String rightStandardWeightDiffPercentageStr = "";
                    if (j != 21) {
                        rightStandardWeightDiffPercentageStr = String.valueOf(j * 5);
                    }

                    String rangeKeys = rangeKey + "_" + leftStandardWeightDiffPercentageStr + "_";
                    if (StringUtils.isNotBlank(rightStandardWeightDiffPercentageStr)) {
                        rangeKeys += rightStandardWeightDiffPercentageStr;
                    }

                    for (Map<String, Object> record : records) {
                        if (record.containsKey(rangeKeys)) {
                            exportLine.add(POIUtils.transferObj2Str(record.get(rangeKeys)));
                        }
                        if (record.containsKey("rowSum")) {
                            rowMap = record;
                        }
                        if (record.containsKey("colSum")) {
                            colMap = record;
                        }
                    }
                }
                if (Objects.nonNull(rowMap)) {
                    if (Objects.equals(rangeKey, "100")) {
                        rangeKey = "100_";
                    }
                    exportLine.add(POIUtils.transferObj2Str(rowMap.get(rangeKey)));
                }
                data.add(exportLine);
            }

            // 用于最后一行合计的值
            List<String> exportLine = new ArrayList<>();
            exportLine.add(POIUtils.transferObj2Str("合计"));
            for (int i = 1; i <= 21; i++) {
                String leftStandardWeightDiffStr = String.valueOf((i - 1) * 5);
                String rightStandardWeightDiffStr = "";
                if (i != 21) {
                    rightStandardWeightDiffStr = String.valueOf(i * 5);
                }
                String rangeKey = leftStandardWeightDiffStr + "_";
                if (StringUtils.isNotBlank(rightStandardWeightDiffStr)) {
                    rangeKey += rightStandardWeightDiffStr;
                }
                exportLine.add(POIUtils.transferObj2Str(colMap.get(rangeKey)));
            }
            exportLine.add(POIUtils.transferObj2Str(colMap.get("colSum")));
            data.add(exportLine);
        }

        return data;
    }

    /**
     * 用于汇总和统计订单分布情况
     *
     * @param records
     */
    private List<Map<String, Object>> convergeDistribution(List<Map<String, Object>> records) {

        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }

        // key为行的区间范围，value为对应的行
        Map<String, Map<String, Object>> resultMap = new HashMap<>(21);

        // 行合计以及列合计
        Map<String, Object> rowCount = new HashMap<>(21);
        Map<String, Object> colCount = new HashMap<>(21);


        // 此处先生成441个统计范围
        // i为标准重量差范围
        for (int i = 1; i <= 21; i++) {
            Map<String, Object> countMap = new HashMap<>(21);

            String leftStandardWeightDiffStr = String.valueOf((i - 1) * 5);
            String rightStandardWeightDiffStr = "";
            if (i != 21) {
                rightStandardWeightDiffStr = String.valueOf(i * 5);
            }
            String rangeKey = leftStandardWeightDiffStr;
            String rowKey = leftStandardWeightDiffStr + "_";
            if (StringUtils.isNotBlank(rightStandardWeightDiffStr)) {
                rangeKey += "_" + rightStandardWeightDiffStr;
                rowKey += rightStandardWeightDiffStr;
            }

            // j为标准重量差百分比范围
            for (int j = 1; j <= 21; j++) {
                String leftStandardWeightDiffPercentageStr = String.valueOf((j - 1) * 5);
                String rightStandardWeightDiffPercentageStr = "";
                if (j != 21) {
                    rightStandardWeightDiffPercentageStr = String.valueOf(j * 5);
                }
                String rangeKeys = rangeKey + "_" + leftStandardWeightDiffPercentageStr + "_";
                String colKey = leftStandardWeightDiffPercentageStr + "_";
                if (StringUtils.isNotBlank(rightStandardWeightDiffPercentageStr)) {
                    rangeKeys += rightStandardWeightDiffPercentageStr;
                    colKey += rightStandardWeightDiffPercentageStr;
                }
                countMap.put(rangeKeys, new LongAdder());
                rowCount.put(rowKey, new LongAdder());
                colCount.put(colKey, new LongAdder());
            }
            resultMap.put(rowKey, countMap);
        }

        // 统计各个值落入的区间
        List<Future<?>> futures = new ArrayList<>();
        BigDecimal oneHundred = new BigDecimal(100);
        int size = Math.max(records.size() / 100000, 1);
        for (List<Map<String, Object>> recordList : Lists.partition(records, size)) {
            Future<?> result = countExecutors.submit(new Runnable() {
                @Override
                public void run() {
                    for (Map<String, Object> record : recordList) {
                        BigDecimal standardWeightDiff = new BigDecimal(String.valueOf(record.get("standard_weight_diff")));
                        BigDecimal standardWeightDiffPercentage = new BigDecimal(String.valueOf(record.get("standard_weight_diff_percentage")));
                        String rowKey = getKey(standardWeightDiff);
                        String colKey = getKey(standardWeightDiffPercentage.multiply(oneHundred));
                        String rangeKey = rowKey + "_" + colKey;
                        if ("100_".equals(rowKey)) {
                            rangeKey = rowKey + colKey;
                        }
                        // 对应的行
                        Map<String, Object> countMap = resultMap.get(rowKey);

                        LongAdder count = (LongAdder) countMap.get(rangeKey);
                        count.increment();

                        LongAdder row = (LongAdder) rowCount.get(rowKey);
                        row.increment();

                        LongAdder col = (LongAdder) colCount.get(colKey);
                        col.increment();
                    }
                }
            });
            futures.add(result);
        }

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error(e.getMessage(), e);
                Thread.currentThread().interrupt();
            }
        }

        List<Map<String, Object>> result = new ArrayList<>(resultMap.values());


        // 计算合计总数值
        LongAdder sum = new LongAdder();
        for (Object num : colCount.values()) {
            sum.add(((LongAdder) num).longValue());
        }
        colCount.put("colSum", sum);

        LongAdder sums = new LongAdder();
        for (Object num : rowCount.values()) {
            sums.add(((LongAdder) num).longValue());
        }
        rowCount.put("rowSum", sums);

        result.add(colCount);
        result.add(rowCount);
        return result;
    }

    /**
     * 通过传入的重量差的值，得到重量差所在区间的key，区间与区间之间用"_"分隔开
     * 1. 先判断等于0和大于100的特殊情况，如果满足条件，则直接返回
     * 2. 四舍五入得到重量差的整数值roundNumber，将该值除以5得到val，如果重量差整数值roundNumber能够被5整除，
     * 则目前暂定认为其处于val * 5 的闭区间范围内(即 val * 5 为右区间)，之后用重量差的值与整数值roundNumber进行对比。
     * 如果相等，说明落在整数值val * 5 为闭合的区间范围内(即 val * 5 为右区间)
     * 如果大于，说明落在整数值val * 5 为开区间的范围内(即 val * 5 为左区间)
     * 如果不能被5直接整除，直接落入以val * 5 为开区间的范围内
     * ps: 没有小于的可能
     *
     * @param diff 重量差的值，其数字分布在0-无穷之间
     * @return 重量差所在区间的key
     */
    private String getKey(BigDecimal diff) {
        if (Objects.isNull(diff)) {
            return null;
        }
        boolean equalsZero = diff.compareTo(BigDecimal.ZERO) == 0;
        if (equalsZero) {
            return "0_5";
        }

        boolean greatThanOneHundred = diff.compareTo(BigDecimal.TEN.multiply(BigDecimal.TEN)) >= 0;
        if (greatThanOneHundred) {
            return "100_";
        }

        // 整数部分的值
        BigDecimal roundDownNumber = diff.setScale(0, RoundingMode.DOWN);
        Integer roundDownNumberVal = roundDownNumber.intValue();

        Integer val = roundDownNumberVal / 5;
        boolean equals = diff.compareTo(roundDownNumber) == 0;

        if ((roundDownNumberVal % 5 == 0) && equals) {
            return String.valueOf((val - 1) * 5) + "_" + (val * 5);
        }

        return String.valueOf((val * 5)) + "_" + String.valueOf((val + 1) * 5);
    }

    /**
     * 用于得到平均值及汇总数据，以及各个占比情况
     *
     * @param records 统计记录
     */
    private void statisticDistribution(List<Map<String, Object>> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        //得到平均值行和汇总行
        Map<String, Object> averageColumn = new HashMap<>();
        averageColumn.put("groupDay", "平均值");
        Map<String, Object> sumColumn = new HashMap<>();
        sumColumn.put("groupDay", "汇总");
        for (int i = 1; i <= 21; i++) {
            String key = (i - 1) * 5 + "_";
            if (i != 21) {
                key += i * 5;
            }
            BigDecimal sum = BigDecimal.ZERO;
            for (Map<String, Object> record : records) {
                sum = sum.add((BigDecimal) record.get(key));
            }
            averageColumn.put(key, sum.divide(new BigDecimal(records.size()), 0, RoundingMode.HALF_UP));
            sumColumn.put(key, sum);
        }
        records.add(averageColumn);
        records.add(sumColumn);

        // 计算个日期以及平均值，汇总的订单数合计
        for (Map<String, Object> record : records) {
            BigDecimal count = BigDecimal.ZERO;
            for (int i = 1; i <= 21; i++) {
                String key = (i - 1) * 5 + "_";
                if (i != 21) {
                    key += i * 5;
                }
                BigDecimal val = (BigDecimal) record.get(key);
                count = count.add(val);
            }
            record.put("count", count);
        }

        // 计算个日期以及平均值，汇总的占比情况
        final String percentageStr = "_percentage";
        for (Map<String, Object> record : records) {
            BigDecimal count = (BigDecimal) record.get("count");
            BigDecimal percentageCount = BigDecimal.ZERO;
            Map<String, Object> percentageMap = new HashMap<>();
            for (int i = 1; i <= 21; i++) {
                String key = (i - 1) * 5 + "_";
                if (i != 21) {
                    key += i * 5;
                }
                BigDecimal val = (BigDecimal) record.get(key);
                BigDecimal percentage = val.divide(count, 4, RoundingMode.HALF_UP);
                // 如果为最后一个数值计算时
                if (i == 21) {
                    percentage = new BigDecimal(1).subtract(percentageCount);
                }
                percentageMap.put(key + percentageStr, percentage);
                percentageCount = percentageCount.add(percentage);
            }
            percentageMap.put("count" + percentageStr, percentageCount);
            record.putAll(percentageMap);
        }
    }

    @Override
    public boolean passZfProductScan(Integer apvId) {
        WhApv whApv = new WhApv();
        whApv.setId(apvId);

        // 等待发货
        whApv.setStatus(ApvStatus.WAITING_DELIVER.intCode());

        // 打单日期
        whApv.setPrintDate(new Timestamp(System.currentTimeMillis()));

        // 下一个流程[等待发货]
        int successModified = whApvDao.updateWhApvStatusByStatus(whApv, ApvStatus.ALLOT.intCode());
        if (successModified > 0) {
            WhApv queryWhApv = whApvDao.queryWhApv(apvId);
            // 包装完成
            ApvTrack apvTrack = new ApvTrack();
            apvTrack.setApvNo(queryWhApv.getApvNo());
            apvTrack.setPackUser(DataContextHolder.getUserId());
            apvTrack.setPackFinishTime(new Timestamp(System.currentTimeMillis()));
            apvTrackService.updateApvTrackByApvNo(apvTrack);
            // 推送pms 包装
            pushPackingFinish(queryWhApv);

            SystemLogUtils.APVLOG.log(whApv.getId(), "发货单状态变更",
                    new String[][]{{"历史状态", ApvStatus.getNameByCode(ApvStatus.ALLOT.getCode())},
                            {"更改状态", whApv.getStatusCn()}});
            return true;
        }
        return false;
    }

    @Override
    public void allotUpNotAllotApv() {
        // 匹配缺货订单上架后未匹配的库存
        Map<Integer, WhCheckInItem> existItemMap = StringRedisUtils.hGetAll(RedisConstant.ZF_NOT_ALLOT_CHECK_IN_LIST);
        if (MapUtils.isEmpty(existItemMap))
            return;
        List<WhCheckInItem> existList = new ArrayList<>(existItemMap.values());
        if (CollectionUtils.isEmpty(existList))
            return;
        Map<String, List<WhCheckInItem>> itemMap = existList.stream()
                .collect(Collectors.groupingBy(i -> i.getSkuId() + i.getComment()));
        for (Map.Entry<String, List<WhCheckInItem>> entry : itemMap.entrySet()) {
            List<WhCheckInItem> items = entry.getValue();
            if (CollectionUtils.isEmpty(items))
                continue;
            List<String> skuList = items.stream().map(WhCheckInItem::getSku).distinct().collect(Collectors.toList());
            int qty = items.stream().mapToInt(i -> Optional.ofNullable(i.getQcQuantity()).orElse(0)).sum();
            WhCheckInItem checkInItem = items.get(0);
            checkInItem.setQcQuantity(qty);
            try {
                outStockMatchHandelService.allotStockApv(skuList, checkInItem);
                items.forEach(
                        item -> StringRedisUtils.hDel(RedisConstant.ZF_NOT_ALLOT_CHECK_IN_LIST, String.valueOf(item.getItemId())));
            }
            catch (Exception e) {
                log.error("allotStockApv error:{}", e.getMessage());
            }
        }
    }

    @Override
    public ResponseJson doCancelJitApv(String apvNo) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            WhApvQueryCondition query = new WhApvQueryCondition();
            query.setApvNo(apvNo);
            WhApv whApv =whApvDao.queryWhApv(query);
            if (whApv.getStatus().equals(ApvStatus.CANCEL.intCode())) {
                response.setMessage("该订单已取消");
                response.setStatus(StatusCode.SUCCESS);
                return response;
            }
            // 装车不能取消
            if (whApv.getStatus().equals(ApvStatus.LOADED.intCode())) {
                response.setMessage("订单：" + apvNo + "已装车取消失败！");
                return response;
            }
            // 交运不能取消
            if (whApv.getStatus().equals(ApvStatus.DELIVER.intCode())) {
                response.setMessage("订单：" + apvNo + "已交运取消失败！");
                return response;
            }
            List<Integer> shipStatusList = Arrays.asList(ApvOrderType.JIT_FULL_MANAGEMENT.intCode(), ApvOrderType.JIT_HALF_MANAGEMENT.intCode());
            if (!shipStatusList.contains(whApv.getShipStatus())) {
                response.setMessage("订单：" + apvNo + "不是本地仓JIT类型取消失败！");
                return response;
            }

            if (whApv.getShipStatus() != null && ApvOrderType.getOptimalIntCode().contains(whApv.getShipStatus())) {
                response.setMessage("订单：" + apvNo + "优选仓订单禁止取消！");
                return response;
            }

            List<WhApvItem> items = whApvItemService.querySkuAndSaleQuantityByApvNo(whApv.getApvNo());
            if (CollectionUtils.isEmpty(items)) {
                throw new RuntimeException(whApv.getApvNo() + ": 数据不全!");
            }
            whApv.setWhApvItems(items);
            apvStatusUpdateService.cancel(whApv);
            cancelJitApvTms(apvNo,AsnPrepareStatus.CANCEL.intCode());
        }
        catch (Exception e) {
            response.setMessage("取消发货异常：" + e.getMessage());
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @Override
    public void cancelJitApvTms(String apvNo, Integer status) {
        FirstOrder firstOrder = new FirstOrder();
        firstOrder.setApvNo(apvNo);
        firstOrder.setBatStatus(AsnPrepareStatus.getNameByCode(status.toString()));
        firstOrder.setUpdateTime(new Timestamp(System.currentTimeMillis()));

        AmqMessage amqMessage = new AmqMessage();
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_TMS_ASN_PREPARE_STATUS.getCode());
        amqMessage.setQueue(Queues.PUSH_FIRST_ORDER_STATUS_CHANGE);
        amqMessage.setRoutingKey(Queues.PUSH_FIRST_ORDER_STATUS_CHANGE);
        amqMessage.setExchange(RabbitMqExchange.TMS_WMS_DIRECT_EXCHANGE);
        amqMessage.setMessageBody(JSON.toJSONString(firstOrder));
        amqMessage.setRelevantParam(apvNo);
        amqMessage.setCreateBy(DataContextHolder.getUserId());
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        amqMessage.setSendStatus(false);
        amqMessage.setRetryLimit(0);
        amqMessageService.createAmqMessage(amqMessage);
    }

    /**
     * 中转仓JIT迁移本地仓
     * @param apvNo
     * @param status  这里使用原来中转仓的取消状态，需与订单系统保持一致
     */
    @Override
    public void cancelJitApvOms(String apvNo,String trackingNumber, Integer status) {
        if (StringUtils.isBlank(apvNo)) return;
        Map<String, Object> message = new HashMap();
        message.put("receivingCode", apvNo);
        message.put("status", status);//AsnPrepareStatus
        message.put("handleTime", new Date());
        Integer createBy = DataContextHolder.getUserId();
        if (createBy == null)
            createBy = 1;
        message.put("user", createBy + "-" + TaglibUtils.getEmployeeNameByUserId(createBy));
        message.put("trackingNumber",trackingNumber);
        message.put("isJit",true);
        String warehouseId = CacheUtils.getLocalWarehouseIdStr();
        message.put("warehouseId", warehouseId);
        amqpTemplate.convertAndSend(RabbitMqExchange.PUSH_WMS_DIRECT_EXCHANGE, Queues.PUSH_ASN_STATUS_CHANGE, JSON.toJSONString(message));
        amqMessageService.createCopyAmqMessage(new AmqMessage(AmqMessageModuleName.PUSH_OMS_ASN_STATUS.getCode(),
                RabbitMqExchange.PUSH_WMS_DIRECT_EXCHANGE, Queues.PUSH_ASN_STATUS_CHANGE, Queues.PUSH_ASN_STATUS_CHANGE, apvNo,
                JSON.toJSONString(message), true, "MQ消息发送成功"));
    }

    @Override
    public ResponseJson doDeliverOrderOnAmq(AmqMessage amqMessage) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (null != amqMessage) {
            try {
                String messageBody = amqMessage.getMessageBody();
                log.info("消费PC交运扫描信息:" + messageBody);
                if (StringUtils.isNotBlank(messageBody)) {
                    DeliverRecordDTO deliverRecordDTO = JSON.parseObject(messageBody, DeliverRecordDTO.class);
                    if (deliverRecordDTO != null && StringUtils.isNotBlank(deliverRecordDTO.getApvNo())) {
                        List<WhApvItem> items = whApvItemService.querySkuAndSaleQuantityByApvNo(deliverRecordDTO.getApvNo());
                        if (CollectionUtils.isEmpty(items)) {
                            return null;
                        }
                        String scanner = deliverRecordDTO.getScanner();
                        if (StringUtils.isNotBlank(scanner)) {
                            try {
                                String[] split = scanner.split("\\(");
                                String userId = split[1].replace(")", "");
                                DataContextHolder.setOperationId(userId);
                                DataContextHolder.setUsername(split[0]);
                                DataContextHolder.setContext(Constant.SALE_USER_ID, userId);
                                DataContextHolder.setContext(Constant.SALE_USER_NAME, split[0]);
                            } catch (Exception e) {
                                log.error("PC交运解析交运人异常:" + e.getMessage(), e);
                            }
                        }
                        WhApv apv = new WhApv();
                        apv.setId(items.get(0).getApvId());
                        apv.setWhApvItems(items);
                        apv.setApvNo(deliverRecordDTO.getApvNo());
                        apv.setActualWeight(deliverRecordDTO.getWeight());
                        apv.setIdcard("PC");
                        if (Integer.valueOf(ApvStatus.DELIVER.intCode()).equals(items.get(0).getSkuId())) {
                            // 多次交运
                            SystemLogUtils.APVLOG.log(apv.getId(), "订单交运,并删除锁",
                                    new String[][]{{"更改后状态", ApvStatus.DELIVER.getName()}, {"实际重量", deliverRecordDTO.getWeight() + "g"},
                                            {"交运人", deliverRecordDTO.getScanner()}, {"交运次数", "多次"}});
                        }
                        log.warn(apv.getApvNo() + " ============= PC");
                        // 扣完库存在改状态   失败重试10次
                        if (!apvStatusUpdateService.deliverWhApvStatus(apv.getId(), 10, apv.getActualWeight())) {
                            log.info("发货单号：" + deliverRecordDTO.getApvNo() + "已经交运，不能重复交运！");
                            return null;
                        }

                        apvStatusUpdateService.deliverByJms(apv);
                        calculateApvStandardWeightByApvNo(deliverRecordDTO.getApvNo());
                    }
                }
                amqMessageService.deleteAndBackupMessage(amqMessage, "MQ消息发送成功");
            }
            catch (Exception e) {
                log.error("定时执行消费PC交运扫描信息失败！messageId：" + amqMessage.getMessageId(), e);
                AmqMessage updateAmqMessage = new AmqMessage();
                updateAmqMessage.setMessageId(amqMessage.getMessageId());
                updateAmqMessage.setSendStatus(false);
                Integer retryLimit = amqMessage.getRetryLimit() == null ? 0 : amqMessage.getRetryLimit() + 1;
                updateAmqMessage.setRetryLimit(retryLimit);
                updateAmqMessage.setResponseMsg("系统异常，消费PC交运扫描信息");
                updateAmqMessage.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                amqMessageService.updateAmqMessage(updateAmqMessage);
                response.setMessage("系统异常，消费PC交运扫描信息");
                return response;
            }
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("消费PC交运扫描");
            return response;
        }
        response.setMessage("消息体为空");
        return response;

    }

}