package com.estone.sowstockout.dao;

import com.estone.sowstockout.bean.GridException;
import com.estone.sowstockout.bean.GridExceptionQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface GridExceptionDao {
    int queryGridExceptionCount(GridExceptionQueryCondition query);

    List<GridException> queryGridExceptionList();

    List<GridException> queryGridExceptionList(GridExceptionQueryCondition query, Pager pager);

    GridException queryGridException(Integer primaryKey);

    GridException queryGridException(GridExceptionQueryCondition query);

    void createGridException(GridException entity);

    void batchCreateGridException(List<GridException> entityList);

    void batchUpdateGridException(List<GridException> entityList);

    void deleteGridException(Integer primaryKey);

    void updateGridException(GridException entity);
}