package com.estone.wms.bean;

import com.estone.wms.enums.ExceptionMarkReasonEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved. 
 * Project Name: wms
 * Package Name: com.estone.wms.bean
 * File Name: ExceptionMarkConfig.java
 * Description: 入库异常标记原因配置（极简版本，支持灵活扩展）
 * Author: Amoi
 * Date: 2025-06-28
 * ---------------------------------------------------------------------------
 */
@Data
public class ExceptionMarkConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标记原因列表，只存储显示名称
     * 例如：["待确认", "质量问题", "包装损坏", "商品破损"]
     */
    private List<String> markReasonList;

    /**
     * 默认构造函数，初始化标记原因都为枚举预设值
     */
    public ExceptionMarkConfig() {
        this.markReasonList = new ArrayList<>(Arrays.asList(ExceptionMarkReasonEnum.getAllDisplayNames()));
    }

    /**
     * 添加标记原因
     * @param reasonName 原因名称
     */
    public void addMarkReason(String reasonName) {
        if (reasonName != null && !reasonName.trim().isEmpty()) {
            if (!this.markReasonList.contains(reasonName)) {
                this.markReasonList.add(reasonName);
            }
        }
    }

    /**
     * 检查原因是否存在
     * @param reasonName 原因名称
     * @return 是否存在
     */
    public boolean containsReason(String reasonName) {
        return this.markReasonList != null && this.markReasonList.contains(reasonName);
    }

    /**
     * 获取所有可用的标记原因（用于前端下拉选项）
     * @return 标记原因列表
     */
    public List<String> getAvailableReasons() {
        List<String> availableReasons = new ArrayList<>();
        
        if (this.markReasonList != null) {
            availableReasons.addAll(this.markReasonList);
        }
        
        return availableReasons;
    }
}