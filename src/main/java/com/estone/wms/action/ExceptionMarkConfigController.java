package com.estone.wms.action;

import com.estone.common.util.model.ApiResult;
import com.estone.wms.bean.ExceptionMarkConfig;
import com.estone.wms.enums.ExceptionMarkReasonEnum;
import com.estone.wms.utils.ExceptionMarkConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved. 
 * Project Name: wms
 * Package Name: com.estone.wms.action
 * File Name: ExceptionMarkConfigController.java
 * Description: 入库异常标记配置Controller（极简版本，支持灵活扩展）
 * Author: Amoi
 * Date: 2025-06-28
 * ---------------------------------------------------------------------------
 */
@RestController
@RequestMapping(value = "exceptionMarkConfig")
@Slf4j
public class ExceptionMarkConfigController {

    /**
     * 获取当前可用的标记原因选项（用于前端下拉）
     * @return ApiResult<List<String>>
     */
    @PostMapping("/getAvailableReasons")
    @ResponseBody
    public ApiResult<List<String>> getAvailableReasons() {
        try {
            log.info("获取可用标记原因选项");

            ExceptionMarkConfig config = ExceptionMarkConfigUtil.getExceptionMarkConfig();
            return ApiResult.newSuccess(config.getMarkReasonList());

        } catch (Exception e) {
            log.error("获取可用标记原因选项失败", e);
            return ApiResult.newError("获取可用标记原因选项失败: " + e.getMessage());
        }
    }

    /**
     * 添加自定义标记原因
     * @param reasonName 原因名称
     * @return ApiResult<Boolean>
     */
    @PostMapping("/addCustomReason")
    @ResponseBody
    public ApiResult<Boolean> addCustomReason(@RequestParam String reasonName) {
        try {
            log.info("添加自定义标记原因: {}", reasonName);

            // 参数校验
            if (StringUtils.isBlank(reasonName)) {
                return ApiResult.newError("原因名称不能为空");
            }
            
            if (reasonName.length() > 100) {
                return ApiResult.newError("原因名称长度不能超过100个字符");
            }

            // 获取当前配置
            ExceptionMarkConfig config = ExceptionMarkConfigUtil.getExceptionMarkConfig();
            
            // 检查名称是否已存在
            if (config.containsReason(reasonName)) {
                return ApiResult.newError("原因名称已存在");
            }
            
            // 添加自定义原因
            config.addMarkReason(reasonName);

            // 保存配置
            String saveResult = ExceptionMarkConfigUtil.setExceptionMarkConfig(config);
            if (saveResult != null) {
                return ApiResult.newError(saveResult);
            }

            log.info("添加自定义标记原因成功: {}", reasonName);
            return ApiResult.newSuccess(true);

        } catch (Exception e) {
            log.error("添加自定义标记原因失败", e);
            return ApiResult.newError("添加自定义标记原因失败: " + e.getMessage());
        }
    }
} 