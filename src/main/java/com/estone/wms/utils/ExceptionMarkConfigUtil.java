package com.estone.wms.utils;

import com.alibaba.fastjson.JSON;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.SpringUtils;
import com.estone.system.param.bean.SystemParam;
import com.estone.system.param.service.SystemParamService;
import com.estone.transfer.bean.SeasonAlertSetting;
import com.estone.wms.bean.ExceptionMarkConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved. 
 * Project Name: wms
 * Package Name: com.estone.wms.utils
 * File Name: ExceptionMarkConfigUtil.java
 * Description: 入库异常标记配置工具类（简化版本）
 * Author: Amoi
 * Date: 2025-06-28
 * ---------------------------------------------------------------------------
 */
@Slf4j
public class ExceptionMarkConfigUtil {

    private static SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);
    /**
     * SystemParam中的参数键名
     */
    private static final String SYSTEM_PARAM_KEY = "EXCEPTION_MARK_CONFIG.WMS_EXCEPTION_MARK_CONFIG";

    /**
     * 获取异常标记配置
     * @return 异常标记配置对象
     */
    public static ExceptionMarkConfig getExceptionMarkConfig() {
        try {
            log.debug("开始从SystemParam获取异常标记配置");

            // 从SystemParam获取配置JSON字符串
            // 这里需要根据实际的SystemParam获取方式进行调整
            SystemParam systemParam = CacheUtils.SystemParamGet(SYSTEM_PARAM_KEY);
            if (systemParam == null || StringUtils.isBlank(systemParam.getParamValue())) {
                log.info("未找到异常标记配置，返回默认配置");
                return new ExceptionMarkConfig();
            }

            // 解析JSON配置
            ExceptionMarkConfig config = JSON.parseObject(systemParam.getParamValue(), ExceptionMarkConfig.class);
            if (config == null) {
                log.warn("异常标记配置JSON解析失败，返回默认配置");
                config = new ExceptionMarkConfig();
            }
            return config;

        }
        catch (Exception e) {
            log.error("获取异常标记配置失败，返回默认配置", e);
            return new ExceptionMarkConfig();
        }
    }

    /**
     * 保存异常标记配置
     * @param config 异常标记配置对象
     * @return 保存结果，null表示成功，非null表示失败原因
     */
    public static String setExceptionMarkConfig(ExceptionMarkConfig config) {
        try {
            log.debug("开始保存异常标记配置: {}", toJson(config));
            
            if (config == null) {
                return "配置对象不能为空";
            }
            
            // 转换为JSON字符串
            String configJson = toJson(config);
            if (StringUtils.isBlank(configJson)) {
                return "配置对象序列化失败";
            }
            // 保存到SystemParam
            SystemParam systemParam = CacheUtils.SystemParamGet(SYSTEM_PARAM_KEY);
            if (systemParam != null) {
                // 更新现有配置
                systemParam.setParamValue(configJson);
                systemParamService.updateSystemParam(systemParam);
                log.info("更新异常标记配置成功: {}", configJson);
            }
            else {
                // 创建新配置
                systemParam = new SystemParam();
                systemParam.setParamCode(SYSTEM_PARAM_KEY.split("\\.")[0]);
                systemParam.setParamKey(SYSTEM_PARAM_KEY.split("\\.")[1]);
                systemParam.setParamName("异常标记配置");
                systemParam.setParamValue(configJson);
                systemParam.setParamType(5); // string类型
                systemParam.setParamDisplay(true);
                systemParam.setParamEnabled(true);
                systemParamService.createSystemParam(systemParam);
                log.info("创建异常标记配置成功: {}", configJson);
            }

            log.info("异常标记配置保存成功");
            return null; // 成功

        }
        catch (Exception e) {
            log.error("保存异常标记配置失败", e);
            return "保存配置时发生异常: " + e.getMessage();
        }
    }

    /**
     * 将配置对象转换为JSON字符串
     * @param config 配置对象
     * @return JSON字符串
     */
    public static String toJson(ExceptionMarkConfig config) {
        try {
            return JSON.toJSONString(config);
        } catch (Exception e) {
            log.error("配置对象转JSON失败", e);
            return null;
        }
    }
} 