package com.estone.wms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved. 
 * Project Name: wms
 * Package Name: com.estone.wms.enums
 * File Name: ExceptionMarkReasonEnum.java
 * Description: 入库异常标记原因枚举（简化版本，支持灵活扩展）
 * Author: Amoi
 * Date: 2025-06-28
 * ---------------------------------------------------------------------------
 */
@Getter
@AllArgsConstructor
public enum ExceptionMarkReasonEnum {

    /**
     * 待确认
     */
    TO_CONFIRM("待确认"),

    /**
     * 需补货
     */
    NEED_REPLENISH("需补货"),

    /**
     * 质量问题
     */
    QUALITY_ISSUE("质量问题"),

    /**
     * 包装损坏
     */
    PACKAGE_DAMAGE("包装损坏"),

    /**
     * 数量异常
     */
    QUANTITY_EXCEPTION("数量异常"),

    /**
     * 待退货
     */
    TO_RETURN("待退货"),

    /**
     * 库位错误
     */
    LOCATION_ERROR("库位错误"),

    /**
     * SKU不符
     */
    SKU_MISMATCH("SKU不符"),

    /**
     * 供应商问题
     */
    SUPPLIER_ISSUE("供应商问题"),

    /**
     * 运输损坏
     */
    TRANSPORT_DAMAGE("运输损坏"),

    /**
     * 其他
     */
    OTHER("其他");

    /**
     * 原因名称
     */
    private final String name;

    /**
     * 根据枚举名称获取枚举
     * @param enumName 枚举名称（如：TO_CONFIRM）
     * @return 对应的枚举，未找到返回null
     */
    public static ExceptionMarkReasonEnum getByName(String enumName) {
        if (enumName == null || enumName.trim().isEmpty()) {
            return null;
        }
        
        try {
            return ExceptionMarkReasonEnum.valueOf(enumName.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * 根据显示名称获取枚举
     * @param displayName 显示名称（如：待确认）
     * @return 对应的枚举，未找到返回null
     */
    public static ExceptionMarkReasonEnum getByDisplayName(String displayName) {
        if (displayName == null || displayName.trim().isEmpty()) {
            return null;
        }
        
        for (ExceptionMarkReasonEnum reason : values()) {
            if (reason.getName().equals(displayName)) {
                return reason;
            }
        }
        return null;
    }

    /**
     * 验证枚举名称是否有效
     * @param enumName 枚举名称
     * @return 是否为有效的预设原因
     */
    public static boolean isValidEnumName(String enumName) {
        return getByName(enumName) != null;
    }

    /**
     * 验证显示名称是否有效
     * @param displayName 显示名称
     * @return 是否为有效的预设原因
     */
    public static boolean isValidDisplayName(String displayName) {
        return getByDisplayName(displayName) != null;
    }

    /**
     * 获取所有预设原因的Map形式
     * @return Map<枚举名称, 显示名称>
     */
    public static Map<String, String> getAllReasonsMap() {
        return Arrays.stream(values())
                .collect(Collectors.toMap(
                    Enum::name,
                    ExceptionMarkReasonEnum::getName,
                    (existing, replacement) -> existing,
                    LinkedHashMap::new
                ));
    }

    /**
     * 获取所有显示名称列表
     * @return 所有显示名称的列表
     */
    public static String[] getAllDisplayNames() {
        return Arrays.stream(values())
                .map(ExceptionMarkReasonEnum::getName)
                .toArray(String[]::new);
    }

    // ========== 向后兼容方法 ==========
    
    /**
     * 根据代码获取枚举（向后兼容）
     * @deprecated 推荐使用getByName()方法
     * @param code 原因代码（实际为枚举名称）
     * @return 对应的枚举，未找到返回null
     */
    @Deprecated
    public static ExceptionMarkReasonEnum getByCode(String code) {
        return getByName(code);
    }

    /**
     * 根据代码获取名称（向后兼容）
     * @deprecated 推荐使用getByName().getName()方法
     * @param code 原因代码（实际为枚举名称）
     * @return 原因名称，未找到返回原代码
     */
    @Deprecated
    public static String getNameByCode(String code) {
        ExceptionMarkReasonEnum reason = getByName(code);
        return reason != null ? reason.getName() : code;
    }

    /**
     * 验证代码是否有效（向后兼容）
     * @deprecated 推荐使用isValidEnumName()方法
     * @param code 原因代码（实际为枚举名称）
     * @return 是否为有效的预设原因
     */
    @Deprecated
    public static boolean isValidCode(String code) {
        return isValidEnumName(code);
    }
} 