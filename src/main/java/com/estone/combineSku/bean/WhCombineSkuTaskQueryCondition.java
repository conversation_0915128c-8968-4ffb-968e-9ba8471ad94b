package com.estone.combineSku.bean;

import lombok.Data;

import java.util.List;

@Data
public class WhCombineSkuTaskQueryCondition extends WhCombineSkuTask {
    private static final long serialVersionUID = 1L;

    private Boolean readOnly = false;

    public List<Integer> idList;

    private List<Integer> statusList;

    private String sku;

    /** 创建时间 */
    private String fromCreationDate;
    private String toCreationDate;

    // 是否查询库存信息
    private boolean stockFlag;

    private Boolean receiveFlag;

    // 分配信息
    private boolean outStockChainFlag;
}