package com.estone.allocation.action;

import static java.util.stream.Collectors.toList;

import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.estone.allocation.enums.DeliveryMethodEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.alibaba.fastjson.JSON;
import com.estone.allocation.bean.*;
import com.estone.allocation.call.ApvAllocationRequestCall;
import com.estone.allocation.dao.WhApvAllocationDao;
import com.estone.allocation.domain.WhApvAllocationDo;
import com.estone.allocation.enums.AllocationOrderStatusEnum;
import com.estone.allocation.enums.AllocationStatusEnum;
import com.estone.allocation.enums.AllocationTypeEnum;
import com.estone.allocation.service.WhApvAllocationOrderService;
import com.estone.allocation.service.WhApvAllocationService;
import com.estone.allocation.service.WhAsnAllocationPickingService;
import com.estone.allocation.util.AllocationPushUtil;
import com.estone.checkin.utils.GetSkuQcCategoryDescUtil;
import com.estone.common.SelectJson;
import com.estone.common.enums.WarehousePropertyEnum;
import com.estone.common.util.*;
import com.estone.common.util.model.ResultModel;
import com.estone.picking.bean.WhAllocationPickTask;
import com.estone.picking.bean.WhAllocationPickTaskQueryCondition;
import com.estone.picking.enums.AllocationPickingTaskStatus;
import com.estone.picking.service.WhAllocationPickTaskService;
import com.estone.sku.bean.AfterSaleSettlement;
import com.estone.sku.bean.AfterSaleSettlementQueryCondition;
import com.estone.sku.bean.ExpManage;
import com.estone.sku.bean.ExpManageQueryCondition;
import com.estone.sku.service.AfterSaleSettlementService;
import com.estone.sku.service.ExpManageService;
import com.estone.sku.service.WhSkuService;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.enums.LocationTagEnum;
import com.estone.warehouse.enums.LocationWarehouseType;
import com.estone.warehouse.enums.StockLogType;
import com.estone.warehouse.service.WhStockService;
import com.estone.warehouse.service.WhWarehouseService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
@RequestMapping(value = "allocation")
public class WhApvAllocationController extends BaseController {

    private Logger logger = LoggerFactory.getLogger(WhApvAllocationController.class);

    @Resource
    private WhApvAllocationService whApvAllocationService;

    @Resource
    private WhApvAllocationOrderService whApvAllocationOrderService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private WhWarehouseService whWarehouseService;

    @Resource
    private WhAllocationPickTaskService whAllocationPickTaskService;

    @Resource
    private AfterSaleSettlementService afterSaleSettlementService;

    @Resource
    private WhApvAllocationDao whApvAllocationDao;

    @Resource
    private WhAsnAllocationPickingService whAsnAllocationPickingService;
    @Resource
    private WhStockService whStockService;
    @Resource
    private ExpManageService expManageService;

    @RequestMapping(method = { RequestMethod.GET })
    public String init(@ModelAttribute("domain") WhApvAllocationDo domain) {
        initFormData(domain);
        if (null != domain.getQuery() && null != domain.getQuery().getAllocationType()) {
            queryWhApvAllocations(domain);
        }
        return "allocation/allocation_list";
    }

    @RequestMapping(value = "search", method = { RequestMethod.POST })
    public String search(@ModelAttribute("domain") WhApvAllocationDo domain) {
        initFormData(domain);
        queryWhApvAllocations(domain);
        return "allocation/allocation_list";
    }

    private void initFormData(WhApvAllocationDo domain) {
        domain.setStatusSelectJosn(SelectJson.getList(AllocationStatusEnum.values()));
        domain.setStatusOrderSelectJson(SelectJson.getList(AllocationOrderStatusEnum.values()));
    }

    private void queryWhApvAllocations(@ModelAttribute("domain") WhApvAllocationDo domain) {
        WhApvAllocationQueryCondition query = domain.getQuery();
        if (null == query) {
            query = new WhApvAllocationQueryCondition();
        }

        Pager pager = domain.getPage();
        if (null == pager) {
            pager = new Pager();
        }
        //兼容SKU编码和唯一码
        query.setSkuStr(CompatibleSkuUtils.getSku(query.getSkuStr()));

        List<WhApvAllocation> allocationList = new ArrayList<WhApvAllocation>();

        if (AllocationTypeEnum.INVENTORY.intCode().equals(query.getAllocationType())) {
            allocationList = whApvAllocationService.queryApvAllocationList(query, pager);
        }
        else if (AllocationTypeEnum.ORDER.intCode().equals(query.getAllocationType())) {
            allocationList = whApvAllocationOrderService.queryApvAllocationOrderList(query, pager);
        }
        else {
            allocationList = whApvAllocationOrderService.queryApvAllocationOrderList(query, pager);
        }

        domain.setWhApvAllocations(allocationList);
    }

    // 获取sku信息
    @RequestMapping(value = "sku/info", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson getSkuInfo(@RequestParam("skuSplit") String skuSplit) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (StringUtils.isBlank(skuSplit)) {
            response.setMessage("请输入要添加的sku");
            return response;
        }
        //兼容SKU编码和唯一码
        if(!skuSplit.contains(",")){
            skuSplit = CompatibleSkuUtils.getSku(skuSplit);
        }
        List<String> skuList = CommonUtils.splitList(skuSplit, ",");
        List<WhApvAllocationItem> allocationSkuList = whApvAllocationService.queryApvAllocationSkuList(skuList);

        if (CollectionUtils.isEmpty(allocationSkuList)) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("没有该SKU");
            return response;
        }

        Integer localWarehouseId = CacheUtils.getLocalWarehouseId();
        String message = "";

        // 判断SKU是否合并
        Map<String, String> skuMap = whSkuService.queryWhSkuDiscard(skuList);
        List<String> discardSku = whSkuService.queryDiscardSku(skuList);

        for (WhApvAllocationItem item : allocationSkuList) {
            Integer warehouseId = item.getWarehouseId();
            if (!localWarehouseId.equals(warehouseId)) {
                message += item.getSku() + "不是本仓SKU，不可调拨";
            }
            if (StringUtils.isBlank(item.getLocationNumber())) {
                message += item.getSku() + "没有库位，不可调拨";
            }
            if (item.getAllocationNum() > 0) {
                message += item.getSku() + "正在调拨中";
            }
            if (skuMap.get(item.getSku()) != null) {
                message += item.getSku() + ": 该SKU已被合并，请盘点处理!";
            }
            if (CollectionUtils.isNotEmpty(discardSku) && discardSku.contains(item.getSku())) {
                message += item.getSku() + ": 该SKU已被废弃!";
            }
        }

        if (StringUtils.isNotBlank(message)) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage(message);
            return response;
        }

        Map<String, Object> body = new HashMap<String, Object>();
        body.put("skuList", allocationSkuList);
        response.setBody(body);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @RequestMapping(value = "queryStocks", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson queryStocks(@RequestParam("skus") List<String> skus) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(skus)) {
            response.setMessage("参数为空！");
            return response;
        }
        //兼容SKU编码和唯一码
        String skuStr = CompatibleSkuUtils.getSku(org.apache.commons.lang.StringUtils.join(skus,","));
        skus = CommonUtils.splitList(skuStr,",");
        return whApvAllocationService.getSkuInfo(skus,null);
    }

    // 导入调拨SKU
    @RequestMapping(value = "uploadSku", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson uploadSku(HttpServletRequest request) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        ResultModel<String> resultModel = null;
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multiRequest.getFileMap();
        String[] titles = new String[] { "SKU", "库存ID", "调拨数量" };
        /** excel中所有的sku */
        List<WhStock> stockList = new ArrayList<>();
        for (MultipartFile multiPartFile : fileMap.values()) {
            try {
                resultModel = POIUtils.readExcel(titles, multiPartFile, row -> {
                    int cellnum = 0;
                    String sku = CompatibleSkuUtils.getSku(POIUtils.cellValue2Str(row.getCell(cellnum++)));
                    String stockId = CompatibleSkuUtils.getSku(POIUtils.cellValue2Str(row.getCell(cellnum++)));
                    String quantity = CompatibleSkuUtils.getSku(POIUtils.cellValue2Str(row.getCell(cellnum++)));
                    if (StringUtils.isAnyBlank(stockId, quantity, sku)) {
                        throw new RuntimeException("第" + row.getRowNum() + "行数据有误；SKU,库存ID,调拨数量都不能为空，请检查！");
                    }
                    WhStock stock = new WhStock();
                    stock.setSku(sku);
                    stock.setQuantity(Integer.parseInt(quantity));
                    stock.setId(Integer.parseInt(stockId));
                    stockList.add(stock);
                    return sku;
                }, false);

                if (resultModel.isSuccess()) {
                    if (CollectionUtils.isNotEmpty(stockList)) {
                        Map<Integer, WhStock> importMap = stockList.stream().collect(Collectors.toMap(WhStock::getId, s -> s));
                        // 使用增强版的getSkuInfoNew方法，包含完整的数量校验
                        response = whApvAllocationService.getSkuInfoNew(stockList.stream().map(WhStock::getSku).collect(toList()), importMap);

                    }
                }
                else {
                    response.setStatus(StatusCode.FAIL);
                    response.setMessage("系统异常，导入失败");
                    return response;
                }
            }
            catch (Exception e) {
                response.setStatus(StatusCode.FAIL);
                response.setMessage(e.getMessage());
                return response;
            }
        }
        return response;
    }


    @RequestMapping(value = "create", method = { RequestMethod.GET })
    public String toCreateWhApvAllocation(@ModelAttribute("domain") WhApvAllocationDo domain) {
        return "allocation/allocation_create";
    }

    // 创建调拨单
    @RequestMapping(value = "create", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson createWhApvAllocation(@RequestBody WhApvAllocationQueryCondition query) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (null == query) {
            response.setMessage("query is null");
            return response;
        }
        try {
            response = whApvAllocationService.createOrUpdateApvAllocation(query);
        }
        catch (Exception e) {
            logger.error("创建/修改调拨单失败", e);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    // 修改调拨单
    @RequestMapping(value = "update", method = { RequestMethod.GET })
    public String toUpdateWhApvAllocation(@ModelAttribute("domain") WhApvAllocationDo domain) {
        initFormData(domain);
        WhApvAllocationQueryCondition query = domain.getQuery();
        if (null != query && null != query.getAllocationId()) {
            WhApvAllocation allocationEntity = whApvAllocationService.queryApvAllocationDetail(query);
            domain.setWhApvAllocation(allocationEntity);
            if (null != allocationEntity) {
                if (AllocationStatusEnum.WAIT_COMMIT.intCode().equals(allocationEntity.getAllocationStatus())
                        || AllocationStatusEnum.DISCARD.intCode().equals(allocationEntity.getAllocationStatus())) {
                    return "allocation/allocation_create";
                }
            }
        }
        domain.setStatusSelectJosn(SelectJson.getDescriptionList(AllocationStatusEnum.values()));
        return "allocation/allocation_update";
    }

    // 修改调拨单
    @RequestMapping(value = "update", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson updateWhApvAllocation(@ModelAttribute("domain") WhApvAllocationDo domain) {
        try {
            return whApvAllocationService.updateApvAllocation(domain.getQuery());
        }
        catch (Exception e) {
            logger.error("修改调拨单失败", e);
            ResponseJson response = new ResponseJson();
            response.setStatus(StatusCode.FAIL);
            response.setMessage(e.getMessage());
            return response;
        }
    }
    
    @PostMapping(value = "removeItem/{itemId}")
    @ResponseBody
    public ResponseJson removeItem(@PathVariable Integer itemId) {
        ResponseJson response = new ResponseJson();
        Assert.notNull(itemId, "param id is null");
        WhApvAllocationQueryCondition query = new WhApvAllocationQueryCondition();

        query.setItemIds(List.of(itemId));
        List<WhApvAllocationItem> list = whApvAllocationDao.queryApvAllocationDetails(query);
        List<String> logList = new ArrayList<String>();
        List<Integer> deleteItemIds = new ArrayList<Integer>();
        for (WhApvAllocationItem item : list) {
            deleteItemIds.add(item.getAllocationItemId());
            logList.add("删除：" + item.getSku());
        }
        whApvAllocationDao.deleteApvAllocationItem(deleteItemIds);
        if (CollectionUtils.isNotEmpty(logList)) {
            SystemLogUtils.WHAPVALLOCATION.log(list.get(0).getAllocationId(), "待提交状态修改库存调拨单",
                    new String[][] { { JSON.toJSONString(logList) } });
        }
        return response;
    }

    // 更新调拨数据
    @RequestMapping(value = "updateAllocationQuantity", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson updateAllocationQuantity(@RequestParam(value = "allocationId") Integer allocationId) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            WhApvAllocationQueryCondition query = new WhApvAllocationQueryCondition();
            query.setAllocationId(allocationId);
            query.setAllocationType(AllocationTypeEnum.INVENTORY.intCode());
            WhApvAllocation apvAllocation = whApvAllocationService.queryApvAllocationDetail(query);
            if (null == apvAllocation) {
                response.setMessage("没有该调拨单");
                return response;
            }
            if (!AllocationStatusEnum.PICKING.intCode().equals(apvAllocation.getAllocationStatus())) {
                response.setMessage("该调拨单不可更新调拨数据，调拨单状态为拣货中才可操作");
                return response;
            }

            WhAllocationPickTaskQueryCondition taskQuery = new WhAllocationPickTaskQueryCondition();
            taskQuery.setAllocationNo(apvAllocation.getAllocationNo());
            List<WhAllocationPickTask> taskList = whAllocationPickTaskService.queryWhAllocationPickTasks(taskQuery,
                    null);
            for (WhAllocationPickTask pickTask : taskList) {
                if (!AllocationPickingTaskStatus.COMPLETED.intCode().equals(pickTask.getTaskStatus())) {
                    response.setMessage(pickTask.getTaskNo() + "未完成拣货，不可更新调拨数据");
                    return response;
                }
            }

            /** 差异 */
            List<WhApvAllocationItem> diffItems = new ArrayList<WhApvAllocationItem>();

            for (WhApvAllocationItem allocationItem : apvAllocation.getAllocationItems()) {
                if (!allocationItem.getAllocationNum().equals(allocationItem.getPickNum())) {
                    WhApvAllocationItem updateItem = new WhApvAllocationItem();
                    updateItem.setAllocationId(allocationItem.getAllocationId());
                    updateItem.setAllocationNo(allocationItem.getAllocationNo());
                    updateItem.setAllocationItemId(allocationItem.getAllocationItemId());
                    updateItem.setSku(allocationItem.getSku());
                    // 可用库存
                    Integer surplusQuantity = allocationItem.getSurplusQuantity() == null ? 0
                            : allocationItem.getSurplusQuantity();
                    // 新的调拨数量=可用库存+已拣货数量
                    Integer newAllocationNum = allocationItem.getPickNum() + surplusQuantity;
                    updateItem.setAllotSurplusQuantity(surplusQuantity);// 修改的库存
                    updateItem.setAllocationNum(newAllocationNum);// 新调拨数量
                    updateItem.setHistoryPickNum(allocationItem.getAllocationNum());// 原调拨数量

                    diffItems.add(updateItem);
                }
            }

            if (CollectionUtils.isNotEmpty(diffItems)) {
                List<String> skus = new ArrayList<>();
                for (WhApvAllocationItem allocationItem : diffItems) {
                    /*if (allocationItem.getAllotSurplusQuantity() != 0) {
                    }*/
                    skus.add(allocationItem.getSku());
                }
                return whApvAllocationService.updateAllocationQuantity(skus, apvAllocation, diffItems);
            }

            response.setMessage("该调拨单无拣货差异，不需要更新");
            return response;
        }
        catch (Exception e) {
            logger.error("更新调拨数据失败", e);
            response.setMessage(e.getMessage());
            return response;
        }
    }

    // 订单调拨详情
    @RequestMapping(value = "order/view", method = { RequestMethod.GET })
    public String toViewOrderWhApvAllocation(@ModelAttribute("domain") WhApvAllocationDo domain) {
        initFormData(domain);
        WhApvAllocationQueryCondition query = domain.getQuery();
        if (null != query && null != query.getAllocationId()) {
            WhApvAllocation whApvAllocation = whApvAllocationOrderService.queryApvAllocationOrderSkuList(query, null);
            List<WhApvAllocationOrderItem> items = whApvAllocationOrderService.queryWhApvAllocationOrderItems(Arrays.asList(query.getAllocationId()));
            List<String> taskNoList = items.stream().map(item -> item.getTaskNo().substring(1)).collect(Collectors.toList());
            Map<String, Integer> skuQuantityMap = ApvAllocationRequestCall.allocationQueryApvGridItem(taskNoList);
            if (skuQuantityMap != null) {
                whApvAllocation.getAllocationOrderSkus().forEach(orderSku -> orderSku.setGridQuantity(skuQuantityMap.get(orderSku.getSku()) == null ? 0 : skuQuantityMap.get(orderSku.getSku())));
            }
            domain.setWhApvAllocation(whApvAllocation);
        }
        return "allocation/allocation_view";
    }

    // 提交拣货
    @RequestMapping(value = "commit", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson commitWhApvAllocation(@ModelAttribute("domain") WhApvAllocationDo domain) {
        try {
            ResponseJson response = new ResponseJson();
            response.setStatus(StatusCode.FAIL);
            WhApvAllocationQueryCondition query = domain.getQuery();
            Integer sectionNum = query.getSectionNum();
            if (null == sectionNum || sectionNum <= 0) {
                response.setMessage("请输入货位数");
                return response;
            }

            WhApvAllocation apvAllocation = whApvAllocationDao.queryApvAllocationDetail(query);

            if (null == apvAllocation) {
                response.setMessage("没有相应的调拨单");
                return response;
            }
            List<String> skus = new ArrayList<>();
            for (WhApvAllocationItem item: apvAllocation.getAllocationItems()){
                skus.add(item.getSku());
            }

            return whApvAllocationService.batchCommitWhApvAllocation(skus, apvAllocation, sectionNum);
        }
        catch (Exception e) {
            logger.error("提交拣货失败", e);
            ResponseJson response = new ResponseJson();
            response.setStatus(StatusCode.FAIL);
            response.setMessage("提交拣货失败，原因：" + e.getMessage());
            return response;
        }
    }

    // 调整装箱数量
    @RequestMapping(value = "stockDiffBox", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson singleAuditWhApvAllocation(@RequestParam(value = "allocationNo") String allocationNo,
            @RequestParam(value = "allocationItemId") Integer allocationItemId,
            @RequestParam(value = "boxNum") Integer boxNum) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        try {
            if (StringUtils.isBlank(allocationNo) || null == allocationItemId || null == boxNum) {
                responseJson.setMessage("参数异常");
                return responseJson;
            }

            WhApvAllocationQueryCondition query = new WhApvAllocationQueryCondition();
            query.setAllocationNo(allocationNo);
            query.setAllocationType(AllocationTypeEnum.INVENTORY.intCode());
            query.setAllocationStatus(AllocationStatusEnum.WAIT_AUDIT.intCode());
            WhApvAllocation apvAllocation = whApvAllocationDao.queryApvAllocation(query);
            if (null == apvAllocation) {
                responseJson.setMessage(allocationNo.concat("该调拨单不可调整装箱数量"));
                return responseJson;
            }

            WhApvAllocationItem itemEntity = whApvAllocationDao.queryWhApvAllocationItemByPrimaryKey(allocationItemId);
            if (null == itemEntity) {
                responseJson.setMessage(allocationNo.concat("无调拨单明细"));
                return responseJson;
            }
            if (Integer.valueOf(1).equals(itemEntity.getInventoryStatus())){
                responseJson.setMessage(allocationNo.concat("不可重复调整"));
                return responseJson;
            }
            if (Boolean.TRUE.equals(itemEntity.getIsAudit())){
                responseJson.setMessage(allocationNo.concat("不可重复调整"));
                return responseJson;
            }

            List<String> skus = new ArrayList<>();
            skus.add(itemEntity.getSku());
            return whApvAllocationService.doSingleAuditWhApvAllocation(skus, itemEntity, boxNum);
        }
        catch (Exception e) {
            logger.error("调整装箱数量失败", e);
            ResponseJson response = new ResponseJson();
            response.setStatus(StatusCode.FAIL);
            response.setMessage("调整装箱数量失败，原因：" + e.getMessage());
            return response;
        }
    }

    // 退回重新拣货
    @RequestMapping(value = "doBackPicking", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson doBackPicking(@RequestParam(value = "allocationNo") String allocationNo) {
        try {
            return whApvAllocationService.doBackAllocationPicking(allocationNo);
        }
        catch (Exception e) {
            logger.error("退回重新拣货失败", e);
            ResponseJson response = new ResponseJson();
            response.setStatus(StatusCode.FAIL);
            response.setMessage("退回重新拣货失败，原因：" + e.getMessage());
            return response;
        }
    }

    // 审核通过
    @RequestMapping(value = "audit", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson auditWhApvAllocation(@ModelAttribute("domain") WhApvAllocationDo domain) {
        return whApvAllocationService.batchAuditWhApvAllocation(domain.getQuery());
    }

    @RequestMapping(value = "batchPushOrder", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson batchPushOrder(@ModelAttribute("domain") WhApvAllocationDo domain) {
        WhApvAllocationQueryCondition query = domain.getQuery();
        ResponseJson response = new ResponseJson();
        if (query == null || CollectionUtils.isEmpty(query.getAllocationIds())
                || StringUtils.isBlank(query.getPushType())){
            response.setStatus(StatusCode.FAIL);
            response.setMessage("参数错误！");
            return response;
        }
        if (StringUtils.equalsIgnoreCase(query.getPushType(), "ORDER")) {
            AllocationPushUtil.pushStockAllocationData(query.getAllocationIds());
        }
        if (StringUtils.equalsIgnoreCase(query.getPushType(), "STATUS")) {
            WhApvAllocationQueryCondition queryCondition = new WhApvAllocationQueryCondition();
            queryCondition.setAllocationIds(query.getAllocationIds());
            List<WhApvAllocation> allocationList = whApvAllocationDao.queryApvAllocationList(queryCondition, null);
            if (CollectionUtils.isEmpty(allocationList)){
                response.setStatus(StatusCode.FAIL);
                response.setMessage("没有要同步的单据");
                return response;
            }
            allocationList.stream().collect(Collectors.groupingBy(WhApvAllocation::getAllocationStatus))
                    .forEach((k, v) -> {
                        WhApvAllocationQueryCondition pushQuery = new WhApvAllocationQueryCondition();
                        pushQuery.setAllocationNoList(
                                v.stream().map(WhApvAllocation::getAllocationNo).collect(Collectors.toList()));
                        pushQuery.setAllocationStatus(k);
                        AllocationPushUtil.syncApvAllocationStatus(pushQuery);
                    });

        }
        return response;
    }

    // 确认签收
    @RequestMapping(value = "confirm", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson confirmWhApvAllocation(@ModelAttribute("domain") WhApvAllocationDo domain) {
        return whApvAllocationService.batchConfirmWhApvAllocation(domain.getQuery());
    }

    // 废弃调拨单
    @RequestMapping(value = "discard", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson discardWhApvAllocation(@ModelAttribute("domain") WhApvAllocationDo domain) {
        return whApvAllocationService.batchDiscardWhApvAllocation(domain.getQuery());
    }

    // 导入调拨单
    @RequestMapping(value = "upload", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson uploadWhApvAllocation(HttpServletRequest request) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        ResultModel<String> resultModel = null;
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multiRequest.getFileMap();
        String[] titles = new String[] { "SKU" };
        /** excel中所有的sku */
        Set<String> skus = new HashSet<String>();
        /** 本仓仓库属性 */
        Integer localWarehouseId = CacheUtils.getLocalWarehouseId();
        /** 错误信息 */
        String message = "";
        /** 创建导入sku */
        List<WhApvAllocationItem> allocationItems = new ArrayList<WhApvAllocationItem>();
        for (MultipartFile multiPartFile : fileMap.values()) {
            try {
                resultModel = POIUtils.readExcel(titles, multiPartFile, row -> {
                    int cellnum = 0;
                    String sku = CompatibleSkuUtils.getSku(POIUtils.cellValue2Str(row.getCell(cellnum++)));
                    if (StringUtils.isNotBlank(sku)) {
                        skus.add(sku);
                    }
                    return sku;
                }, false);

                if (resultModel.isSuccess()) {
                    if (CollectionUtils.isNotEmpty(skus)) {
                        List<WhApvAllocationItem> allocationSkuList = whApvAllocationService
                                .queryApvAllocationSkuList(new ArrayList<String>(skus));

                        if (CollectionUtils.isEmpty(allocationSkuList)) {
                            response.setStatus(StatusCode.FAIL);
                            response.setMessage("没有该SKU");
                            return response;
                        }

                        for (WhApvAllocationItem item : allocationSkuList) {
                            Integer warehouseId = item.getWarehouseId();
                            if (!localWarehouseId.equals(warehouseId)) {
                                message += item.getSku() + "不是本仓SKU，不可调拨";
                            }
                            if (StringUtils.isBlank(item.getLocationNumber())) {
                                message += item.getSku() + "没有库位，不可调拨";
                            }
                            if (item.getAllocationNum() > 0) {
                                message += item.getSku() + "正在调拨中";
                            }
                        }

                        if (StringUtils.isBlank(message)) {
                            allocationItems.addAll(allocationSkuList);
                        }
                    }
                }
                else {
                    response.setStatus(StatusCode.FAIL);
                    response.setMessage("系统异常，导入失败");
                    return response;
                }
            }
            catch (Exception e) {
                response.setStatus(StatusCode.FAIL);
                response.setMessage(e.getMessage());
                return response;
            }
        }

        if (StringUtils.isNotBlank(message)) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage(message);
            return response;
        }

        if (allocationItems.size() > 1000) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("最大只能导入1000个SKU");
            return response;
        }

        try {
            WhApvAllocationQueryCondition whApvAllocation = new WhApvAllocationQueryCondition();
            whApvAllocation.setDeliveryWarehouseId(localWarehouseId);
            Integer destWarehouseId = LocationWarehouseType.OLD.intCode().equals(localWarehouseId)
                    ? LocationWarehouseType.NANNING.intCode()
                    : LocationWarehouseType.OLD.intCode();
            whApvAllocation.setDestWarehouseId(destWarehouseId);
            whApvAllocation.setAllocationType(AllocationTypeEnum.INVENTORY.intCode());
            whApvAllocation.setAllocationItems(allocationItems);
            whApvAllocationService.createApvAllocation(whApvAllocation);
            response.setStatus(StatusCode.SUCCESS);
            return response;
        }
        catch (Exception e) {
            logger.error("导入调拨单失败", e);
            response.setStatus(StatusCode.FAIL);
            response.setMessage(e.getMessage());
            return response;
        }
    }

    String[] headers = { "调拨单号", "发货仓", "目的仓", "类型", "状态", "SKU", "商品名称", "调拨数量", "已拣数量", "装箱数量", "入库数量", "箱号","送货方式","物流单号"};

    // 导出调拨单
    @RequestMapping(value = "download", method = { RequestMethod.POST })
    @ResponseBody
    public void download(@ModelAttribute("domain") WhApvAllocationDo domain, HttpServletResponse response) {
        WhApvAllocationQueryCondition query = domain.getQuery();

        List<WhApvAllocation> dataList = new ArrayList<WhApvAllocation>();

        String exportType = query.getExportType();
        //兼容SKU编码和唯一码
        if(StringUtils.isNotBlank(query.getSkuStr())){
            query.setSkuStr(CompatibleSkuUtils.getSku(query.getSkuStr()));
        }
        switch (exportType) {
            case "ALL":
                dataList = whApvAllocationService.queryApvAllocationDetailList(query, null);
                break;
            case "CHECK":
                List<Integer> allocationIds = query.getAllocationIds();
                query = new WhApvAllocationQueryCondition();
                query.setAllocationIds(allocationIds);
                dataList = whApvAllocationService.queryApvAllocationDetailList(query, null);
                break;
            default:
                break;
        }

        OutputStream os = null;
        try {
            String fileName = "调拨单详情" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            final List<List<String>> recordsData = new ArrayList<List<String>>();
            POIUtils.createExcel(headers, dataList, apvAllocation -> {
                recordsData.clear();
                List<WhApvAllocationItem> itemList = apvAllocation.getAllocationItems();
                for (WhApvAllocationItem item : itemList) {
                    List<String> downloadList = new ArrayList<String>(headers.length);
                    downloadList.add(POIUtils.transferObj2Str(apvAllocation.getAllocationNo()));
                    downloadList.add(POIUtils.transferObj2Str(WarehousePropertyEnum.getNameByCode(apvAllocation.getDeliveryWarehouseId()+"")));
                    downloadList.add(POIUtils.transferObj2Str(WarehousePropertyEnum.getNameByCode(apvAllocation.getDestWarehouseId()+"")));
                    downloadList.add(POIUtils
                            .transferObj2Str(AllocationTypeEnum.getNameByCode(apvAllocation.getAllocationType() + "")));
                    downloadList.add(POIUtils.transferObj2Str(
                            AllocationStatusEnum.getNameByCode(apvAllocation.getAllocationStatus() + "")));
                    downloadList.add(POIUtils.transferObj2Str(item.getSku()));
                    downloadList.add(POIUtils.transferObj2Str(item.getSkuName()));
                    downloadList.add(POIUtils.transferObj2Str(item.getAllocationNum()));
                    downloadList.add(POIUtils.transferObj2Str(item.getPickNum()));
                    downloadList.add(POIUtils.transferObj2Str(item.getBoxNum()));
                    downloadList.add(POIUtils.transferObj2Str(item.getUpNum()));
                    downloadList.add(POIUtils.transferObj2Str(item.getBoxNo()));
                    downloadList.add(POIUtils.transferObj2Str(DeliveryMethodEnum.getNameByCode(item.getDeliveryMethod())));
                    downloadList.add(POIUtils.transferObj2Str(item.getLogisticsNo()));
                    recordsData.add(downloadList);
                }

                return recordsData;

            }, true, os);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        finally {
            IOUtils.closeQuietly(os);
        }
    }

    // 打印调拨单
    @RequestMapping(value = "order/qRCodePrint", method = { RequestMethod.GET })
    public String qrCodePrint(@ModelAttribute("domain") WhApvAllocationDo domain,
            @RequestParam("allocationIds") List<Integer> allocationIds) {

        WhApvAllocationQueryCondition query = new WhApvAllocationQueryCondition();
        query.setAllocationIds(allocationIds);
        List<WhApvAllocation> entityList = whApvAllocationService.queryApvAllocationList(query);
        domain.setWhApvAllocations(entityList);

        return "allocation/allocation_order_qrcode_print";
    }

    @RequestMapping(value = "order/updatePrint")
    @ResponseBody
    public ResponseJson updatePrint(@RequestParam("allocationIds") List<Integer> allocationIds) {
        ResponseJson response = new ResponseJson();

        WhApvAllocationQueryCondition query = new WhApvAllocationQueryCondition();
        allocationIds.forEach(allocationId -> {
            query.setAllocationId(allocationId);
            WhApvAllocation whApvAllocation = whApvAllocationService.queryApvAllocation(query);
            if (null != whApvAllocation && Integer.valueOf(AllocationOrderStatusEnum.WAIT_PRINT.intCode())
                    .equals(whApvAllocation.getAllocationStatus())) {
                WhApvAllocation entity = new WhApvAllocation();
                entity.setAllocationId(allocationId);
                entity.setAllocationStatus(AllocationOrderStatusEnum.PRINTING.intCode());
                whApvAllocationService.updateApvAllocationByPush(entity);
                SystemLogUtils.WHAPVALLOCATION.log(whApvAllocation.getAllocationId(), "打印调拨单状态变更",
                        new String[][] { { "历史状态", AllocationOrderStatusEnum.WAIT_PRINT.getName() },
                                { "更改状态", AllocationOrderStatusEnum.PRINTING.getName() } });
            }
        });

        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    // 导出调拨单SKU详情
    @RequestMapping(value = "download/item", method = { RequestMethod.POST })
    public void downloadItem(@ModelAttribute("domain") WhApvAllocationDo domain, HttpServletResponse response)
            throws Exception {

        AllocationDownloadQueryCondition query = domain.getDownloadQuery();

        String allocationNo = query.getAllocationNo();
        if (StringUtils.isBlank(allocationNo)) {
            response.getWriter().write("调拨单号为空");
            return;
        }

        String skuStr = query.getSkuStr();
        List<String> skuStrList = new ArrayList<String>();
        if (StringUtils.isNotBlank(skuStr)) {
            skuStrList = new ArrayList<String>(Arrays.asList(skuStr.replaceAll(" ", "")));
        }

        WhApvAllocationQueryCondition queryCondition = new WhApvAllocationQueryCondition();
        queryCondition.setAllocationNo(allocationNo);
        WhApvAllocation apvAllocation = whApvAllocationService.queryApvAllocationDetail(queryCondition);
        if (null == apvAllocation) {
            response.getWriter().write("没有该调拨单");
            return;
        }
        List<WhApvAllocationItem> allocationItems = apvAllocation.getAllocationItems();
        if (CollectionUtils.isEmpty(allocationItems)) {
            response.getWriter().write("调拨单SKU为空");
            return;
        }

        List<WhApvAllocationItem> dataList = new ArrayList<WhApvAllocationItem>();

        String[] itemHeaders = { "SKU", "库位", "名称", "可用库存", "调拨数量", "拣货人/拣货时间", "拣货数量", "拣货差异", "装箱人/装箱时间", "箱号",
                "装箱数量", "装箱差异", "上架数量" };

        if (apvAllocation.getAllocationStatus() < AllocationStatusEnum.WAIT_PICK.intCode()
                || AllocationStatusEnum.DISCARD.intCode().equals(apvAllocation.getAllocationStatus())) {// 待处理前、或是已废弃
            itemHeaders = new String[] { "SKU", "库位", "名称", "可用库存", "未返架库内返架单", "未返架海外退件单", "未返架调拨返架单   ", "未上架入库单",
                    "未出库订单" };

            List<String> skuList = new ArrayList<String>();
            for (WhApvAllocationItem allocationItem : allocationItems) {
                String sku = allocationItem.getSku();
                skuList.add(sku);
            }
            List<WhApvAllocationItem> allocationSkuList = whApvAllocationService.queryApvAllocationSkuList(skuList);
            for (WhApvAllocationItem allocationItem : allocationSkuList) {
                // 库内返架
                boolean isReturnQuantity = false;
                if (query.getReturnQuantity() == 1) {
                    isReturnQuantity = true;
                }
                else if (query.getReturnQuantity() == 2 && allocationItem.getWaitReturnQuantity() <= 0) {
                    isReturnQuantity = true;
                }
                else if (query.getReturnQuantity() == 3 && allocationItem.getWaitReturnQuantity() > 0) {
                    isReturnQuantity = true;
                }
                // 海外退件
                boolean isAbroadReturnQuantity = false;
                if (query.getAbroadReturnQuantity() == 1) {
                    isAbroadReturnQuantity = true;
                }
                else if (query.getAbroadReturnQuantity() == 2 && allocationItem.getWaitAbroadReturnQuantity() <= 0) {
                    isAbroadReturnQuantity = true;
                }
                else if (query.getAbroadReturnQuantity() == 3 && allocationItem.getWaitAbroadReturnQuantity() > 0) {
                    isAbroadReturnQuantity = true;
                }
                // 调拨返架
                boolean isAllocationQuantity = false;
                if (query.getAllocationQuantity() == 1) {
                    isAllocationQuantity = true;
                }
                else if (query.getAllocationQuantity() == 2 && allocationItem.getWaitAllocationQuantity() <= 0) {
                    isAllocationQuantity = true;
                }
                else if (query.getAllocationQuantity() == 3 && allocationItem.getWaitAllocationQuantity() > 0) {
                    isAllocationQuantity = true;
                }
                // 未上架
                boolean isUpQuantity = false;
                if (query.getUpQuantity() == 1) {
                    isUpQuantity = true;
                }
                else if (query.getUpQuantity() == 2 && allocationItem.getWaitUpQuantity() <= 0) {
                    isUpQuantity = true;
                }
                else if (query.getUpQuantity() == 3 && allocationItem.getWaitUpQuantity() > 0) {
                    isUpQuantity = true;
                }
                // 未出库
                boolean isDeliveryQuantity = false;
                if (query.getDeliveryQuantity() == 1) {
                    isDeliveryQuantity = true;
                }
                else if (query.getDeliveryQuantity() == 2 && allocationItem.getWaitDeliveryQuantity() <= 0) {
                    isDeliveryQuantity = true;
                }
                else if (query.getDeliveryQuantity() == 3 && allocationItem.getWaitDeliveryQuantity() > 0) {
                    isDeliveryQuantity = true;
                }

                // 组装条件查询
                if (isReturnQuantity && isAbroadReturnQuantity && isAllocationQuantity && isUpQuantity
                        && isDeliveryQuantity) {
                    dataList.add(allocationItem);
                }
            }
        }
        else if (apvAllocation.getAllocationStatus() >= AllocationStatusEnum.WAIT_PICK.intCode()
                && apvAllocation.getAllocationStatus() < AllocationStatusEnum.WAIT_BOX.intCode()) {// 拣货差异
            for (WhApvAllocationItem allocationItem : allocationItems) {
                String sku = allocationItem.getSku();
                Integer pickDiff = query.getPickDiff();
                Integer allocationNum = allocationItem.getAllocationNum();
                Integer pickNum = allocationItem.getPickNum();

                boolean skuQuery = true;
                if (CollectionUtils.isNotEmpty(skuStrList) && !skuStrList.contains(sku)) {
                    skuQuery = false;
                }

                if (pickDiff == 1 && skuQuery) {// 不限
                    dataList.add(allocationItem);
                }
                else if (pickDiff == 2 && pickNum.compareTo(allocationNum) == 0 && skuQuery) {// 无差异
                    dataList.add(allocationItem);
                }
                else if (pickDiff == 3 && pickNum.compareTo(allocationNum) != 0 && skuQuery) {// 有差异
                    dataList.add(allocationItem);
                }
                else if (pickDiff == 4 && pickNum.compareTo(allocationNum) > 0 && skuQuery) {// 拣货大于调拨
                    dataList.add(allocationItem);
                }
                else if (pickDiff == 5 && pickNum.compareTo(allocationNum) < 0 && skuQuery) {// 拣货小于调拨
                    dataList.add(allocationItem);
                }
                else {
                    continue;
                }
            }
        }
        else if (apvAllocation.getAllocationStatus() >= AllocationStatusEnum.WAIT_BOX.intCode()
                && apvAllocation.getAllocationStatus() < AllocationStatusEnum.SIGNING.intCode()) {// 装箱差异
            for (WhApvAllocationItem allocationItem : allocationItems) {
                String sku = allocationItem.getSku();
                Integer boxDiff = query.getBoxDiff();
                Integer pickNum = allocationItem.getPickNum();
                Integer boxNum = allocationItem.getBoxNum();

                boolean skuQuery = true;
                if (CollectionUtils.isNotEmpty(skuStrList) && !skuStrList.contains(sku)) {
                    skuQuery = false;
                }

                if (boxDiff == 1 && skuQuery) {// 不限
                    dataList.add(allocationItem);
                }
                else if (boxDiff == 2 && boxNum.compareTo(pickNum) == 0 && skuQuery) {// 无差异
                    dataList.add(allocationItem);
                }
                else if (boxDiff == 3 && boxNum.compareTo(pickNum) != 0 && skuQuery) {// 有差异
                    dataList.add(allocationItem);
                }
                else if (boxDiff == 4 && boxNum.compareTo(pickNum) > 0 && skuQuery) {// 装箱大于拣货
                    dataList.add(allocationItem);
                }
                else if (boxDiff == 5 && boxNum.compareTo(pickNum) < 0 && skuQuery) {// 装箱小于拣货
                    dataList.add(allocationItem);
                }
                else {
                    continue;
                }
            }
        }
        else {// 上架差异
            for (WhApvAllocationItem allocationItem : allocationItems) {
                String sku = allocationItem.getSku();
                Integer upDiff = query.getUpDiff();
                Integer boxNum = allocationItem.getBoxNum();
                Integer upNum = allocationItem.getUpNum();

                Integer upStatus = 0;

                if (boxNum - upNum != 0) {
                    if (upNum != 0) {// 部分上架
                        upStatus = 1;
                    }
                    else {
                        upStatus = 0;// 未上架
                    }
                }
                else {
                    upStatus = 3;// 已上架
                }

                boolean skuQuery = true;
                if (CollectionUtils.isNotEmpty(skuStrList) && !skuStrList.contains(sku)) {
                    skuQuery = false;
                }

                if (upDiff == 1 && skuQuery) {// 不限
                    dataList.add(allocationItem);
                }
                else if (upDiff == 2 && upStatus == 0 && skuQuery) {// 未上架
                    dataList.add(allocationItem);
                }
                else if (upDiff == 3 && upStatus == 1 && skuQuery) {// 部分上架
                    dataList.add(allocationItem);
                }
                else if (upDiff == 4 && upStatus == 3 && skuQuery) {// 已上架
                    dataList.add(allocationItem);
                }
                else {
                    continue;
                }
            }
        }

        OutputStream os = null;
        try {
            String fileName = "调拨单SKU详情" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            final List<List<String>> recordsData = new ArrayList<List<String>>();
            if (apvAllocation.getAllocationStatus() < AllocationStatusEnum.WAIT_PICK.intCode()) {// 待处理前
                POIUtils.createExcel(itemHeaders, dataList, item -> {
                    recordsData.clear();
                    List<String> downloadList = new ArrayList<String>(headers.length);
                    downloadList.add(POIUtils.transferObj2Str(item.getSku()));
                    downloadList.add(POIUtils.transferObj2Str(item.getLocationNumber()));
                    downloadList.add(POIUtils.transferObj2Str(item.getSkuName()));
                    downloadList.add(POIUtils.transferObj2Str(item.getSurplusQuantity()));
                    downloadList.add(POIUtils.transferObj2Str(item.getWaitReturnQuantity()));
                    downloadList.add(POIUtils.transferObj2Str(item.getWaitAbroadReturnQuantity()));
                    downloadList.add(POIUtils.transferObj2Str(item.getWaitAllocationQuantity()));
                    downloadList.add(POIUtils.transferObj2Str(item.getWaitUpQuantity()));
                    downloadList.add(POIUtils.transferObj2Str(item.getWaitDeliveryQuantity()));
                    recordsData.add(downloadList);
                    return recordsData;
                }, true, os);
            }
            else {
                POIUtils.createExcel(itemHeaders, dataList, item -> {
                    recordsData.clear();
                    List<String> downloadList = new ArrayList<String>(headers.length);
                    downloadList.add(POIUtils.transferObj2Str(item.getSku()));
                    downloadList.add(POIUtils.transferObj2Str(item.getLocationNumber()));
                    downloadList.add(POIUtils.transferObj2Str(item.getSkuName()));
                    downloadList.add(POIUtils.transferObj2Str(item.getSurplusQuantity()));
                    downloadList.add(POIUtils.transferObj2Str(item.getAllocationNum()));
                    if (null != item.getPickBy() && null != item.getPickTime()) {
                        String pickName = TaglibUtils.getEmployeeNameByUserId(item.getPickBy());
                        String pickTime = POIUtils.transferObj2Str(item.getPickTime());
                        downloadList.add(pickName + "/" + pickTime);
                    }
                    else {
                        downloadList.add("");
                    }
                    downloadList.add(POIUtils.transferObj2Str(item.getPickNum()));
                    downloadList.add(POIUtils.transferObj2Str(item.getPickNum() - item.getAllocationNum()));
                    if (null != item.getBoxBy() && null != item.getBoxTime()) {
                        String boxName = TaglibUtils.getEmployeeNameByUserId(item.getBoxBy());
                        String boxTime = POIUtils.transferObj2Str(item.getBoxTime());
                        downloadList.add(boxName + "/" + boxTime);
                    }
                    else {
                        downloadList.add("");
                    }
                    downloadList.add(POIUtils.transferObj2Str(item.getBoxNo()));
                    downloadList.add(POIUtils.transferObj2Str(item.getBoxNum()));
                    downloadList.add(POIUtils.transferObj2Str(item.getBoxNum() - item.getPickNum()));
                    downloadList.add(POIUtils.transferObj2Str(item.getUpNum()));

                    recordsData.add(downloadList);

                    return recordsData;
                }, true, os);
            }

        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        finally {
            IOUtils.closeQuietly(os);
        }
    }

    // 调拨上架详情
    @RequestMapping(value = "up", method = { RequestMethod.GET })
    public String upList(@ModelAttribute("domain") WhApvAllocationDo domain) {
        initAllocationUpItem(domain);
        return "allocation/allocation_up_list";
    }

    private void initAllocationUpItem(WhApvAllocationDo domain) {
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("id", AllocationStatusEnum.SIGNING.getCode());
        map1.put("text", AllocationStatusEnum.SIGNING.getName());
        Map<String, Object> map2 = new HashMap<>();
        map2.put("id", AllocationStatusEnum.SEGMENT_STORAGE.getCode());
        map2.put("text", AllocationStatusEnum.SEGMENT_STORAGE.getName());
        Map<String, Object> map3 = new HashMap<>();
        map3.put("id", AllocationStatusEnum.ALL_STORAGE.getCode());
        map3.put("text", AllocationStatusEnum.ALL_STORAGE.getName());
        list.add(map1);
        list.add(map2);
        list.add(map3);
        domain.setStatusSelectJosn(JSON.toJSONString(list));
    }

    @RequestMapping(value = "up/search", method = { RequestMethod.POST })
    public String upSearch(@ModelAttribute("domain") WhApvAllocationDo domain) {
        initAllocationUpItem(domain);
        WhApvAllocationQueryCondition query = domain.getQuery();
        if (null == query) {
            query = new WhApvAllocationQueryCondition();
        }
        Pager pager = domain.getPage();
        //兼容SKU编码和唯一码
        if(StringUtils.isNotBlank(query.getSkuStr())){
            query.setSkuStr(CompatibleSkuUtils.getSku(query.getSkuStr()));
        }
        domain.setAllocationUpItems(whApvAllocationService.queryAllocationUpItemList(query, pager));
        return "allocation/allocation_up_list";
    }

    String[] upheaders = { "调拨单号", "调出仓", "调入仓", "SKU", "箱号", "名称", "调拨数量", "拣货数量", "装箱数量", "装箱人/装箱时间", "调拨入库单",
            "入库人/入库时间", "待QC", "待上架", "上架中", "已上架", "上架差异" };

    // 导出调拨上架详情
    @RequestMapping("up/download")
    public void upDownload(@ModelAttribute("domain") WhApvAllocationDo domain, HttpServletResponse response) {
        WhApvAllocationQueryCondition query = domain.getQuery();
        if (null == query) {
            query = new WhApvAllocationQueryCondition();
        }
        List<WhApvAllocationUpItem> dataList = whApvAllocationService.queryAllocationUpItemList(query, null);

        OutputStream os = null;
        try {
            String fileName = "调拨单上架详情" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            final List<List<String>> recordsData = new ArrayList<List<String>>();
            POIUtils.createExcel(upheaders, dataList, upItem -> {
                recordsData.clear();
                List<String> downloadList = new ArrayList<String>(headers.length);
                downloadList.add(POIUtils.transferObj2Str(upItem.getAllocationNo()));
                downloadList.add(POIUtils.transferObj2Str(upItem.getDeliveryWarehouseId() == 1 ? "汉海达"
                        : upItem.getDeliveryWarehouseId() == 2 ? "美景仓" : "-"));
                downloadList.add(POIUtils.transferObj2Str(
                        upItem.getDestWarehouseId() == 1 ? "汉海达" : upItem.getDestWarehouseId() == 2 ? "美景仓" : "-"));
                downloadList.add(POIUtils.transferObj2Str(upItem.getSku()));
                downloadList.add(POIUtils.transferObj2Str(upItem.getBoxNo()));
                downloadList.add(POIUtils.transferObj2Str(upItem.getSkuName()));
                downloadList.add(POIUtils.transferObj2Str(upItem.getAllocationNum()));
                downloadList.add(POIUtils.transferObj2Str(upItem.getPickNum()));
                downloadList.add(POIUtils.transferObj2Str(upItem.getBoxNum()));
                downloadList.add(TaglibUtils.getEmployeeNameByUserId(upItem.getBoxBy()) + "-"
                        + POIUtils.transferObj2Str(upItem.getBoxTime()));
                downloadList.add(POIUtils.transferObj2Str(upItem.getInId()));
                downloadList.add(TaglibUtils.getEmployeeNameByUserId(upItem.getCheckInCreateBy()) + "-"
                        + POIUtils.transferObj2Str(upItem.getCheckInCreateTime()));
                downloadList.add(POIUtils.transferObj2Str(upItem.getWaitQcQuantity()));
                downloadList.add(POIUtils.transferObj2Str(upItem.getWaitUpQuantity()));
                downloadList.add(POIUtils.transferObj2Str(upItem.getUpingQuantity()));
                downloadList.add(POIUtils.transferObj2Str(upItem.getCompleteUpQuantity()));
                downloadList.add(POIUtils.transferObj2Str(upItem.getCompleteUpQuantity() - upItem.getAllocationNum()));

                recordsData.add(downloadList);

                return recordsData;

            }, true, os);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        finally {
            IOUtils.closeQuietly(os);
        }
    }

    /**
     * 推送海外仓调拨装车数据到老仓
     * @param allocationId
     * @return
     */
    @RequestMapping(value = "asn/pushAsnLoadDataToOldWarehouse")
    @ResponseBody
    public ResponseJson pushAsnLoadDataToOldWarehouse(@RequestParam("allocationId") Integer allocationId) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if(allocationId == null){
            response.setMessage("海外仓调拨单ID不能为空");
            return response;
        }
        try {
            boolean result = whAsnAllocationPickingService.pushAsnLoadDataToOldWarehouse(Arrays.asList(allocationId));
            if (result){
                response.setMessage("操作成功");
                response.setStatus(StatusCode.SUCCESS);
            }else {
                response.setMessage("海外仓调拨数据不存在");
            }
        } catch (Exception e) {
            response.setStatus(e.getMessage());
            return response;
        }
        return response;
    }
}
