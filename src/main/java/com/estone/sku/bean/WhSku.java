package com.estone.sku.bean;

import com.alibaba.fastjson.JSONArray;
import com.estone.checkin.utils.GetUserNameOrEmployeeNameUtil;
import com.estone.common.util.*;
import com.estone.sku.enums.ProcessType;
import com.estone.sku.enums.WhSkuStatus;
import com.estone.sku.utils.SaleAttributeSettingUtils;
import com.estone.system.param.bean.SystemParam;
import com.estone.warehouse.bean.WhPackagingMaterialManagement;
import com.estone.warehouse.bean.WhRecord;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockFullDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.*;

public class WhSku implements Serializable, CompareParam {
    private static final long serialVersionUID = 1L;
    public static final String PACK_IMAGE_PATH = "/skuPackImage";

    /**
     * 主键id This field corresponds to the database column wh_sku.id
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer id;

    /**
     * sku This field corresponds to the database column wh_sku.sku
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String sku;

    /**
     * 产品名称 This field corresponds to the database column wh_sku.name
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String name;

    /**
     * 仓库 This field corresponds to the database column wh_sku.warehouse_id
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer warehouseId;

    /**
     * 产品分类 This field corresponds to the database column wh_sku.category_id
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer categoryId;

    /**
     * 产品分类全路径
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String categoryPath;

    /**
     * 包材 This field corresponds to the database column wh_sku.packaging_id
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer packagingId;

    /**
     * 包材名称
     */
    private String packagingName;

    /**
     * 搭配包材
     */
    private String matchMaterialsCode;

    /**
     * 搭配包材名称
     */
    private String matchMaterialsName;

    /**
     * 搭配包材图片
     */
    private String matchMaterialPictures;

    /**
     * 货位地板标识号 This field corresponds to the database column wh_sku.floor_location
     * todo 现作为加工类型使用，0:否，3：轻加工，5：一般加工，7：重加工
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer floorLocation;

    /**
     * 来货是否加工
     */
    private Integer checkInProcess;

    /**
     * 库位号 This field corresponds to the database column wh_sku.location_number
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String locationNumber;

    /**
     * 1 上架 2 下架 This field corresponds to the database column wh_sku.listing_status
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer listingStatus;

    /**
     * 采购员 This field corresponds to the database column wh_sku.product_buyer
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer productBuyer;

    /**
     * 开发员 This field corresponds to the database column wh_sku.product_developer
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer productDeveloper;

    /**
     * 摄影师 This field corresponds to the database column wh_sku.cameraman
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018

     *     */
    //TODO 现用于仓库贵重物品(WMS)标识，值=1
    private Integer cameraman;

    /**
     * 美工 This field corresponds to the database column wh_sku.art_designer
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer artDesigner;

    /**
     * 合并到的SKU column wh_sku.keywords
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String keywords;

    /**
     * 标题 This field corresponds to the database column wh_sku.title
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String title;

    /**
     * 重量 This field corresponds to the database column wh_sku.weight
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Double weight;

    /**
     * 上次修改前的重量,修改重量时使用
     */
    private Double lastWeight;

    /**
     * 标准重量
     */
    private Double netWeight;

    /**
     * 缩略图 This field corresponds to the database column wh_sku.thumbnail_url
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String thumbnailUrl;

    /**
     * 图片链接 This field corresponds to the database column wh_sku.image_url
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String imageUrl;

    /**
     * 产品链接 This field corresponds to the database column wh_sku.url
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String url;

    /**
     * 图片来源 This field corresponds to the database column wh_sku.image_source
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer imageSource;

    /**
     * 产品尺寸 This field corresponds to the database column wh_sku.contain
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String contain;

    /**
     * 颜色 This field corresponds to the database column wh_sku.color
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String color;

    /**
     * 产品尺寸规格 ,是否不规则产品:true;false;默认空即为false
     */
    private String specification;

    /**
     * 特性标签 This field corresponds to the database column wh_sku.feature
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String feature;

    /**
     * 中文报关名 This field corresponds to the database column wh_sku.customs_name_cn
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String customsNameCn;

    /**
     * 英文报关命 This field corresponds to the database column wh_sku.customs_name_en
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String customsNameEn;

    /**
     * 报关价值 This field corresponds to the database column wh_sku.customs_value
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Double customsValue;

    /**
     * 产品核重重量 This field corresponds to the database column wh_sku.customs_weight
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Double customsWeight;

    private Double lastCustomsWeight;

    /**
     * 成本价
     */
    private Double averagePurchasePrice;

    /**
     * 长 This field corresponds to the database column wh_sku.length
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */


    private Double length;    /**
     * 宽 This field corresponds to the database column wh_sku.width
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Double width;

    /**
     * 高 This field corresponds to the database column wh_sku.height
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Double height;

    /**
     * 是否需要QC This field corresponds to the database column wh_sku.is_qc
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Boolean isQc;

    /**
     * 质检方式 This field corresponds to the database column wh_sku.qc_type
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer qcType;

    /**
     * 是否采购 This field corresponds to the database column wh_sku.is_purchase
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Boolean isPurchase;

    /**
     * 创建时间 This field corresponds to the database column wh_sku.creation_date
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Timestamp creationDate;

    /**
     * 仓库系统更改sku属性同步另一个仓库 This field corresponds to the database column
     * wh_sku.completion_date
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Timestamp completionDate;

    /**
     * 创建人 This field corresponds to the database column wh_sku.created_by
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer createdBy;

    /**
     * 最后修改时间 This field corresponds to the database column wh_sku.last_update_date
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Timestamp lastUpdateDate;

    /**
     * 最后修改人 This field corresponds to the database column wh_sku.last_updated_by
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer lastUpdatedBy;

    /**
     * 流程 This field corresponds to the database column wh_sku.flow
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer flow;

    /**
     * 加工装袋修改次数 This field corresponds to the database column wh_sku.goods_quantity
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer goodsQuantity;

    /**
     * 品名 This field corresponds to the database column wh_sku.goods_name
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String goodsName;

    /**
     * 折扣系数 This field corresponds to the database column wh_sku.discount
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Double discount;

    /**
     * 最后修改标准重时间
     */
    private Timestamp discountTime;

    /**
     * 产品标识 1-废弃
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer discountExpire;

    /**
     * 是否带原包装发货-是=1，否=0
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer commissionPeriod;

    /**
     * 状态 This field corresponds to the database column wh_sku.status
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Integer status;

    /**
     * 海外仓成本 This field corresponds to the database column wh_sku.oversea_cost
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private Double overseaCost;

    /**
     * 描述 This field corresponds to the database column wh_sku.sku_decs
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String skuDecs;

    /**
     * SKU别名 This field corresponds to the database column wh_sku.sku_alias
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String skuAlias;

    /**
     * 产品描述 This field corresponds to the database column wh_sku.description
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String description;

    /**
     * 财产 This field corresponds to the database column wh_sku.propertys
     *
     * @mbggenerated Wed Aug 15 09:18:10 CST 2018
     */
    private String propertys;

    private Integer transitingQuantity;// 在途数量

    private Integer sevenDaysSaleQuantity;// 7天销量

    private Integer fourteenDaysSaleQuantity;// 14天销量

    private Integer thirtyDaysSaleQuantity;// 30天销量

    private Integer ninetyDaysSaleQuantity; // 90天销量

    private Integer thirtyDaysSalesOrders;// 销售频次,最近30天内出现的SKU的订单数

    private Integer thirtyDaysSalesDays;// 动销频次,最近30天内出现的SKU订单的天数

    private Integer shearSign;// 是否剪标，1表示剪标，0表示不剪标

    private Integer noStockUp;// 是否备货，1表示不备货，0表示备货

    private Timestamp enrollDate;// 产品创建时间/录入时间

    /**
     * 销售属性名
     */
    private String saleAttributeSettingStr;

    private String saleAttributeSettingStr1;

    /**
     * 库存记录
     */
    private WhRecord whRecord;

    /**
     * 包材
     */
    private WhPackagingMaterial whPackagingMaterial;

    /**
     * 类目
     */
    private WhSkuCategory whSkuCategory;

    /**
     * 标准尺寸
     */
    private String size;

    /**
     * 图片
     */
    private WhSkuImage skuImage;

    /**
     * 仓库图片排序
     */
    private WhSkuWmsImage wmsImage;

    // 子SKU图
    private List<String> showImages;
    // 尺寸图
    private List<String> cmbImages;
    //其他图片
    private List<String> otherImages;

    private boolean addLastWeightLog = false;

    // SKU质检备注
    private List<WhSkuQcCategory> skuQcCategorys = new ArrayList<WhSkuQcCategory>();

    /**
     * 库存
     */
    private WhStock whStock;

    /**
     * 条形码
     */
    private String skuBarCode;

    /**
     * 清空包材ID标记
     */
    private boolean clearPackagingId = false;

    private boolean clearMatchMaterialsCode = false;


    /**
     * 长(包裹尺寸)
     */
    private Double packLength;

    /**
     * 宽(包裹尺寸)
     */
    private Double packWidth;

    /**
     * 高(包裹尺寸)
     */
    private Double packHeight;

    //包裹重量记录
    private Double parcelWeighs;
    /**
     * 包装标准图片
     */
    private String packImage;

    /**
     * 是否进行包装标准图片变更的日志记录
     */
    private boolean addPackImageLog = false;

    /**
     * 导出服装属性
     */
    private String downLoadAttr;

    /**
     * 特殊标签
     */
    private List<WhSkuSpecialGoods> specialGoods;

    /**
     * 包材属性
     */
    private String packageAttribute;

    private Integer specialType;

    /**
     * 主sku
     */
    private WhSkuExtend whSkuExtend;

    public WhSkuExtend getWhSkuExtend() {
        return whSkuExtend;
    }

    public void setWhSkuExtend(WhSkuExtend whSkuExtend) {
        this.whSkuExtend = whSkuExtend;
    }
    /**
     * 材质
     */
    private String texture;

    /**
     * 海关编码
     */
    private String customsCode;

    /**
     * 是否是保质期SKU
     */
    private boolean expSku;

    /**
     * 本地仓+中转仓库存
     */
    private WhStockFullDTO whStockFull;

    private WhSkuColorSize skuColorSize;

    private ExpManage expManage;

    // 标准尺寸变更次数
    private Long standardSizeChangeCount;

    private String specialTag;

    private WhSkuTags whSkuTags;

    private WhSkuWithPmsInfo whSkuWithPmsInfo;

    public WhSkuWithPmsInfo getWhSkuWithPmsInfo() {
        return whSkuWithPmsInfo;
    }

    public void setWhSkuWithPmsInfo(WhSkuWithPmsInfo whSkuWithPmsInfo) {
        this.whSkuWithPmsInfo = whSkuWithPmsInfo;
    }

    public WhSkuTags getWhSkuTags() {
        return whSkuTags;
    }

    public void setWhSkuTags(WhSkuTags whSkuTags) {
        this.whSkuTags = whSkuTags;
    }


    public String getSpecialTag() {
        return specialTag;
    }

    public void setSpecialTag(String specialTag) {
        this.specialTag = specialTag;
    }

    public Long getStandardSizeChangeCount() {
        return standardSizeChangeCount;
    }

    public void setStandardSizeChangeCount(Long standardSizeChangeCount) {
        this.standardSizeChangeCount = standardSizeChangeCount;
    }
    public ExpManage getExpManage() {
        return expManage;
    }

    public void setExpManage(ExpManage expManage) {
        this.expManage = expManage;
    }

    public boolean isAddPackImageLog(){return addPackImageLog;}

    public void setAddPackImageLog(boolean addPackImageLog){this.addPackImageLog = addPackImageLog;}

    public WhSkuColorSize getSkuColorSize() {
        return skuColorSize;
    }

    public void setSkuColorSize(WhSkuColorSize skuColorSize) {
        this.skuColorSize = skuColorSize;
    }

    public WhStockFullDTO getWhStockFull() {
        return whStockFull;
    }

    public void setWhStockFull(WhStockFullDTO whStockFull) {
        this.whStockFull = whStockFull;
    }

    public boolean isExpSku() {
        return expSku;
    }

    public void setExpSku(boolean expSku) {
        this.expSku = expSku;
    }

    public Double getParcelWeighs() {
        return parcelWeighs;
    }

    public void setParcelWeighs(Double parcelWeighs) {
        this.parcelWeighs = parcelWeighs;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public String getOneCategoryPath() {
        return StringUtils.isBlank(categoryPath)?null:categoryPath.split(">")[0];
    }

    public Integer getCheckInProcess() {
        return checkInProcess;
    }

    public void setCheckInProcess(Integer checkInProcess) {
        this.checkInProcess = checkInProcess;
    }

    public String getPackageAttribute() {
        return packageAttribute;
    }

    public void setPackageAttribute(String packageAttribute) {
        this.packageAttribute = packageAttribute;
    }

    public List<WhSkuSpecialGoods> getSpecialGoods() {
        return specialGoods;
    }

    public void setSpecialGoods(List<WhSkuSpecialGoods> specialGoods) {
        this.specialGoods = specialGoods;
    }

    /**
     * sku服装属性
     */
    private List<Map<String,String>> wmsCategoryAtts;

    public List<Map<String, String>> getWmsCategoryAtts() {
        return wmsCategoryAtts;
    }

    public void setWmsCategoryAtts(List<Map<String, String>> wmsCategoryAtts) {
        this.wmsCategoryAtts = wmsCategoryAtts;
    }

    public String getDownLoadAttr() {
        return downLoadAttr;
    }

    public void setDownLoadAttr(String downLoadAttr) {
        this.downLoadAttr = downLoadAttr;
    }

    private List<WhSkuExpandAttr> whSkuExpandAttr;

    public List<WhSkuExpandAttr> getWhSkuExpandAttr() {
        return whSkuExpandAttr;
    }

    public void setWhSkuExpandAttr(List<WhSkuExpandAttr> whSkuExpandAttr) {
        this.whSkuExpandAttr = whSkuExpandAttr;
    }

    public String getSaleAttributeSettingStr1() {
        return saleAttributeSettingStr1;
    }

    public void setSaleAttributeSettingStr1(String saleAttributeSettingStr1) {
        this.saleAttributeSettingStr1 = saleAttributeSettingStr1;
    }

    public boolean isClearMatchMaterialsCode() {
        return clearMatchMaterialsCode;
    }

    public void setClearMatchMaterialsCode(boolean clearMatchMaterialsCode) {
        this.clearMatchMaterialsCode = clearMatchMaterialsCode;
    }

    public boolean isClearPackagingId() {
        return clearPackagingId;
    }

    public void setClearPackagingId(boolean clearPackagingId) {
        this.clearPackagingId = clearPackagingId;
    }

    public Integer getSevenDaysSaleQuantity() {
        return sevenDaysSaleQuantity;
    }

    public void setSevenDaysSaleQuantity(Integer sevenDaysSaleQuantity) {
        this.sevenDaysSaleQuantity = sevenDaysSaleQuantity;
    }

    public void addSkuQcCategorys(WhSkuQcCategory whSkuQcCategory) {
        skuQcCategorys.add(whSkuQcCategory);
    }

    public List<WhSkuQcCategory> getSkuQcCategorys() {
        return skuQcCategorys;
    }

    public void setSkuQcCategorys(List<WhSkuQcCategory> skuQcCategorys) {
        this.skuQcCategorys = skuQcCategorys;
    }

    public WhRecord getWhRecord() {
        return whRecord;
    }

    public void setWhRecord(WhRecord whRecord) {
        this.whRecord = whRecord;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }

    public Integer getFloorLocation() {
        return floorLocation;
    }

    public void setFloorLocation(Integer floorLocation) {
        this.floorLocation = floorLocation;
    }

    public String getLocationNumber() {
        return locationNumber;
    }

    public void setLocationNumber(String locationNumber) {
        this.locationNumber = locationNumber;
    }

    public Integer getListingStatus() {
        return listingStatus;
    }

    public void setListingStatus(Integer listingStatus) {
        this.listingStatus = listingStatus;
    }

    public Integer getProductBuyer() {
        return productBuyer;
    }

    public void setProductBuyer(Integer productBuyer) {
        this.productBuyer = productBuyer;
    }

    public Integer getProductDeveloper() {
        return productDeveloper;
    }

    public void setProductDeveloper(Integer productDeveloper) {
        this.productDeveloper = productDeveloper;
    }

    public Integer getCameraman() {
        return cameraman;
    }

    public void setCameraman(Integer cameraman) {
        this.cameraman = cameraman;
    }

    public Integer getArtDesigner() {
        return artDesigner;
    }

    public void setArtDesigner(Integer artDesigner) {
        this.artDesigner = artDesigner;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getLastWeight() {
        return lastWeight;
    }

    public void setLastWeight(Double lastWeight) {
        this.lastWeight = lastWeight;
    }

    public Double getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(Double netWeight) {
        this.netWeight = netWeight;
    }

    public String getThumbnailUrl() {
        return thumbnailUrl;
    }

    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getImageSource() {
        return imageSource;
    }

    public void setImageSource(Integer imageSource) {
        this.imageSource = imageSource;
    }

    public String getContain() {
        return contain;
    }

    public void setContain(String contain) {
        this.contain = contain;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }

    public String getCustomsNameCn() {
        return customsNameCn;
    }

    public void setCustomsNameCn(String customsNameCn) {
        this.customsNameCn = customsNameCn;
    }

    public String getCustomsNameEn() {
        return customsNameEn;
    }

    public void setCustomsNameEn(String customsNameEn) {
        this.customsNameEn = customsNameEn;
    }

    public Double getCustomsValue() {
        return customsValue;
    }

    public void setCustomsValue(Double customsValue) {
        this.customsValue = customsValue;
    }

    public Double getCustomsWeight() {
        return customsWeight;
    }

    public void setCustomsWeight(Double customsWeight) {
        this.customsWeight = customsWeight;
    }

    public Double getLastCustomsWeight() {
        return lastCustomsWeight;
    }

    public void setLastCustomsWeight(Double lastCustomsWeight) {
        this.lastCustomsWeight = lastCustomsWeight;
    }

    public Double getLength() {
        return length;
    }

    public void setLength(Double length) {
        this.length = length;
    }

    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Boolean getIsQc() {
        return isQc;
    }

    public void setIsQc(Boolean isQc) {
        this.isQc = isQc;
    }

    public Integer getQcType() {
        return qcType;
    }

    public void setQcType(Integer qcType) {
        this.qcType = qcType;
    }

    public Boolean getIsPurchase() {
        return isPurchase;
    }

    public void setIsPurchase(Boolean isPurchase) {
        this.isPurchase = isPurchase;
    }

    public Timestamp getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Timestamp creationDate) {
        this.creationDate = creationDate;
    }

    public Timestamp getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(Timestamp completionDate) {
        this.completionDate = completionDate;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Timestamp getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Timestamp lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Integer getFlow() {
        return flow;
    }

    public void setFlow(Integer flow) {
        this.flow = flow;
    }

    public Integer getGoodsQuantity() {
        return goodsQuantity;
    }

    public Integer getGoodsQuantityAdd() {
        return goodsQuantity == null ? 1 : goodsQuantity + 1;
    }

    public void setGoodsQuantity(Integer goodsQuantity) {
        this.goodsQuantity = goodsQuantity;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Timestamp getDiscountTime() {
        return discountTime;
    }

    public void setDiscountTime(Timestamp discountTime) {
        this.discountTime = discountTime;
    }

    public Integer getDiscountExpire() {
        return discountExpire;
    }

    public void setDiscountExpire(Integer discountExpire) {
        this.discountExpire = discountExpire;
    }

    public Integer getCommissionPeriod() {
        return commissionPeriod;
    }

    public void setCommissionPeriod(Integer commissionPeriod) {
        this.commissionPeriod = commissionPeriod;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Double getOverseaCost() {
        return overseaCost;
    }

    public void setOverseaCost(Double overseaCost) {
        this.overseaCost = overseaCost;
    }

    public String getSkuDecs() {
        return skuDecs;
    }

    public void setSkuDecs(String skuDecs) {
        this.skuDecs = skuDecs;
    }

    public String getSkuAlias() {
        return skuAlias;
    }

    public void setSkuAlias(String skuAlias) {
        this.skuAlias = skuAlias;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPropertys() {
        return propertys;
    }

    public void setPropertys(String propertys) {
        this.propertys = propertys;
    }

    public Integer getTransitingQuantity() {
        return transitingQuantity;
    }

    public void setTransitingQuantity(Integer transitingQuantity) {
        this.transitingQuantity = transitingQuantity;
    }

    public Integer getFourteenDaysSaleQuantity() {
        return fourteenDaysSaleQuantity;
    }

    public void setFourteenDaysSaleQuantity(Integer fourteenDaysSaleQuantity) {
        this.fourteenDaysSaleQuantity = fourteenDaysSaleQuantity;
    }

    public WhPackagingMaterial getWhPackagingMaterial() {
        return whPackagingMaterial;
    }

    public void setWhPackagingMaterial(WhPackagingMaterial whPackagingMaterial) {
        this.whPackagingMaterial = whPackagingMaterial;
    }

    public WhSkuCategory getWhSkuCategory() {
        return whSkuCategory;
    }

    public void setWhSkuCategory(WhSkuCategory whSkuCategory) {
        this.whSkuCategory = whSkuCategory;
    }

    public String getStatusName() {
        return WhSkuStatus.getDisplayByCode(status + "");
    }

    public Double getAveragePurchasePrice() {
        return averagePurchasePrice;
    }

    public void setAveragePurchasePrice(Double averagePurchasePrice) {
        this.averagePurchasePrice = averagePurchasePrice;
    }

    public String getSaleAttributeSettingStr() {
        SystemParam systemParam = CacheUtils.SystemParamGet("SALES_ATTRIBUTE_SETTING.SALES_ATTRIBUTE_SETTING");
        if (systemParam != null && StringUtils.isNotBlank(systemParam.getParamValue())) {
            List<SaleAttributeSetting> list = JSONArray.parseArray(systemParam.getParamValue(),
                    SaleAttributeSetting.class);

            Integer ninetyDaysSaleQuantity = this.ninetyDaysSaleQuantity == null ? 0 : this.ninetyDaysSaleQuantity;
            Integer thirtyDaysSaleQuantity = this.thirtyDaysSaleQuantity == null ? 0 : this.thirtyDaysSaleQuantity;
            Integer thirtyDaysSalesDays = this.thirtyDaysSalesDays == null ? 0 : this.thirtyDaysSalesDays;

            if (CollectionUtils.isNotEmpty(list)) {
                SaleAttributeSetting setting = list.get(list.size() - 1);
                if (setting != null) {
                    boolean isOk = true;
                    if (setting.getNum1() != null || setting.getNum2() != null || setting.getNum3() != null
                            || setting.getNum4() != null || setting.getNum5() != null) {
                        if (setting.getNum1() != null && enrollDate != null && !DateUtils.getBeforeDate(new Date(), setting.getNum1()).before(enrollDate)) {
                            isOk = false;
                        }
                        if (setting.getNum2() != null && !(thirtyDaysSaleQuantity >= setting.getNum2())) {
                            isOk = false;
                        }
                        if (setting.getNum3() != null && !(setting.getNum3() >= thirtyDaysSaleQuantity)) {
                            isOk = false;
                        }
                        if (setting.getNum4() != null && !(thirtyDaysSalesDays >= setting.getNum4())) {
                            isOk = false;
                        }
                        if (setting.getNum5() != null && !(setting.getNum5() >= thirtyDaysSalesDays)) {
                            isOk = false;
                        }
                    } else {
                        isOk = false;
                    }
                    if (isOk) {
                        saleAttributeSettingStr = "新品";
                        return saleAttributeSettingStr;
                    }
                }
                for (SaleAttributeSetting saleAttributeSetting : list) {
                    Integer[] numArray = saleAttributeSetting.getNumArray();
                    if (numArray == null) {
                        continue;
                    }
                    // thirtyDaysSaleQuantity 30  ninetyDaysSaleQuantity 90  thirtyDaysSalesDays 动销
                    boolean isOk = true;
                    // 销量必填
                    if (numArray[0] != null || numArray[1] != null || numArray[2] != null || numArray[3] != null) {
                        if (numArray[0] != null && !(numArray[0] >= thirtyDaysSaleQuantity)) {
                            isOk = false;
                        }
                        if (numArray[1] != null && !(thirtyDaysSaleQuantity > numArray[1])) {
                            isOk = false;
                        }
                        if (numArray[2] != null && !(numArray[2] >= ninetyDaysSaleQuantity)) {
                            isOk = false;
                        }
                        if (numArray[3] != null && !(ninetyDaysSaleQuantity > numArray[3])) {
                            isOk = false;
                        }
                    } else {
                        isOk = false;
                    }
                    if (isOk) {
                        for (int i = 4; i < numArray.length; i+=2) {
                            boolean isNotNull = false; // 必填一个值
                            isOk = true;
                            if (numArray[i] != null && !(numArray[i] >= thirtyDaysSalesDays)) {
                                isOk = false;
                            }
                            if (numArray[i + 1] != null && !(thirtyDaysSalesDays >= numArray[i + 1])) {
                                isOk = false;
                            }
                            if (numArray[i] != null || numArray[i + 1] != null) {
                                isNotNull = true;
                            }
                            if (isNotNull && isOk) {
                                saleAttributeSettingStr = SaleAttributeSettingUtils.STRING_ARRAY[i];
                                return saleAttributeSettingStr;
                            }
                        }
                    }
                }
            }
        }
        return saleAttributeSettingStr;
    }
    
    // 是否新品 去掉创建时间180天判断
    public boolean isNewProduct() {
        if (null == this.customsWeight || this.customsWeight <= 0) {
            return true;
        }
        return false;
    }
    // 判断尺寸是否为空
    public boolean isWeightEmpty() {
        if (null == this.length || this.length<=0 || null == this.width || this.width<=0 || null == this.height || this.height<=0) {
            return true;
        }
        return false;
    }

    public void setSaleAttributeSettingStr(String saleAttributeSettingStr) {
        this.saleAttributeSettingStr = saleAttributeSettingStr;
    }

    public Integer getThirtyDaysSaleQuantity() {
        return thirtyDaysSaleQuantity;
    }

    public void setThirtyDaysSaleQuantity(Integer thirtyDaysSaleQuantity) {
        this.thirtyDaysSaleQuantity = thirtyDaysSaleQuantity;
    }

    public Integer getNinetyDaysSaleQuantity() {
        return ninetyDaysSaleQuantity;
    }

    public void setNinetyDaysSaleQuantity(Integer ninetyDaysSaleQuantity) {
        this.ninetyDaysSaleQuantity = ninetyDaysSaleQuantity;
    }

    public Integer getThirtyDaysSalesOrders() {
        return thirtyDaysSalesOrders;
    }

    public void setThirtyDaysSalesOrders(Integer thirtyDaysSalesOrders) {
        this.thirtyDaysSalesOrders = thirtyDaysSalesOrders;
    }

    public Integer getThirtyDaysSalesDays() {
        return thirtyDaysSalesDays;
    }

    public void setThirtyDaysSalesDays(Integer thirtyDaysSalesDays) {
        this.thirtyDaysSalesDays = thirtyDaysSalesDays;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getProcessTypeName() {
        return ProcessType.getNameByCode(ProcessType.getIntCode(skuAlias, floorLocation).toString());
    }

    public WhSkuImage getSkuImage() {
        return skuImage;
    }

    public void setSkuImage(WhSkuImage skuImage) {
        this.skuImage = skuImage;
    }

    public Integer getShearSign() {
        return shearSign;
    }

    public void setShearSign(Integer shearSign) {
        this.shearSign = shearSign;
    }

    public Integer getNoStockUp() {
        return noStockUp;
    }

    public void setNoStockUp(Integer noStockUp) {
        this.noStockUp = noStockUp;
    }

    /**
     * 销售成本价
     * 
     * @return
     */
    public Double getDiscountAveragePurchasePrice() {

        if (discount != null && discount > 0d && averagePurchasePrice != null && averagePurchasePrice > 0) {
            return (double) Math.round(averagePurchasePrice * discount * 10000) / 10000.0;// 保留两位小数
        }
        else {
            return averagePurchasePrice;
        }
    }

    public Timestamp getEnrollDate() {
        return enrollDate;
    }

    public void setEnrollDate(Timestamp enrollDate) {
        this.enrollDate = enrollDate;
    }

    public boolean isAddLastWeightLog() {
        return addLastWeightLog;
    }

    public void setAddLastWeightLog(boolean addLastWeightLog) {
        this.addLastWeightLog = addLastWeightLog;
    }

    public WhStock getWhStock() {
        return whStock;
    }

    public void setWhStock(WhStock whStock) {
        this.whStock = whStock;
    }

    public String getSkuBarCode() {
        return skuBarCode;
    }

    public void setSkuBarCode(String skuBarCode) {
        this.skuBarCode = skuBarCode;
    }

    public String getProductBuyerName(){
        return GetUserNameOrEmployeeNameUtil.getEmployeeName(null,this.productBuyer);
    }

    public String getProductDeveloperName(){
        return GetUserNameOrEmployeeNameUtil.getEmployeeName(null,this.productDeveloper);
    }

    public String getMatchMaterialsCode() {
        return matchMaterialsCode;
    }

    public void setMatchMaterialsCode(String matchMaterialsCode) {
        this.matchMaterialsCode = matchMaterialsCode;
    }

    public String getPackagingName() {
        return packagingName;
    }

    public void setPackagingName(String packagingName) {
        this.packagingName = packagingName;
    }

    /**
     * 获得包材图片的完整url列表
     * @return
     */
    public List<String> getMatchMaterialPictureList(){
        List<String> pictures = CommonUtils.splitList(this.getMatchMaterialPictures(),",");
        List<String> images = new ArrayList<>();
        String url = SeaWeedFSUtils.URL;
        for (int i = 0; i < pictures.size(); i++) {
            String img = url + WhPackagingMaterialManagement.IMAGE_PATH + "/" + pictures.get(i);
            images.add(img);
        }
        return images;
    }

    public String getMatchMaterialPictures(){
        return matchMaterialPictures;
    }

    public void setMatchMaterialPictures(String matchMaterialPictures){
        this.matchMaterialPictures = matchMaterialPictures;
    }

    public String getMatchMaterialsName() {
        return matchMaterialsName;
    }

    public void setMatchMaterialsName(String matchMaterialsName) {
        this.matchMaterialsName = matchMaterialsName;
    }

    public Double getPackLength() {
        return packLength;
    }

    public void setPackLength(Double packLength) {
        this.packLength = packLength;
    }

    public Double getPackWidth() {
        return packWidth;
    }

    public void setPackWidth(Double packWidth) {
        this.packWidth = packWidth;
    }

    public Double getPackHeight() {
        return packHeight;
    }

    public void setPackHeight(Double packHeight) {
        this.packHeight = packHeight;
    }

    public String getPackImage() {
        return packImage;
    }

    public void setPackImage(String packImage) {
        this.packImage = packImage;
    }

    public List<String> getPackImageList(){
        if (StringUtils.isBlank(packImage)){
            return null;
        }
        return Arrays.asList(packImage.split(","));
    }
    public void setPackImageList(List<String> images){

    }

    public WhSkuWmsImage getWmsImage() {
        return wmsImage;
    }

    public void setWmsImage(WhSkuWmsImage wmsImage) {
        this.wmsImage = wmsImage;
    }

    public List<String> getShowImages() {
        return showImages;
    }

    public void setShowImages(List<String> showImages) {
        this.showImages = showImages;
    }

    public List<String> getCmbImages() {
        return cmbImages;
    }

    public void setCmbImages(List<String> cmbImages) {
        this.cmbImages = cmbImages;
    }

    public List<String> getOtherImages() {
        return otherImages;
    }

    public void setOtherImages(List<String> otherImages) {
        this.otherImages = otherImages;
    }

    public Integer getSpecialType() {
        return specialType;
    }

    public void setSpecialType(Integer specialType) {
        this.specialType = specialType;
    }

    public String getTexture() {
        return texture;
    }

    public void setTexture(String texture) {
        this.texture = texture;
    }

    public String getCustomsCode() {
        return customsCode;
    }

    public void setCustomsCode(String customsCode) {
        this.customsCode = customsCode;
    }

    @Override
    public String toString() {
        return "WhSku{" +
                "id=" + id +
                ", sku='" + sku + '\'' +
                ", name='" + name + '\'' +
                ", warehouseId=" + warehouseId +
                ", categoryId=" + categoryId +
                ", categoryPath='" + categoryPath + '\'' +
                ", packagingId=" + packagingId +
                ", packagingName='" + packagingName + '\'' +
                ", matchMaterialsCode='" + matchMaterialsCode + '\'' +
                ", matchMaterialsName='" + matchMaterialsName + '\'' +
                ", floorLocation=" + floorLocation +
                ", checkInProcess=" + checkInProcess +
                ", locationNumber='" + locationNumber + '\'' +
                ", listingStatus=" + listingStatus +
                ", productBuyer=" + productBuyer +
                ", productDeveloper=" + productDeveloper +
                ", cameraman=" + cameraman +
                ", artDesigner=" + artDesigner +
                ", keywords='" + keywords + '\'' +
                ", title='" + title + '\'' +
                ", weight=" + weight +
                ", lastWeight=" + lastWeight +
                ", netWeight=" + netWeight +
                ", thumbnailUrl='" + thumbnailUrl + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", url='" + url + '\'' +
                ", imageSource=" + imageSource +
                ", contain='" + contain + '\'' +
                ", color='" + color + '\'' +
                ", specification='" + specification + '\'' +
                ", feature='" + feature + '\'' +
                ", customsNameCn='" + customsNameCn + '\'' +
                ", customsNameEn='" + customsNameEn + '\'' +
                ", customsValue=" + customsValue +
                ", customsWeight=" + customsWeight +
                ", lastCustomsWeight=" + lastCustomsWeight +
                ", averagePurchasePrice=" + averagePurchasePrice +
                ", length=" + length +
                ", width=" + width +
                ", height=" + height +
                ", isQc=" + isQc +
                ", qcType=" + qcType +
                ", isPurchase=" + isPurchase +
                ", creationDate=" + creationDate +
                ", completionDate=" + completionDate +
                ", createdBy=" + createdBy +
                ", lastUpdateDate=" + lastUpdateDate +
                ", lastUpdatedBy=" + lastUpdatedBy +
                ", flow=" + flow +
                ", goodsQuantity=" + goodsQuantity +
                ", goodsName='" + goodsName + '\'' +
                ", discount=" + discount +
                ", discountTime=" + discountTime +
                ", discountExpire=" + discountExpire +
                ", commissionPeriod=" + commissionPeriod +
                ", status=" + status +
                ", overseaCost=" + overseaCost +
                ", skuDecs='" + skuDecs + '\'' +
                ", skuAlias='" + skuAlias + '\'' +
                ", description='" + description + '\'' +
                ", propertys='" + propertys + '\'' +
                ", transitingQuantity=" + transitingQuantity +
                ", sevenDaysSaleQuantity=" + sevenDaysSaleQuantity +
                ", fourteenDaysSaleQuantity=" + fourteenDaysSaleQuantity +
                ", thirtyDaysSaleQuantity=" + thirtyDaysSaleQuantity +
                ", ninetyDaysSaleQuantity=" + ninetyDaysSaleQuantity +
                ", thirtyDaysSalesOrders=" + thirtyDaysSalesOrders +
                ", thirtyDaysSalesDays=" + thirtyDaysSalesDays +
                ", shearSign=" + shearSign +
                ", noStockUp=" + noStockUp +
                ", enrollDate=" + enrollDate +
                ", saleAttributeSettingStr='" + saleAttributeSettingStr + '\'' +
                ", saleAttributeSettingStr1='" + saleAttributeSettingStr1 + '\'' +
                ", whRecord=" + whRecord +
                ", whPackagingMaterial=" + whPackagingMaterial +
                ", whSkuCategory=" + whSkuCategory +
                ", size='" + size + '\'' +
                ", skuImage=" + skuImage +
                ", wmsImage=" + wmsImage +
                ", showImages=" + showImages +
                ", cmbImages=" + cmbImages +
                ", otherImages=" + otherImages +
                ", addLastWeightLog=" + addLastWeightLog +
                ", skuQcCategorys=" + skuQcCategorys +
                ", whStock=" + whStock +
                ", skuBarCode='" + skuBarCode + '\'' +
                ", clearPackagingId=" + clearPackagingId +
                ", clearMatchMaterialsCode=" + clearMatchMaterialsCode +
                ", packLength=" + packLength +
                ", packWidth=" + packWidth +
                ", packHeight=" + packHeight +
                ", packImage='" + packImage + '\'' +
                ", downLoadAttr='" + downLoadAttr + '\'' +
                ", specialGoods=" + specialGoods +
                ", packageAttribute='" + packageAttribute + '\'' +
                ", specialType=" + specialType +
                ", whSkuExtend=" + whSkuExtend +
                ", texture='" + texture + '\'' +
                ", customsCode='" + customsCode + '\'' +
                ", wmsCategoryAtts=" + wmsCategoryAtts +
                ", whSkuExpandAttr=" + whSkuExpandAttr +
                '}';
    }

    // 添加库位
    public String addLocationNumber(String location) {
        if (StringUtils.isBlank(location))
            return locationNumber;
        List<String> locations = CommonUtils.splitList(locationNumber, ",");
        // 已存在
        if (locations.stream().anyMatch(s -> StringUtils.equalsIgnoreCase(s, location)))
            return locationNumber;
        locations.add(location);
        locationNumber = StringUtils.join(locations, ",");
        return locationNumber;
    }

    // 删除库位
    public String remLocationTag(String location){
        if (StringUtils.isBlank(location))
            return locationNumber;

        List<String> locations = CommonUtils.splitList(locationNumber, ",");
        if(locations.remove(location)) {
            locationNumber = StringUtils.join(locations, ",");
        }
        return locationNumber;
    }
    //  替换库位
    public String replaceLocationNumber(String dest, String orig) {
        if (StringUtils.isBlank(dest) || StringUtils.isBlank(orig))
            return locationNumber;
        remLocationTag(orig);
        return addLocationNumber(dest);
    }
    // 匹配库位
    public boolean matchLocationNumber(String location){
        if (StringUtils.isBlank(location) || StringUtils.isBlank(locationNumber))
            return false;
        List<String> locations = CommonUtils.splitList(locationNumber, ",");
        return locations.contains(location);
    }

    public String getSPU(){
        if (StringUtils.isBlank(this.getSku())){
            return null;
        }
        return this.getSku().split("-")[0];
    }
}