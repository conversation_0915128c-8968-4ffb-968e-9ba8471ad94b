package com.estone.sku.service.impl;

import com.estone.android.domain.AndroidProductDo;
import com.estone.checkin.utils.AfterSaleAndExpUtils;
import com.estone.checkin.utils.GetSkuQcCategoryDescUtil;
import com.estone.common.util.CreateTaskNoUtils;
import com.estone.common.util.DateUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.exquisite.service.BaseService;
import com.estone.multiplelocation.service.ExpRuleSettingService;
import com.estone.picking.enums.PickingTaskIsPrinting;
import com.estone.picking.enums.PickingTaskSkuStatus;
import com.estone.sku.bean.*;
import com.estone.sku.bean.dto.CeStockDTO;
import com.estone.sku.dao.CeManageDao;
import com.estone.sku.dao.MergeSkuTaskDao;
import com.estone.sku.domain.MergeSkuTagDo;
import com.estone.sku.enums.MargeSkuTaskStatus;
import com.estone.sku.enums.MergeSkuStatus;
import com.estone.sku.enums.MergeSkuType;
import com.estone.sku.service.*;
import com.estone.warehouse.bean.WhLocation;
import com.estone.warehouse.bean.WhLocationQueryCondition;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.enums.LocationStatus;
import com.estone.warehouse.enums.LocationTagEnum;
import com.estone.warehouse.enums.LocationType;
import com.estone.warehouse.service.WhAllocateLocationRuleService;
import com.estone.warehouse.service.WhLocationService;
import com.estone.warehouse.service.WhStockService;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("mergeSkuTaskService")
@Slf4j
public class MergeSkuTaskServiceImpl extends BaseService<MergeSkuTaskQueryCondition, MergeSkuTask> implements MergeSkuTaskService {
    @Resource
    private MergeSkuTaskDao mergeSkuTaskDao;

    @Resource
    private MergeSkuTaskItemService mergeSkuTaskItemService;

    @Resource
    private MergeSkuStockService mergeSkuStockService;

    @Resource
    private MergeSkuService mergeSkuService;

    @Resource
    private CeManageDao ceManageDao;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private MergeSkuStockMatchRecordService mergeSkuStockMatchRecordService;

    @Resource
    private WhStockService whStockService;

    @Resource
    private WhLocationService whLocationService;

    @Resource
    private WhAllocateLocationRuleService whAllocateLocationRuleService;

    @Resource
    private ExpManageService expManageService;

    @Override
    public MergeSkuTask getMergeSkuTask(Integer id) {
        MergeSkuTask mergeSkuTask = mergeSkuTaskDao.queryMergeSkuTask(id);
        return mergeSkuTask;
    }

    @Override
    public MergeSkuTask getMergeSkuTaskDetail(Integer id) {
        MergeSkuTask mergeSkuTask = mergeSkuTaskDao.queryMergeSkuTask(id);
        // 关联查询
        return mergeSkuTask;
    }

    @Override
    public MergeSkuTask queryMergeSkuTask(MergeSkuTaskQueryCondition query) {
        Assert.notNull(query, "query is null!");
        MergeSkuTask mergeSkuTask = mergeSkuTaskDao.queryMergeSkuTask(query);
        return mergeSkuTask;
    }

    @Override
    public List<MergeSkuTask> queryAllMergeSkuTasks() {
        return mergeSkuTaskDao.queryMergeSkuTaskList();
    }

    @Override
    public List<MergeSkuTask> queryMergeSkuTasks(MergeSkuTaskQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = mergeSkuTaskDao.queryMergeSkuTaskCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<MergeSkuTask>();
            }
        }
        List<MergeSkuTask> mergeSkuTasks = mergeSkuTaskDao.queryMergeSkuTaskList(query, pager);
        if (query.getIsQueryStock()){
            queryMergeSkuStock(mergeSkuTasks);
        }
        if (query.getIsUpSkuStock()){
            mergeSkuTasks=queryUpMergeSkuStock(mergeSkuTasks);
        }
        return mergeSkuTasks;
    }

    //上架查询合并sku是否翻新sku
    private List<MergeSkuTask> queryUpMergeSkuStock(List<MergeSkuTask> mergeSkuTasks) {
        List<Integer> mergeIdList = mergeSkuTasks.stream()
                .map(MergeSkuTask::getItems)
                .flatMap(Collection::stream)
                .map(MergeSkuTaskItem::getMergeId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mergeIdList)) {
            return new ArrayList<>();
        }
        MergeSkuQueryCondition queryCondition=new MergeSkuQueryCondition();
        queryCondition.setIds(mergeIdList);
        queryCondition.setQueryDiscardSkuStocks(true);
        queryCondition.setQueryMatchingSkuStocks(true);
        List<MergeSku> mergeSkuList = mergeSkuService.query(queryCondition, null);
        if (CollectionUtils.isEmpty(mergeSkuList)) {
            return new ArrayList<>();
        }

        MergeSkuTask mergeSkuTask = mergeSkuTasks.get(0);

        Map<Integer, MergeSku> mergeSkuMap = mergeSkuList.stream().collect(Collectors.toMap(MergeSku::getId, s -> s));

        Map<Integer, List<MergeSkuTaskItem>> skuTaskItemMap = mergeSkuTasks.stream()
                .map(MergeSkuTask::getItems)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(MergeSkuTaskItem::getMergeId));

        List<MergeSkuTaskItem> mergeSkuTaskItems=new ArrayList<>();
        skuTaskItemMap.forEach((mergeId,mergeSkuTaskItemList)->{
            MergeSku mergeSku = mergeSkuMap.get(mergeId);
            if (mergeSku==null) {
                return;
            }
            Map<Integer, MergeSkuStockMatchRecord> stockMatchRecordMap = Optional.ofNullable(mergeSku.getMergeSkuStockMatchRecords())
                    .orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toMap(MergeSkuStockMatchRecord::getDiscardStockId, Function.identity()));
//            //合并sku上架到目标库位
//            if (MergeSkuType.MERGE.getCode().equals(mergeSku.getType())) {
//                MergeSkuTaskItem mergeSkuTaskItem = mergeSkuTaskItemList.get(0);
//                int pickQuantity = mergeSkuTaskItemList.stream().mapToInt(i -> Optional.ofNullable(i.getPickQuantity()).orElse(0)).sum();
//                if (pickQuantity<=0)return;
//                mergeSkuTaskItem.setNeedUpQuantity(pickQuantity);
//                mergeSkuTaskItem.setSku(mergeSku.getMergeSku());
//                mergeSkuTaskItem.setLocationNumber(mergeSku.getMergeLocation());
//                mergeSkuTaskItem.setDiscardSku(mergeSku.getDiscardSku());
//                mergeSkuTaskItem.setType(mergeSku.getType());
//                mergeSkuTaskItems.add(mergeSkuTaskItem);
//                return;
//            }
//            //翻新sku上架到原来的库位
//            Map<Integer, WhStock> stockMap = Optional.ofNullable(mergeSku.getDiscardSkuStocks())
//                    .orElse(new ArrayList<>())
//                    .stream()
//                    .collect(Collectors.toMap(WhStock::getId, s -> s));

            for (MergeSkuTaskItem mergeSkuTaskItem : mergeSkuTaskItemList) {
                MergeSkuStockMatchRecord matchRecord = stockMatchRecordMap.get(mergeSkuTaskItem.getStockId());
                if (matchRecord==null) {
                    continue;
                }
                Integer pickQuantity = Optional.ofNullable(mergeSkuTaskItem.getPickQuantity()).orElse(0);
                if (pickQuantity<=0)continue;
                mergeSkuTaskItem.setDiscardSku(mergeSku.getDiscardSku());
                mergeSkuTaskItem.setSku(mergeSku.getMergeSku());
                mergeSkuTaskItem.setType(mergeSku.getType());
                mergeSkuTaskItem.setLocationNumber(matchRecord.getMatchStockLocation());
                mergeSkuTaskItem.setNeedUpQuantity(mergeSkuTaskItem.getPickQuantity());
                mergeSkuTaskItems.add(mergeSkuTaskItem);
            }
        });
        mergeSkuTask.setItems(mergeSkuTaskItems);
        return Collections.singletonList(mergeSkuTask);
    }


    private void queryMergeSkuStock(List<MergeSkuTask> mergeSkuTasks){
        List<Integer> stockIdList = mergeSkuTasks.stream()
                .map(MergeSkuTask::getItems)
                .flatMap(Collection::stream)
                .map(MergeSkuTaskItem::getStockId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockIdList)) {
            return;
        }
        List<CeStockDTO> stockDTOList = ceManageDao.queryLocationStock(null, stockIdList);
        if (CollectionUtils.isEmpty(stockDTOList)) {
            return;
        }
        Map<Integer, CeStockDTO> ceStockMap = stockDTOList.stream().collect(Collectors.toMap(CeStockDTO::getId,s->s));
        for (MergeSkuTask mergeSkuTask : mergeSkuTasks) {
            for (MergeSkuTaskItem item : mergeSkuTask.getItems()) {
                if (item.getStockId()==null) {
                   continue;
                }
                CeStockDTO stockDTO = ceStockMap.get(item.getStockId());
                if (stockDTO==null) {
                    continue;
                }
                item.setSkuName(stockDTO.getSkuName());
                item.setSku(stockDTO.getSku());
                item.setLocationNumber(stockDTO.getLocationNumber());
            }

        }

    }

    @Override
    public void createMergeSkuTask(MergeSkuTask mergeSkuTask) {
        try {
            mergeSkuTaskDao.createMergeSkuTask(mergeSkuTask);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateMergeSkuTask(List<MergeSkuTask> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                mergeSkuTaskDao.batchCreateMergeSkuTask(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteMergeSkuTask(Integer id) {
        try {
            mergeSkuTaskDao.deleteMergeSkuTask(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateMergeSkuTask(MergeSkuTask mergeSkuTask) {
        try {
            mergeSkuTaskDao.updateMergeSkuTask(mergeSkuTask);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateMergeSkuTask(List<MergeSkuTask> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                mergeSkuTaskDao.batchUpdateMergeSkuTask(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void doCreateTaskNo(List<MergeSku> mergeSkuList) {
        if (CollectionUtils.isEmpty(mergeSkuList)){
            return;
        }
        // 筛选出无库存要自动合并完成的sku明细
        List<MergeSku> needCompletedSkuList = mergeSkuList.stream()
                .filter(s-> CollectionUtils.isEmpty(s.getDiscardSkuStocks())
                        || s.getDiscardSkuStocks()
                            .stream()
                            .allMatch(w -> Optional.ofNullable(w.getSurplusAndFrozenQuantity()).orElse(0) <= 0)
                        )
                .collect(Collectors.toList());
        // 筛选出需要分配库位且创建任务的合并sku
        mergeSkuList = mergeSkuList.stream()
                .filter(s-> !needCompletedSkuList.contains(s))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needCompletedSkuList)){
            this.doAutoCompletedMergeSku(needCompletedSkuList);
        }
        if (CollectionUtils.isEmpty(mergeSkuList)){
            return;
        }

        MergeSkuTask mergeSkuTask = new MergeSkuTask();
        mergeSkuTask.setTaskNo(CreateTaskNoUtils.createBatNo("HB", "mergeSkuTask"));
        mergeSkuTask.setIsPrinting(PickingTaskIsPrinting.UNPRINTING.intCode());
        mergeSkuTask.setStatus(MargeSkuTaskStatus.PENDING.intCode());
        createMergeSkuTask(mergeSkuTask);

        List<String> mergeSkus = mergeSkuList.stream().map(MergeSku::getMergeSku).collect(Collectors.toList());
        List<String> discardSkus = mergeSkuList.stream().map(MergeSku::getDiscardSku).collect(Collectors.toList());
        List<String> skus = new ArrayList<>(mergeSkus.size() + discardSkus.size());
        skus.addAll(mergeSkus);
        skus.addAll(discardSkus);
        List<String> expSkuList = GetSkuQcCategoryDescUtil.getExpSkuList(skus);
        // 为每个合并sku分配库存信息
        for(MergeSku mergeSku : mergeSkuList) {
            this.allotMergeSkuStock(mergeSku, expSkuList);
        }

        //所有可用移动到冻结
        List<WhStock> whStockList = mergeSkuList.stream()
                .map(MergeSku::getDiscardSkuStocks)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(s->Optional.ofNullable(s.getSurplusAndFrozenQuantity()).orElse(0) > 0)
                .collect(Collectors.toList());

        List<String> skuList = whStockList.stream().map(WhStock::getSku).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(whStockList)) {
            throw new RuntimeException("无可用库存+冻结库存大于零的数据！");
        }
        mergeSkuStockService.updateMergeSkuSurplusStock(skuList,whStockList,mergeSkuTask.getTaskNo());


        // 拣货任务详情的集合
        List<MergeSkuTaskItem> items = new ArrayList<>();
        List<MergeSku> updateMergeSkuList=new ArrayList<>();
        for (MergeSku mergeSku : mergeSkuList) {
            if (CollectionUtils.isEmpty(mergeSku.getDiscardSkuStocks())) {
                continue;
            }
            for (WhStock discardSkuStock : mergeSku.getDiscardSkuStocks()) {
                Integer surplusAndFrozenQuantity = Optional.ofNullable(discardSkuStock.getSurplusAndFrozenQuantity()).orElse(0);
                if (surplusAndFrozenQuantity<=0){
                    continue;
                }
                MergeSkuTaskItem mergeSkuTaskItem=new MergeSkuTaskItem();
                mergeSkuTaskItem.setTaskId(mergeSkuTask.getId());
                mergeSkuTaskItem.setMergeId(mergeSku.getId());
                mergeSkuTaskItem.setPickStatus(PickingTaskSkuStatus.UNCOMPLETED.intCode());
                mergeSkuTaskItem.setUpStatus(PickingTaskSkuStatus.UNCOMPLETED.intCode());
                mergeSkuTaskItem.setStockId(discardSkuStock.getId());
                mergeSkuTaskItem.setNeedQuantity(surplusAndFrozenQuantity);
                items.add(mergeSkuTaskItem);
            }
            boolean anyMatch = mergeSku.getDiscardSkuStocks().stream().anyMatch(s -> Optional.ofNullable(s.getTotalLocationQuantity()).orElse(0) > 0);
            if (anyMatch){
                MergeSku update=new MergeSku();
                update.setId(mergeSku.getId());
                update.setStatus(MergeSkuStatus.MERGING.getCode());
                updateMergeSkuList.add(update);
            }
        }
        mergeSkuTaskItemService.batchCreateMergeSkuTaskItem(items);
        if (CollectionUtils.isNotEmpty(updateMergeSkuList)) {
            mergeSkuService.batchUpdateByPrimaryKey(updateMergeSkuList);
            updateMergeSkuList.forEach(s -> {
                SystemLogUtils.MERGE_SKU_LOG.log(s.getId(), "生成拣货任务，任务号：" + mergeSkuTask.getTaskNo());
            });
        }
        SystemLogUtils.MERGE_SKU_TASK_LOG.log(mergeSkuTask.getId(), "生成任务！" );
    }

    /**
     * 用于匹配废弃sku库存记录对应的合并到sku的库存记录
     * @param mergeSku
     * @param expSkuList 保质期sku
     */
    private void allotMergeSkuStock(MergeSku mergeSku,List<String> expSkuList){
        if (CollectionUtils.isEmpty(mergeSku.getDiscardSkuStocks())){
            return;
        }
        boolean MergeSkuIsExp = CollectionUtils.isNotEmpty(expSkuList) && expSkuList.contains(mergeSku.getMergeSku());

        Map<WhStock, WhStock> allotStockMap = new HashMap<>();
        for(WhStock discardSkuStock : mergeSku.getDiscardSkuStocks()) {
            Integer discardSkuQuantity = Optional.ofNullable(discardSkuStock.getSurplusAndFrozenQuantity()).orElse(0);
            if (discardSkuQuantity <= 0){
                continue;
            }
            WhStock allotStock = null;
            boolean discardExpSku = CollectionUtils.isNotEmpty(expSkuList) && expSkuList.contains(discardSkuStock.getSku());
            boolean discardAfterSale = discardSkuStock.existLocationTag(LocationTagEnum.SALED_BALANCE);

            WhStockQueryCondition stockQuery = new WhStockQueryCondition();
            stockQuery.setSku(mergeSku.getMergeSku());
            stockQuery.setQueryLocationType(true);
            List<WhStock> whStockList = whStockService.queryWhStocks(stockQuery, null);
            // 排除虚拟库位与售后结算库位
            List<WhStock> pickList = whStockList.stream()
                    .filter(w -> StringUtils.isNotBlank(w.getLocationNumber()) && w.getLocationType() != null
                            && !LocationType.VIRTUAL.intCode().equals(w.getLocationType())
                            && !w.existLocationTag(LocationTagEnum.SALED_BALANCE)
                            && !w.existLocationTag(LocationTagEnum.PRESTORE))
                    .collect(Collectors.toList());
            List<String> pickLocationNumbers = Optional.ofNullable(pickList).orElse(new ArrayList<>())
                    .stream()
                    .map(WhStock::getLocationNumber)
                    .collect(Collectors.toList());
            List<WhLocation> pickLocations = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(pickLocationNumbers)){
                WhLocationQueryCondition locationQuery = new WhLocationQueryCondition();
                locationQuery.setLocationList(pickLocationNumbers);
                pickLocations = whLocationService.queryWhLocations(locationQuery, null);
            }

            if (Objects.equals(MergeSkuType.RENEW.getCode(), mergeSku.getType())) {
                allotStock = this.matchLocation(mergeSku.getMergeSku(), pickLocations, discardExpSku, discardAfterSale);
            }else if(Objects.equals(MergeSkuType.MERGE.getCode(), mergeSku.getType())){
                if(!MergeSkuIsExp){ // 合并到sku非保质期
                    if (CollectionUtils.isNotEmpty(pickList)){
                        allotStock = pickList.get(0);
                    }else{
                        allotStock = this.matchLocation(mergeSku.getMergeSku(), pickLocations, false, false);
                    }
                }else if(!discardExpSku && !discardAfterSale){ // 合并到sku为保质期且被废弃sku为普通
                    allotStock = this.matchLocation(mergeSku.getMergeSku(), pickLocations, false, false);
                }else if(discardExpSku){ // 合并到sku为保质期且被废弃sku为保质期
                    ExpManageQueryCondition expManageQuery = new ExpManageQueryCondition();
                    expManageQuery.setSku(discardSkuStock.getSku());
                    expManageQuery.setStockId(discardSkuStock.getId());
                    List<ExpManage> expManages = expManageService.queryExpManages(expManageQuery, null);
                    Optional<ExpManage> expManageOptional = expManages.stream()
                            .filter(e -> e.getQuantity() > 0)
                            .sorted(Comparator.comparing(ExpManage::getExpDate))
                            .findFirst();
                    if (expManageOptional.isPresent()){
                        ExpManage expManage = expManageOptional.get();
                        // 匹配保质期规则
                        if (CollectionUtils.isNotEmpty(pickList)) {
                            Map<Integer,WhStock> mergeSkuStockMap = pickList.stream().collect(Collectors.toMap(WhStock::getId, Function.identity()));
                            List<Integer> pickStockIds = AfterSaleAndExpUtils.verifyLocationExp(mergeSku.getMergeSku(), new ArrayList<>(mergeSkuStockMap.keySet()),
                                    DateUtils.dateToString(expManage.getExpDate(), DateUtils.DEFAULT_FORMAT), expManage.getDays());
                            if (CollectionUtils.isNotEmpty(pickStockIds)) {
                                allotStock = mergeSkuStockMap.get(pickStockIds.get(0));
                            }
                        }
                    }

                    if (Objects.isNull(allotStock)){
                        allotStock = this.matchLocation(mergeSku.getMergeSku(), pickLocations, true, false);
                    }
                }

            }
            if(Objects.nonNull(allotStock)) {
                allotStockMap.put(discardSkuStock, allotStock);
            }
        }
        if(MapUtils.isEmpty(allotStockMap)){
            return;
        }
        // 保存匹配记录
        List<MergeSkuStockMatchRecord> mergeSkuStockMatchRecords = mergeSkuStockMatchRecordService.transformByMap(mergeSku, allotStockMap);
        mergeSkuStockMatchRecordService.batchCreateMergeSkuStockMatchRecord(mergeSkuStockMatchRecords);
    }

    private WhStock matchLocation(String sku, List<WhLocation> pickLocations, boolean expSku, boolean afterSale){
        WhStock allotStock = whAllocateLocationRuleService.matchSkuLocation(sku, pickLocations, expSku, afterSale);
        // 匹配不到库位，放弃规则得到库位
        if (Objects.nonNull(allotStock) && StringUtils.isBlank(allotStock.getLocationNumber())){
            String location = this.getLocation(LocationType.PICKING.intCode());
            if(StringUtils.isNotBlank(location)) {
                allotStock.setLocationNumber(location);
                allotStock.setLastUpdatedBy(DataContextHolder.getUserId());
                allotStock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                whStockService.updateWhStock(allotStock);

                // 更新sku的库位
                WhSkuQueryCondition skuQuery = new WhSkuQueryCondition();
                skuQuery.setSku(sku);
                List<WhSku> whSkus = whSkuService.queryWhSkus(skuQuery, null);
                if(CollectionUtils.isNotEmpty(whSkus)){
                    WhSku whSku = whSkus.get(0);
                    WhSku updateWhSku = new WhSku();
                    updateWhSku.setId(whSku.getId());
                    updateWhSku.setLocationNumber(whSku.addLocationNumber(location));
                    whSkuService.updateWhSku(updateWhSku);
                }
            }
        }
        return allotStock;
    }

    /**
     * 得到一个不到上限的库位
     * @return
     */
    private String getLocation(Integer locationType){
        WhLocationQueryCondition whLocationQueryCondition = new WhLocationQueryCondition();
        whLocationQueryCondition.setStorageDiffType(3);
        whLocationQueryCondition.setLocationType(locationType);
        Pager page = new Pager(1,1);
        List<WhLocation> whLocations = whLocationService.queryWhLocations(whLocationQueryCondition, page);
        if (CollectionUtils.isEmpty(whLocations)){
            return null;
        }
        return whLocations.get(0).getLocation();
    }

    /**
     * 进行无库存自动合并操作的合并sku
     * @param mergeSkuList 需要进行自动合并操作的无库存sku
     */
    @Override
    public void doAutoCompletedMergeSku(List<MergeSku> mergeSkuList){
        if (CollectionUtils.isEmpty(mergeSkuList)){
            return;
        }
        List<MergeSku> updateMergeSkuList = new ArrayList<>();
        for (MergeSku mergeSku : mergeSkuList){
            MergeSku update=new MergeSku();
            update.setId(mergeSku.getId());
            update.setStatus(MergeSkuStatus.COMPLETED.getCode());
            updateMergeSkuList.add(update);
        }
        mergeSkuService.batchUpdateByPrimaryKey(updateMergeSkuList);
        for(MergeSku mergeSku : mergeSkuList) {
            SystemLogUtils.MERGE_SKU_LOG.log(mergeSku.getId(), "无库存自动合并完成！");
        }
        mergeSkuService.pushToProductMS(mergeSkuList);
    }

    @Override
    public ApiResult<MergeSkuTagDo> getMergeSkuTag(String taskNo, String sku) {
        MergeSkuTaskQueryCondition mergeSkuTaskQueryCondition = new MergeSkuTaskQueryCondition();
        mergeSkuTaskQueryCondition.setTaskNo(taskNo);
        mergeSkuTaskQueryCondition.setDiscardSku(sku);
        List<MergeSkuTask> mergeSkuTasks = this.queryMergeSkuTasks(mergeSkuTaskQueryCondition, null);
        if (CollectionUtils.isEmpty(mergeSkuTasks)) {
            return ApiResult.newError("任务不存在!");
        }
        if (mergeSkuTasks.size() > 1) {
            return ApiResult.newError("存在多条任务对象!");
        }
        MergeSkuTask mergeSkuTask = mergeSkuTasks.get(0);
        if (!Objects.equals(MargeSkuTaskStatus.WAITING_UP.intCode(),mergeSkuTask.getStatus())){
            return ApiResult.newError("任务不为待上架状态，不允许进行打标操作!");
        }
        Map<Integer, List<MergeSkuTaskItem>> mergeSkuManageIdMap = mergeSkuTask.getItems()
                .stream()
                .collect(Collectors.groupingBy(MergeSkuTaskItem::getMergeId));
        // 查询出符合条件的对象
        MergeSkuQueryCondition mergeSkuQueryCondition = new MergeSkuQueryCondition();
        mergeSkuQueryCondition.setDiscardSku(sku);
        mergeSkuQueryCondition.setIds(new ArrayList<>(mergeSkuManageIdMap.keySet()));
        List<MergeSku> mergeSkus = mergeSkuService.query(mergeSkuQueryCondition, null);
        if (CollectionUtils.isEmpty(mergeSkus)) {
            return ApiResult.newError("找不到废弃sku管理对象!");
        }
        if (mergeSkus.size() > 1) {
            return ApiResult.newError("找到多个废弃sku管理对象！");
        }
        MergeSku mergeSku = mergeSkus.get(0);
        List<MergeSkuTaskItem> items = mergeSkuManageIdMap.get(mergeSku.getId());
        MergeSkuTagDo mergeSkuTagDo = new MergeSkuTagDo();
        mergeSkuTagDo.setMergeSku(mergeSku);
        mergeSkuTagDo.setItem(items);
        return ApiResult.newSuccess(mergeSkuTagDo);
    }

    @Override
    public void upNextStep(AndroidProductDo domain, List<MergeSkuTaskItem> mergeSkuTaskItems) {
        List<MergeSkuTaskItem> skuTaskItemList = mergeSkuTaskItems.stream().filter(i -> domain.getLocationNumber().equals(i.getLocationNumber())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuTaskItemList)) {
            skuTaskItemList=mergeSkuTaskItems;
            //没有指定库位任意上架库位并且判断库位是否为
            // 兼容原库位等于新库位，直接返回成功
            WhStockQueryCondition stockQuery = new WhStockQueryCondition();
            stockQuery.setSku(domain.getSku());
            stockQuery.setLocationNumber(domain.getLocationNumber());
            WhStock whStock = whStockService.queryWhStock(stockQuery);
            if (whStock == null) {
                WhLocationQueryCondition locationQuery = new WhLocationQueryCondition();
                locationQuery.setLocation(domain.getLocationNumber());
                WhLocation whLocation = whLocationService.queryWhLocation(locationQuery);
                if (null == whLocation) {
                    throw new RuntimeException("无效库位, 请确认");
                }

                // 货位状态
                Integer locationStatus = whLocation.getLocationStatus();
                if (!LocationStatus.STARTUP.intCode().equals(locationStatus)) {
                    throw new RuntimeException("货位已关闭, 请确认");
                }

                if (!LocationType.PICKING.intCode().equals(whLocation.getLocationType())) {
                    throw new RuntimeException(String.format("库位:%s,非拣货库位，不能上架",domain.getLocationNumber()));
                }

                // 实际品类
                Integer categoryActual = Optional.ofNullable(whLocation.getCategoryActual()).orElse(0) ;
                // 品类上限
                Integer categoryLimit =Optional.ofNullable(whLocation.getCategoryLimit()).orElse(0) ;

                if (categoryActual > categoryLimit || categoryLimit==0) {
                    throw new RuntimeException("此货位已达品类上限");
                }
            }
        }
        List<String> skuList = skuTaskItemList.stream().map(MergeSkuTaskItem::getDiscardSku).collect(Collectors.toList());
        skuList.add(domain.getSku());
        //上架
        mergeSkuStockService.doMergeSkuUp(skuList,skuTaskItemList,domain);

    }

    @Override
    public void doPickingNextStep(AndroidProductDo domain, List<MergeSkuTaskItem> mergeSkuTaskItems) {
        Integer pickQuantity = domain.getPickQuantity();
        for (MergeSkuTaskItem mergeSkuTaskItem : mergeSkuTaskItems) {
            Integer needQuantity = Optional.ofNullable(mergeSkuTaskItem.getNeedQuantity()).orElse(0);
            if (pickQuantity>needQuantity) {
                throw new RuntimeException(String.format("拣货数量：%s,大于需拣数量：%s",pickQuantity,needQuantity));
            }
            MergeSkuTaskItem taskItem = new MergeSkuTaskItem();
            taskItem.setId(mergeSkuTaskItem.getId());
            taskItem.setPickStatus(PickingTaskSkuStatus.COMPLETED.intCode());
            taskItem.setPickQuantity(domain.getPickQuantity());
            mergeSkuTaskItemService.updateMergeSkuTaskItem(taskItem);
        }
    }

    @Override
    public void doCompleteCePhotoTask(MergeSkuTask mergeSkuTask) {
        MergeSkuTask updateMergeSkuTask = new MergeSkuTask();
        updateMergeSkuTask.setId(mergeSkuTask.getId());
        if (MargeSkuTaskStatus.PICKING.intCode().equals(mergeSkuTask.getStatus())){
            updateMergeSkuTask.setStatus(MargeSkuTaskStatus.WAITING_UP.intCode());
            updateMergeSkuTask.setPickingEndDate(new Timestamp(System.currentTimeMillis()));
            updateMergeSkuTask(updateMergeSkuTask);
            SystemLogUtils.MERGE_SKU_TASK_LOG.log(mergeSkuTask.getId(),"拣货完成！");
        }
        if (MargeSkuTaskStatus.UPING.intCode().equals(mergeSkuTask.getStatus())){
            updateMergeSkuTask.setStatus(MargeSkuTaskStatus.COMPLETED.intCode());
            SystemLogUtils.MERGE_SKU_TASK_LOG.log(mergeSkuTask.getId(),"上架完成！");
            updateMergeSkuTask(updateMergeSkuTask);
            List<MergeSkuTaskItem> items = mergeSkuTask.getItems();
            List<Integer> mergeIdList = items.stream().map(MergeSkuTaskItem::getMergeId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(mergeIdList)) {
                return;
            }
            MergeSkuQueryCondition queryCondition=new MergeSkuQueryCondition();
            queryCondition.setIds(mergeIdList);
            List<MergeSku> mergeSkuList = mergeSkuService.query(queryCondition, null);
            if (CollectionUtils.isEmpty(mergeSkuList)) {
                return;
            }
            List<MergeSku> mergeSkus=new ArrayList<>();
            for (MergeSku mergeSku : mergeSkuList) {
                MergeSku updateMergeSku=new MergeSku();
                updateMergeSku.setId(mergeSku.getId());
                updateMergeSku.setStatus(MergeSkuStatus.COMPLETED.getCode());
                updateMergeSku.setCompletedTime(new Timestamp(System.currentTimeMillis()));
                mergeSkus.add(updateMergeSku);
                SystemLogUtils.MERGE_SKU_LOG.log(mergeSku.getId(), "上架完成" );
            }
            mergeSkuService.batchUpdateByPrimaryKey(mergeSkus);
            mergeSkuService.pushToProductMS(mergeSkuList);
        }
    }

    @Override
    public List<MergeSkuTask> list(MergeSkuTaskQueryCondition queryCondition, Pager pager) {
        return queryMergeSkuTasks(queryCondition,pager);
    }
}