package com.estone.wms;

import com.estone.wms.bean.ExceptionMarkConfig;
import com.estone.wms.bean.ExceptionMarkRequest;
import com.estone.wms.enums.ExceptionMarkReasonEnum;

import com.estone.wms.utils.ExceptionMarkConfigUtil;
import org.junit.Test;

import java.util.List;
import java.util.Map;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved. 
 * Project Name: wms
 * Package Name: com.estone.wms
 * File Name: ExceptionMarkConfigTest.java
 * Description: 异常标记配置测试类（简化版本）
 * Author: Amoi
 * Date: 2025-06-28
 * ---------------------------------------------------------------------------
 */
public class ExceptionMarkConfigTest {

    @Test
    public void testExceptionMarkReasonEnum() {
        System.out.println("=== 测试ExceptionMarkReasonEnum（简化版本）===");
        
        // 测试枚举基本功能
        System.out.println("所有预设原因:");
        for (ExceptionMarkReasonEnum reason : ExceptionMarkReasonEnum.values()) {
            System.out.println(String.format("枚举名: %s, 显示名称: %s", 
                reason.name(), reason.getName()));
        }
        
        // 测试根据枚举名称获取
        System.out.println("\n根据枚举名称获取:");
        ExceptionMarkReasonEnum qualityIssue = ExceptionMarkReasonEnum.getByName("QUALITY_ISSUE");
        System.out.println("QUALITY_ISSUE -> " + (qualityIssue != null ? qualityIssue.getName() : "未找到"));
        
        ExceptionMarkReasonEnum unknown = ExceptionMarkReasonEnum.getByName("UNKNOWN_CODE");
        System.out.println("UNKNOWN_CODE -> " + (unknown != null ? unknown.getName() : "未找到"));
        
        // 测试根据显示名称获取
        System.out.println("\n根据显示名称获取:");
        ExceptionMarkReasonEnum qualityByDisplay = ExceptionMarkReasonEnum.getByDisplayName("质量问题");
        System.out.println("质量问题 -> " + (qualityByDisplay != null ? qualityByDisplay.name() : "未找到"));
        
        ExceptionMarkReasonEnum unknownByDisplay = ExceptionMarkReasonEnum.getByDisplayName("未知原因");
        System.out.println("未知原因 -> " + (unknownByDisplay != null ? unknownByDisplay.name() : "未找到"));
        
        // 测试枚举名称验证
        System.out.println("\n枚举名称验证:");
        System.out.println("QUALITY_ISSUE有效: " + ExceptionMarkReasonEnum.isValidEnumName("QUALITY_ISSUE"));
        System.out.println("UNKNOWN_CODE有效: " + ExceptionMarkReasonEnum.isValidEnumName("UNKNOWN_CODE"));
        
        // 测试显示名称验证
        System.out.println("\n显示名称验证:");
        System.out.println("质量问题有效: " + ExceptionMarkReasonEnum.isValidDisplayName("质量问题"));
        System.out.println("未知原因有效: " + ExceptionMarkReasonEnum.isValidDisplayName("未知原因"));
        
        // 测试获取所有原因Map
        System.out.println("\n所有原因Map:");
        Map<String, String> allReasons = ExceptionMarkReasonEnum.getAllReasonsMap();
        allReasons.forEach((enumName, displayName) -> 
            System.out.println(String.format("%s: %s", enumName, displayName)));
        
        // 测试获取所有显示名称
        System.out.println("\n所有显示名称:");
        String[] displayNames = ExceptionMarkReasonEnum.getAllDisplayNames();
        for (String displayName : displayNames) {
            System.out.println("- " + displayName);
        }
        
        // 测试向后兼容方法（已废弃）
        System.out.println("\n向后兼容方法测试:");
        System.out.println("getByCode(QUALITY_ISSUE) -> " + 
            ExceptionMarkReasonEnum.getNameByCode("QUALITY_ISSUE"));
        System.out.println("isValidCode(QUALITY_ISSUE) -> " + 
            ExceptionMarkReasonEnum.isValidCode("QUALITY_ISSUE"));
    }

    @Test
    public void testExceptionMarkConfig() {
        System.out.println("\n=== 测试ExceptionMarkConfig ===");
        
        ExceptionMarkConfig config = new ExceptionMarkConfig();
        
        // 测试预设原因验证
        System.out.println("预设原因验证:");
        System.out.println("QUALITY_ISSUE存在: " + config.containsReason("QUALITY_ISSUE"));
        System.out.println("UNKNOWN_CODE存在: " + config.containsReason("UNKNOWN_CODE"));
        
        // 测试添加自定义原因
        System.out.println("\n自定义原因测试:");
        String customReasonName = "商品破损严重";
        config.addMarkReason(customReasonName);
        System.out.println("自定义原因名称: " + customReasonName);
        System.out.println("自定义原因存在: " + config.containsReason(customReasonName));
        System.out.println("是否为自定义: " + (!config.getMarkReasonList().contains("质量问题")));
        
        // 测试检查原因是否存在
        System.out.println("\n检查原因是否存在:");
        System.out.println("质量问题存在: " + config.containsReason("质量问题"));
        System.out.println(customReasonName + "存在: " + config.containsReason(customReasonName));
        
        // 测试获取可用原因
        System.out.println("\n可用原因列表:");
        List<String> availableReasons = config.getAvailableReasons();
        for (int i = 0; i < availableReasons.size(); i++) {
            System.out.println(String.format("[%d]: %s", i, availableReasons.get(i)));
        }
    }

    @Test
    public void testExceptionMarkConfigUtil() {
        System.out.println("\n=== 测试ExceptionMarkConfigUtil ===");
        
        // 测试获取可用原因
        System.out.println("工具类获取可用原因:");
        ExceptionMarkConfig config = ExceptionMarkConfigUtil.getExceptionMarkConfig();
        List<String> reasons = config.getAvailableReasons();
        for (int i = 0; i < reasons.size(); i++) {
            System.out.println(String.format("[%d]: %s", i, reasons.get(i)));
        }
        
        // 测试原因验证
        System.out.println("\n工具类验证原因:");
        System.out.println("质量问题存在: " + config.containsReason("质量问题"));
        System.out.println("无效原因存在: " + config.containsReason("无效原因"));
        
        // 测试配置验证
        System.out.println("\n配置验证:");
        String validationResult = ExceptionMarkConfigUtil.validateConfig(config);
        System.out.println("配置验证结果: " + (validationResult == null ? "通过" : validationResult));
    }

    @Test
    public void testExceptionMarkRequest() {
        System.out.println("\n=== 测试ExceptionMarkRequest ===");
        
        // 测试预设原因请求
        ExceptionMarkRequest request1 = new ExceptionMarkRequest();
        request1.setExceptionId(12345);
        request1.setMarkReason("QUALITY_ISSUE");
        request1.setMarkRemark("商品包装损坏，需要退货处理");
        
        System.out.println("预设原因请求:");
        System.out.println("请求有效: " + request1.isValid());
        System.out.println("最终原因代码: " + request1.getFinalMarkReason());
        System.out.println("验证错误: " + request1.getValidationError());
        
        // 测试自定义原因请求
        ExceptionMarkRequest request2 = new ExceptionMarkRequest();
        request2.setExceptionId(12346);
        request2.setMarkReason("CUSTOM");
        request2.setCustomReason("商品尺寸不符合要求");
        request2.setMarkRemark("与订单描述的尺寸不一致");
        
        System.out.println("\n自定义原因请求:");
        System.out.println("请求有效: " + request2.isValid());
        System.out.println("最终原因代码: " + request2.getFinalMarkReason());
        System.out.println("验证错误: " + request2.getValidationError());
        
        // 测试无效请求
        ExceptionMarkRequest request3 = new ExceptionMarkRequest();
        request3.setExceptionId(12347);
        request3.setMarkReason("CUSTOM");
        // 故意不设置customReason
        
        System.out.println("\n无效请求:");
        System.out.println("请求有效: " + request3.isValid());
        System.out.println("验证错误: " + request3.getValidationError());
    }

    @Test
    public void testNewEnumFeatures() {
        System.out.println("\n=== 测试新增枚举功能 ===");
        
        // 测试新增的查询方法
        System.out.println("测试新增查询方法:");
        
        // 测试按枚举名称查询
        ExceptionMarkReasonEnum reason1 = ExceptionMarkReasonEnum.getByName("quality_issue");
        System.out.println("getByName(quality_issue) -> " + (reason1 != null ? reason1.getName() : "未找到"));
        
        ExceptionMarkReasonEnum reason2 = ExceptionMarkReasonEnum.getByName("QUALITY_ISSUE");
        System.out.println("getByName(QUALITY_ISSUE) -> " + (reason2 != null ? reason2.getName() : "未找到"));
        
        // 测试按显示名称查询
        ExceptionMarkReasonEnum reason3 = ExceptionMarkReasonEnum.getByDisplayName("质量问题");
        System.out.println("getByDisplayName(质量问题) -> " + (reason3 != null ? reason3.name() : "未找到"));
        
        // 测试验证方法
        System.out.println("\n测试验证方法:");
        System.out.println("isValidEnumName(QUALITY_ISSUE): " + ExceptionMarkReasonEnum.isValidEnumName("QUALITY_ISSUE"));
        System.out.println("isValidEnumName(INVALID): " + ExceptionMarkReasonEnum.isValidEnumName("INVALID"));
        System.out.println("isValidDisplayName(质量问题): " + ExceptionMarkReasonEnum.isValidDisplayName("质量问题"));
        System.out.println("isValidDisplayName(无效原因): " + ExceptionMarkReasonEnum.isValidDisplayName("无效原因"));
        
        // 测试getAllDisplayNames方法
        System.out.println("\n测试getAllDisplayNames方法:");
        String[] displayNames = ExceptionMarkReasonEnum.getAllDisplayNames();
        System.out.println("显示名称数组长度: " + displayNames.length);
        for (int i = 0; i < Math.min(3, displayNames.length); i++) {
            System.out.println("  [" + i + "]: " + displayNames[i]);
        }
    }

    @Test
    public void testIntegration() {
        System.out.println("\n=== 集成测试 ===");
        
        ExceptionMarkConfig config = ExceptionMarkConfigUtil.getExceptionMarkConfig();
        
        // 模拟前端获取标记原因选项
        System.out.println("前端获取标记原因选项:");
        List<String> options = config.getAvailableReasons();
        for (int i = 0; i < options.size(); i++) {
            System.out.println(String.format("  <option value=\"%s\">%s</option>", options.get(i), options.get(i)));
        }
        
        // 模拟用户选择预设原因
        System.out.println("\n用户选择预设原因 - 质量问题:");
        ExceptionMarkRequest request1 = new ExceptionMarkRequest();
        request1.setExceptionId(1001);
        request1.setMarkReason("质量问题");
        request1.setMarkRemark("商品表面有划痕");
        
        if (request1.isValid()) {
            String finalReason = request1.getFinalMarkReason();
            System.out.println(String.format("  异常单%d标记为: %s", 
                request1.getExceptionId(), finalReason));
        }
        
        // 模拟用户选择自定义原因
        System.out.println("\n用户选择自定义原因:");
        ExceptionMarkRequest request2 = new ExceptionMarkRequest();
        request2.setExceptionId(1002);
        request2.setMarkReason("CUSTOM");
        request2.setCustomReason("商品颜色与图片不符");
        request2.setMarkRemark("客户反馈颜色差异较大");
        
        if (request2.isValid()) {
            String finalReason = request2.getFinalMarkReason();
            System.out.println(String.format("  异常单%d标记为: %s", 
                request2.getExceptionId(), finalReason));
        }
        
        // 测试简化后的枚举在实际业务中的使用
        System.out.println("\n业务场景测试:");
        System.out.println("1. 检查所有预设原因都可用:");
        for (ExceptionMarkReasonEnum reason : ExceptionMarkReasonEnum.values()) {
            boolean available = config.containsReason(reason.getName());
            System.out.println(String.format("  %s: %s", 
                reason.getName(), available ? "可用" : "不可用"));
        }
        
        System.out.println("\n2. 验证List结构的性能:");
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            config.getAvailableReasons();
        }
        long endTime = System.currentTimeMillis();
        System.out.println(String.format("  1000次getAvailableReasons()调用耗时: %dms", endTime - startTime));
    }
} 