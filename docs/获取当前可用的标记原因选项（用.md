**接口名称:**

获取当前可用的标记原因选项（用

**接口描述:**

获取当前可用的标记原因选项（用于前端下拉）

**请求路径:**

/exceptionMarkConfig/getAvailableReasons

**请求方式:**

POST

**请求参数:**

- Header

|参数名|参数值|必填|描述|
|:-----|:-----|:-----|:-----|
|Content-Type|application/x-www-form-urlencoded|Y||


- Param



- Body



**请求示例:**



**返回参数:**

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|success|boolean|N|api返回状态|
|errorMsg|String|N|返回错误信息|
|result|List<String>|N|api返回值|
|exceptionCode|String|N|异常编码|


**返回示例:**

```JSON
{
    "success": false,
    "errorMsg": "",
    "result": [
        ""
    ],
    "exceptionCode": ""
}
```

