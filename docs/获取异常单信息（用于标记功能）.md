**接口名称:**

获取异常单信息（用于标记功能）

**接口描述:**

获取异常单信息（用于标记功能）

**请求路径:**

/checkInException/getExceptionInfo/{id}

**请求方式:**

GET

**请求参数:**

- Header

|参数名|参数值|必填|描述|
|:-----|:-----|:-----|:-----|
|Content-Type|application/x-www-form-urlencoded|Y||


- Param

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|id|Integer|N||


- Body



**请求示例:**

```Form
id=null
```

**返回参数:**

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|success|boolean|N|api返回状态|
|errorMsg|String|N|返回错误信息|
|result|T|N|api返回值|
|exceptionCode|String|N|异常编码|


**返回示例:**

```JSON
{
    "success": false,
    "errorMsg": "",
    "result": {},
    "exceptionCode": ""
}
```

