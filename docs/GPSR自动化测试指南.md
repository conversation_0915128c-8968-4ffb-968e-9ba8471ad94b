# GPSR自动化测试指南

## 📋 概述

GPSR缓存管理器测试页面现已支持完整的自动化测试功能，可以通过URL参数或页面操作进行无人值守的自动化测试，适用于持续集成、回归测试和批量验证场景。

## 🚀 自动化测试功能特性

### 核心功能
- **URL参数驱动**：通过URL参数传入测试数据和配置
- **自动化流程**：按预定顺序自动执行所有测试步骤
- **批量测试**：支持多个发货单的批量自动化测试
- **结果导出**：生成结构化的JSON测试报告
- **错误重试**：自动重试失败的测试步骤
- **进度监控**：实时显示测试进度和状态

### 测试步骤
1. **网络连接测试** - 验证后端接口可访问性
2. **后端接口测试** - 测试GPSR数据获取
3. **预加载GPSR** - 测试缓存预加载功能
4. **验证GPSR数据** - 验证缓存数据完整性
5. **打印GPSR** - 测试打印功能

## 🔧 URL参数配置

### 基础参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `apvNo` | string | 发货单号 | `YSTN25061015000651` |
| `sku` | string | SKU代码（可选） | `4NB401005-8` |
| `apiType` | string | 接口类型：local/fba | `local` |
| `autoTest` | boolean | 是否自动执行测试 | `true` |

### 批量测试参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `apvNos` | string | 多个发货单号（逗号分隔） | `APV001,APV002,APV003` |
| `batchTest` | boolean | 是否批量测试 | `true` |

### 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `stepDelay` | number | 2000 | 步骤间延迟（毫秒） |
| `retryCount` | number | 2 | 失败重试次数 |
| `timeout` | number | 10000 | 请求超时时间（毫秒） |
| `skipPrint` | boolean | false | 是否跳过打印测试 |
| `skipValidation` | boolean | false | 是否跳过验证测试 |

## 📝 使用示例

### 1. 单个发货单自动化测试

```
http://your-domain/your-app/html/test/gpsr-cache-test.html?apvNo=YSTN25061015000651&sku=4NB401005-8&apiType=local&autoTest=true
```

**说明**：
- 自动测试发货单 `YSTN25061015000651`
- 指定SKU为 `4NB401005-8`
- 使用本地接口
- 3秒后自动开始测试

### 2. 批量发货单自动化测试

```
http://your-domain/your-app/html/test/gpsr-cache-test.html?apvNos=APV001,APV002,APV003&batchTest=true&apiType=local&autoTest=true
```

**说明**：
- 批量测试3个发货单
- 每个发货单依次执行完整测试流程
- 生成批量测试报告

### 3. 自定义配置的自动化测试

```
http://your-domain/your-app/html/test/gpsr-cache-test.html?apvNo=YSTN25061015000651&autoTest=true&stepDelay=1000&retryCount=3&timeout=15000&skipPrint=true
```

**说明**：
- 步骤间延迟1秒
- 失败重试3次
- 超时时间15秒
- 跳过打印测试

## 🧪 手动启动自动化测试

### 页面操作方式

1. **打开测试页面**
   ```
   http://your-domain/your-app/html/test/gpsr-cache-test.html
   ```

2. **输入测试数据**
   - 发货单号：`YSTN25061015000651`
   - SKU：`4NB401005-8`
   - 接口类型：选择"本地接口"

3. **启动自动化测试**
   - 点击 **"🚀 开始自动化测试"** 按钮
   - 或点击 **"批量预加载"** 进行批量测试

4. **生成自动化URL**
   - 点击 **"生成自动化测试URL"** 按钮
   - 复制生成的URL用于后续自动化测试

## 📊 测试结果和报告

### 实时监控

测试过程中可以观察到：
- **进度条**：显示整体测试进度
- **步骤状态**：每个测试步骤的执行状态
  - 🔄 执行中
  - ✅ 成功
  - ❌ 失败
  - 💥 异常
- **日志输出**：详细的执行日志

### 测试报告结构

#### 单个测试报告
```json
{
  "testInfo": {
    "apvNo": "YSTN25061015000651",
    "sku": "4NB401005-8",
    "apiType": "local",
    "startTime": "2025-06-30T10:00:00.000Z",
    "endTime": "2025-06-30T10:00:15.000Z",
    "totalDuration": 15000
  },
  "summary": {
    "totalSteps": 5,
    "successCount": 4,
    "failedCount": 1,
    "errorCount": 0,
    "successRate": "80.00%"
  },
  "steps": [
    {
      "step": "网络连接测试",
      "status": "success",
      "duration": 1200,
      "result": { "message": "网络连接正常" },
      "timestamp": "2025-06-30T10:00:01.000Z"
    }
    // ... 其他步骤
  ],
  "config": {
    "stepDelay": 2000,
    "retryCount": 2,
    "timeout": 10000
  }
}
```

#### 批量测试报告
```json
{
  "batchInfo": {
    "totalItems": 3,
    "completedCount": 2,
    "failedCount": 1,
    "successRate": "66.67%",
    "totalDuration": 45000
  },
  "items": [
    {
      "apvNo": "APV001",
      "status": "completed",
      "duration": 12000,
      "successSteps": 5,
      "totalSteps": 5,
      "results": [/* 详细步骤结果 */]
    }
    // ... 其他项目
  ]
}
```

### 导出和分享

- **导出JSON**：点击"导出结果"按钮下载JSON报告
- **复制结果**：点击"复制结果"按钮复制到剪贴板
- **控制台日志**：在浏览器控制台查看详细日志

## 🔄 持续集成集成

### Jenkins集成示例

```groovy
pipeline {
    agent any
    stages {
        stage('GPSR自动化测试') {
            steps {
                script {
                    def testUrl = "http://test-server/app/html/test/gpsr-cache-test.html?apvNo=${params.APV_NO}&autoTest=true"
                    
                    // 使用Selenium或Puppeteer执行自动化测试
                    sh """
                        node test-automation.js "${testUrl}"
                    """
                }
            }
        }
    }
}
```

### Node.js自动化脚本示例

```javascript
const puppeteer = require('puppeteer');

async function runGpsrAutoTest(testUrl) {
    const browser = await puppeteer.launch();
    const page = await browser.newPage();
    
    // 监听控制台日志
    page.on('console', msg => {
        if (msg.text().includes('[GPSR]')) {
            console.log('GPSR测试日志:', msg.text());
        }
    });
    
    await page.goto(testUrl);
    
    // 等待测试完成
    await page.waitForSelector('#autoTestSection', { visible: true });
    await page.waitForFunction(() => {
        return document.querySelector('#progressBar').style.width === '100%';
    }, { timeout: 60000 });
    
    // 获取测试结果
    const results = await page.evaluate(() => {
        return window.autoTestState.results;
    });
    
    await browser.close();
    return results;
}
```

## ⚠️ 注意事项

### 测试环境要求
- 确保后端服务正常运行
- 验证测试用的发货单号真实有效
- 检查用户权限和网络连接

### 性能考虑
- 批量测试时建议控制并发数量
- 适当调整步骤间延迟避免服务器压力
- 定期清理测试缓存

### 错误处理
- 网络超时：增加timeout参数
- 权限问题：确认用户登录状态
- 数据问题：验证发货单号和SKU

## 📞 技术支持

如需帮助，请提供：
- 完整的测试URL
- 浏览器控制台错误日志
- 测试报告JSON文件
- 环境和配置信息

通过这个自动化测试功能，您可以高效地验证GPSR缓存管理器在各种场景下的工作状态，确保系统的稳定性和可靠性。
