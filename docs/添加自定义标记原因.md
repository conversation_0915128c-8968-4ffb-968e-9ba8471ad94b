**接口名称:**

添加自定义标记原因

**接口描述:**

添加自定义标记原因

**请求路径:**

/exceptionMarkConfig/addCustomReason

**请求方式:**

POST

**请求参数:**

- Header

|参数名|参数值|必填|描述|
|:-----|:-----|:-----|:-----|
|Content-Type|application/x-www-form-urlencoded|Y||


- Param

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|reasonName|String|Y|原因名称|


- Body



**请求示例:**

```Form
reasonName=null
```

**返回参数:**

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|success|boolean|N|api返回状态|
|errorMsg|String|N|返回错误信息|
|result|Boolean|N|api返回值|
|exceptionCode|String|N|异常编码|


**返回示例:**

```JSON
{
    "success": false,
    "errorMsg": "",
    "result": false,
    "exceptionCode": ""
}
```

