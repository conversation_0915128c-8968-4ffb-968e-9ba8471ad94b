## 1. 需求背景

### 1.1 业务痛点
- FBA货件从签收到上架存在时间差，该时段库存处于**无人监管状态**
- 缺乏对签收后货件上架进度的有效监控机制
- 无法及时发现上架延迟问题，影响库存周转和销售计划
- 销售人员无法主动了解货件上架异常情况

### 1.2 解决目标
- 建立FBA货件签收到上架的全程监控体系
- 根据业务淡旺季特点制定差异化预警规则
- 实现异常自动预警和责任人通知机制
- 提供数据可视化展示和导出功能

## 2. 功能说明

### 2.1 核心功能架构

#### 2.1.1 淡旺季配置管理
- **月份维度配置**：支持1-12月份的淡旺季独立配置，采用4列网格布局
- **预警规则设定**：
  - 淡季：货件签收后可配置天数（默认7天）未上架触发预警
  - 旺季：货件签收后7-15天未上架触发预警，超过15天自动推送销售
- **季节判断逻辑**：根据货件**签收时间所在月份**对应的淡旺季配置来判断适用规则
- **可视化展示**：月份标签化显示，旺季黄色标签，淡季绿色标签
- **实时配置**：配置修改后立即生效，无需重启系统

#### 2.1.2 数据监控与展示
- **实时数据同步**：货件签收状态数据来源于OMS系统
- **多维度筛选**：支持货件编号、SKU、签收状态、上架状态、预警状态、销售人员、时间范围等条件查询
- **状态可视化**：差异数量异常标红显示，预警状态标签化展示
- **选项卡分类**：按照货件状态分类展示（全部/已发货/已签收/部分上架/全部上架/超时预警）
- **动态表格渲染**：JavaScript动态生成表格内容，支持实时筛选和状态切换
- **响应式设计**：支持不同屏幕尺寸，表格列宽自适应

#### 2.1.3 预警通知机制
- **自动预警触发**：系统定时检测符合预警条件的货件
- **钉钉通知推送**：自动通过钉钉推送预警消息给对应销售人员
- **通知日志记录**：**仅超期预警货件**记录通知日志，其他预警状态不记录通知日志
- **分级通知策略**：
  - 淡季预警：通知销售人员（不记录日志）
  - 旺季预警：通知销售人员（不记录日志）
  - 超期预警：强制推送给销售人员并记录详细日志

#### 2.1.4 用户交互体验
- **模态框操作**：配置修改、日志查看通过模态框实现
- **全选功能**：支持表格数据的全选/取消全选操作
- **状态筛选**：选项卡实时统计各状态数量，点击切换视图
- **批量操作**：选中记录后可进行批量日志查看和数据导出

### 2.2 辅助功能
- **批量操作**：支持批量查看通知日志（仅限超期预警货件）
- **数据导出**：支持Excel格式数据导出
- **分页展示**：支持大数据量的分页浏览，可调整每页显示数量
- **查询重置**：一键清空所有查询条件
- **实时反馈**：操作后提供即时的用户反馈提示

## 3. 页面字段说明

### 3.1 淡旺季配置区域

| 字段名称     | 字段说明                  | 数据类型 | 实现方式                | 备注                |
| -------- | --------------------- | ---- | ------------------- | ----------------- |
| 淡季预警天数   | 淡季货件签收后多少天未上架触发预警     | 数字   | 数字输入框，带范围限制         | 可配置1-30天，默认7天     |
| 旺季预警天数   | 旺季预警固定规则              | 文本   | 只读输入框               | 固定为"7-15天"        |
| 月份淡旺季配置  | 1-12月份对应的淡旺季设置        | 选择器  | 4列网格布局，每月独立选择器      | 每月可选择"淡季"或"旺季"    |
| 当前月份状态   | 显示当前月份及对应的淡旺季状态       | 文本   | 自动计算并显示             | 自动显示，如"12月(淡季)"   |
| 月份配置展示   | 可视化展示所有月份的淡旺季配置       | 标签组  | 彩色标签，旺季黄色，淡季绿色      | 实时更新，便于快速查看       |
| 修改配置按钮   | 打开配置修改模态框             | 按钮   | 蓝色主按钮               | 点击打开配置模态框         |

### 3.2 状态选项卡区域

| 选项卡名称 | 显示条件      | 统计逻辑    | 数据源筛选条件            | 视觉效果       | 备注                 |
| ----- | --------- | ------- | -------------------- | ---------- | ------------------ |
| 全部    | 所有货件      | 总记录数    | 无筛选                  | 默认激活状态     | 默认选中               |
| 已发货   | 已发货未签收    | 按状态筛选   | status='shipped'     | 蓝色标签       | status='shipped'   |
| 已签收   | 已签收未上架    | 按状态筛选   | status='received'    | 橙色标签       | status='received'  |
| 部分上架  | 部分商品已上架    | 按状态筛选   | status='partial'     | 黄色标签       | status='partial'   |
| 全部上架  | 全部商品已上架    | 按状态筛选   | status='complete'    | 绿色标签       | status='complete'  |
| 超时预警  | 超期预警货件     | 按预警类型筛选 | alertType='overtime' | 红色标签，重点突出  | alertType='overtime' |

### 3.3 查询筛选区域

| 字段名称 | 字段说明     | 数据类型  | 查询方式                      | UI组件     | 验证规则           |
| ---- | -------- | ----- | ------------------------- | -------- | -------------- |
| 货件编号 | FBA货件编号  | 文本    | 支持英文逗号分隔批量查询              | 文本输入框    | 支持批量输入，逗号分隔    |
| SKU  | 商品SKU代码  | 文本    | 支持英文逗号分隔批量查询              | 文本输入框    | 支持批量输入，逗号分隔    |
| 签收状态 | 货件签收状态   | 枚举    | 单选筛选                      | 下拉选择器    | 全部/已签收/未签收     |
| 上架状态 | 货件上架状态   | 枚举    | 单选筛选                      | 下拉选择器    | 全部/未上架/已上架/部分上架 |
| 预警状态 | 预警等级状态   | 枚举    | 单选筛选                      | 下拉选择器    | 全部/正常/淡季预警/旺季预警/超期预警 |
| 销售人员 | 负责销售人员   | 枚举    | 从预设列表中选择                  | 下拉选择器    | 预设销售人员列表       |
| 签收时间 | 货件签收时间范围 | 日期区间  | 开始时间 至 结束时间               | 日期选择器组合  | 结束时间不能早于开始时间   |
| 操作按钮 | 查询和重置操作  | 按钮组   | 执行查询或清空所有条件               | 蓝色主按钮+默认按钮 | 查询前验证输入格式      |

### 3.4 数据展示区域

| 字段名称        | 字段说明         | 数据来源  | 显示规则                 | 数据格式                   | 交互功能        |
| ----------- | ------------ | ----- | -------------------- | ---------------------- | ----------- |
| 编号          | 记录序号         | 系统生成  | 自增序号 + 复选框           | 001, 002, 003...       | 支持单选和全选     |
| **货件信息**    |              |       |                      |                        |             |
| └ 货件编号      | FBA货件编号      | OMS系统 | 粗体显示                 | FBA15DHPV9CK           | 无交互         |
| └ 发货单号      | 对应发货单号       | OMS系统 | 普通文本                 | YSTN2211051234567890   | 无交互         |
| └ 目的仓       | 目标仓库代码       | OMS系统 | 普通文本                 | ONT8, LAX5, PHX7       | 无交互         |
| └ 销售        | 负责销售人员       | OMS系统 | 蓝色链接样式，可点击           | 张三, 李四, 王五             | 点击可查看销售人员详情 |
| **SKU信息**   |              |       |                      |                        |             |
| └ SKU代码     | 商品SKU        | OMS系统 | 主要显示，较大字体            | 4NB401334              | 无交互         |
| └ FNSKU     | Amazon FNSKU | OMS系统 | 小字体，灰色               | X001HLKNVF             | 无交互         |
| └ SellerSku | 卖家SKU        | OMS系统 | 小字体，灰色               | 4NB401334_FRFBAMSJ     | 无交互         |
| 发货数量        | 实际发货商品数量     | OMS系统 | 居中显示，粗体              | 50, 100, 80            | 无交互         |
| 上架数量        | 已上架商品数量      | OMS系统 | 绿色显示已上架数量，灰色显示0      | 已上架绿色，未上架灰色            | 无交互         |
| **差异数量**    | 发货数量-上架数量    | 计算得出  | **异常数量红色背景显示**       |                        |             |
| └ 差异值       | 数量差异         | 计算    | 差异>0时红色背景，=0时绿色背景    | 红色:50, 5; 绿色:0         | 无交互         |
| └ 预警标签      | 预警状态标签       | 计算    | 彩色标签：正常绿色/预警橙色/超期红色  | 淡季预警/旺季预警/正常/超期        | 无交互         |
| 签收时间        | 货件签收时间       | OMS系统 | 分两行显示日期和时间           | 2023-11-15<br>14:30:25 | 无交互         |
| 预计上架时间      | 预计上架时间       | OMS系统 | 分两行显示日期和时间           | 2023-11-20<br>10:00:00 | 无交互         |
| 上架时间        | 货件上架时间       | OMS系统 | 未上架显示"未上架"，已上架显示具体时间 | 未上架显示灰色文字              | 无交互         |
| 等待天数        | 签收到当前的天数     | 计算得出  | 超预警天数红色显示，正常天数绿色显示   | 红色:25天, 30天; 绿色:3天     | 无交互         |
| 货件状态        | 货件处理状态       | 系统记录  | 彩色状态标签               | 已发货/已签收/部分上架/全部上架      | 无交互         |
| **通知日志**    | 钉钉通知记录       | 系统记录  | **仅超期预警显示日志按钮**      |                        |             |
| └ 日志按钮      | 查看通知历史       | 操作按钮  | 蓝色按钮显示通知次数，其他显示"无日志" | "查看日志(5)" 或 "无日志"      | 点击打开日志模态框   |

### 3.5 预警规则计算逻辑

| 预警类型 | 判断条件                      | 触发规则 | 通知策略   | 日志记录 | 视觉标识 | UI行为        |
| ---- | ------------------------- | ---- | ------ | ---- | ---- | ----------- |
| 正常   | 等待天数 ≤ 预警天数               | -    | 无通知    | 否    | 绿色标签 | 显示"无日志"     |
| 淡季预警 | 签收月份为淡季 且 等待天数 > 淡季预警天数   | 每日检测 | 钉钉通知销售 | 否    | 橙色标签 | 显示"无日志"     |
| 旺季预警 | 签收月份为旺季 且 7天 < 等待天数 ≤ 15天 | 每日检测 | 钉钉通知销售 | 否    | 黄色标签 | 显示"无日志"     |
| 超期预警 | 签收月份为旺季 且 等待天数 > 15天      | 立即触发 | 钉钉通知销售 | 是    | 红色标签 | 显示"查看日志(次数)" |

### 3.6 通知日志字段（仅超期预警）

| 字段名称 | 字段说明      | 数据类型 | 显示格式               | 样式规则           | 备注                   |
| ---- | --------- | ---- | ------------------ | -------------- | -------------------- |
| 通知时间 | 发送通知的时间   | 时间戳  | YYYY-MM-DD HH:mm:ss | 灰色小字体          | 精确到秒                 |
| 通知内容 | 预警通知的具体内容 | 文本   | 多行文本显示             | 黑色正文字体         | 包含货件信息和预警原因          |
| 通知状态 | 发送成功或失败状态 | 枚举   | 彩色标签               | 成功绿色，失败红色      | 成功/失败                |
| 接收人  | 通知的目标人员   | 文本   | 人员姓名               | 普通文本           | 销售人员姓名和联系方式          |
| 通知类型 | 预警类型标识    | 文本   | 标签显示               | 红色背景白字         | 固定为"超期预警"            |
| 失败原因 | 发送失败的具体原因 | 文本   | 红色提示文字             | 红色小字体          | 仅失败时显示，如"钉钉服务器异常"等   |

### 3.7 批量操作功能

| 功能名称   | 操作对象 | 前置条件     | 执行逻辑                   | UI交互         | 结果展示           |
| ------ | ---- | -------- | ---------------------- | ------------ | -------------- |
| 批量日志查看 | 选中记录 | 至少选中一条记录 | 只显示选中记录中超期预警货件的通知日志    | 橙色按钮，模态框展示   | 分货件分组展示日志      |
| 导出Excel | 选中记录 | 至少选中一条记录 | 导出选中记录的详细信息到Excel文件     | 绿色按钮，文件下载    | 弹框提示导出进度       |
| 全选操作   | 表格复选框 | 表格有数据    | 切换所有可见记录的选中状态          | 主复选框控制       | 实时更新选中数量显示     |

### 3.8 模态框交互设计

| 模态框名称    | 触发条件       | 尺寸规格     | 交互元素                 | 关闭方式           | 数据处理         |
| -------- | ---------- | -------- | -------------------- | -------------- | ------------ |
| 配置修改模态框  | 点击"修改配置"按钮 | 700px宽度  | 数字输入框+月份选择器网格+保存取消按钮  | 点击关闭/ESC键/背景点击 | 实时保存并更新页面配置  |
| 单个日志模态框  | 点击"查看日志"按钮 | 700px宽度  | 日志列表+时间线展示+关闭按钮      | 点击关闭/ESC键/背景点击 | 只读展示，无数据修改   |
| 批量日志模态框  | 点击"批量日志查看" | 700px宽度  | 分组日志列表+货件标题+关闭按钮     | 点击关闭/ESC键/背景点击 | 只读展示，自动筛选有日志货件 |

## 4. 技术实现要点

### 4.1 前端架构
- **HTML结构**：`index.html` - 语义化标签，清晰的页面结构
- **CSS样式**：
  - `css/FBA签收上架监控.css` - 基础样式，表格布局，响应式设计
  - `css/modal.css` - 模态框组件，选项卡样式，状态标签
- **JavaScript逻辑**：`js/app.js` - 数据渲染，事件处理，业务逻辑

### 4.2 数据管理
- **模拟数据驱动**：完整的货件数据模型和通知日志数据
- **状态计算逻辑**：动态计算预警状态、等待天数、差异数量
- **实时筛选更新**：选项卡切换和查询条件变更时重新渲染表格

### 4.3 交互体验
- **响应式布局**：适配不同屏幕尺寸，表格自适应滚动
- **状态反馈**：操作后的即时提示和视觉反馈
- **键盘支持**：模态框支持ESC键关闭，表单支持回车提交

### 4.4 数据同步（待实现）
- 与OMS系统建立实时数据同步机制
- 定时任务检测货件状态变化
- 缓存机制优化查询性能

### 4.5 预警计算（待实现）
- 根据签收时间月份匹配淡旺季配置
- 计算等待天数并判断预警等级
- 支持配置规则的动态调整

### 4.6 通知推送（待实现）
- 集成钉钉机器人API
- 实现消息模板化管理
- **仅对超期预警建立通知日志追踪机制**

## 5. 业务规则补充说明

### 5.1 通知日志策略调整
根据实际业务需求，**通知日志功能仅针对超期预警货件**：
- **淡季预警**：只发送钉钉通知，不记录日志，界面显示"无日志"
- **旺季预警**：只发送钉钉通知，不记录日志，界面显示"无日志"  
- **超期预警**：发送钉钉通知并记录详细日志，界面显示"查看日志(次数)"

### 5.2 日志记录范围
- 超期预警货件的每次通知都会记录详细日志
- 日志包含通知时间、内容、状态、接收人、失败原因等信息
- 支持单个货件日志查看和批量日志查看功能
- 日志按时间倒序排列，最新的通知记录在最上方

### 5.3 用户界面优化
- 非超期预警货件在通知日志列显示"无日志"文字
- 批量日志查看时自动过滤出有日志记录的货件，无日志货件不显示
- 选项卡统计数据实时更新，便于快速定位问题货件
- 差异数量、等待天数等关键指标采用颜色编码，异常情况红色突出显示

### 5.4 数据展示规则
- **状态优先级**：超期预警 > 旺季预警 > 淡季预警 > 正常
- **颜色编码**：红色(异常/超期) > 橙色(预警) > 黄色(关注) > 绿色(正常)
- **数据更新**：配置修改后立即重新计算所有货件的预警状态
- **筛选逻辑**：选项卡筛选与查询条件可以同时生效，实现多维度数据过滤

## 6. 示例数据说明

### 6.1 测试数据集
当前系统包含5条完整的测试数据，覆盖以下场景：
1. **淡季预警货件**：FBA15DHPV9CK - 已签收8天未上架，淡季预警状态
2. **旺季预警货件**：FBA15CGTX7NK - 已签收12天部分上架，旺季预警状态  
3. **正常完成货件**：FBA15DHPV9DL - 3天内完成上架，正常状态
4. **超期预警货件1**：FBA15OVERTM1 - 已签收25天未上架，有5条通知日志
5. **超期预警货件2**：FBA15OVERTM2 - 已签收30天部分上架，有8条通知日志

### 6.2 日志数据分布
- **FBA15OVERTM1**：5条通知日志，包含1条发送失败记录
- **FBA15OVERTM2**：8条通知日志，包含1条发送失败记录
- **其他货件**：无通知日志记录，界面显示"无日志"

### 6.3 选项卡统计
- 全部：5条记录
- 已发货：0条记录  
- 已签收：2条记录（含1条超期）
- 部分上架：2条记录（含1条超期）
- 全部上架：1条记录
- 超时预警：2条记录

这份PRD明确了系统的核心价值是**监控签收到上架的无人监管时段**，通过**差异化的淡旺季预警规则**和**针对性的通知日志机制**，帮助业务团队及时发现和处理上架延迟问题。特别强调了**只有超期预警才记录通知日志**的业务规则，并详细描述了前端实现的交互细节和用户体验优化。