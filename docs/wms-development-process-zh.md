# WMS 开发流程规范

## 📖 文档信息
- **文档类型**: 开发流程规范
- **适用对象**: 所有开发人员、项目经理
- **版本**: v1.0
- **作者**: Amoi

---

## 🚀 快速开始指南

### 新功能开发流程
1. **需求分析**: 理解业务需求和技术要求
2. **数据库设计**: 设计表结构和索引
3. **接口设计**: 定义Service接口和方法签名
4. **实现开发**: 按层次实现DAO→Service→Controller
5. **前端开发**: 创建FreeMarker模板和JavaScript交互
6. **测试验证**: 单元测试和集成测试
7. **文档更新**: 更新相关技术文档

### 开发环境配置
1. **JDK**: 使用JDK 8+
2. **IDE**: 推荐使用IntelliJ IDEA或Eclipse
3. **数据库**: MySQL 5.1.38+
4. **构建工具**: Maven 3.6+
5. **版本控制**: Git

---

## 📋 执行检查清单

### 开发前检查
- [ ] 阅读本WMS开发规则文档
- [ ] 确认功能需求和业务逻辑
- [ ] 检查相关模块的现有代码结构
- [ ] 确认数据库表结构和字段定义
- [ ] 了解相关外部系统接口
- [ ] 检查缓存和消息队列配置

### 开发中检查
- [ ] 遵循多层架构设计原则
- [ ] 控制服务类大小和复杂度
- [ ] 使用自定义SQL映射框架
- [ ] 实现适当的缓存策略
- [ ] 添加必要的分布式锁
- [ ] 编写规范的异常处理代码
- [ ] 添加详细的日志记录
- [ ] 编写单元测试用例

### 开发后检查
- [ ] 代码质量检查和优化
- [ ] 数据库操作性能测试
- [ ] 缓存命中率验证
- [ ] 分布式锁功能测试
- [ ] 消息队列处理验证
- [ ] 前端页面兼容性测试
- [ ] 安全性检查
- [ ] 文档更新和维护

---

## 🔧 技术框架使用规范

### 缓存策略规范
- **两级缓存使用**:
  - **一级缓存（EhCache）**: 缓存频繁访问的配置数据和字典数据
  - **二级缓存（Redis）**: 缓存业务数据和跨服务共享数据
- **缓存Key规范**: `wms:{module}:{business}:{id}`
- **缓存过期**: 根据数据特性设置合理的过期时间
- **缓存更新**: 数据变更时及时清理相关缓存

### 分布式锁使用规范
- **工具**: 统一使用Redisson实现分布式锁
- **锁粒度**: 尽量使用细粒度锁，避免大范围锁定
- **锁超时**: 设置合理的锁超时时间，防止死锁
- **使用场景**: 库存扣减、订单分配、任务调度等并发场景
- **错误处理**: 获取锁失败时的降级处理机制

### 消息队列使用规范
- **RabbitMQ**: 用于业务流程的异步处理
- **Kafka**: 用于日志收集和事件流处理
- **ActiveMQ**: 用于传统JMS消息通信
- **消息格式**: 统一使用JSON格式
- **错误重试**: 实现消息重试机制和死信队列

### FreeMarker模板规范
- **文件位置**: 模板文件存放在`src/main/webapp/html/`目录
- **命名规范**: 使用小写字母和下划线，如`warehouse_list.html`
- **数据传递**: Controller通过ModelAndView传递数据到模板
- **代码分离**: 模板中不允许包含复杂的业务逻辑
- **国际化**: 支持中文显示，编码统一使用UTF-8

### 任务调度规范
- **框架**: 统一使用XXL-Job 2.2.0进行任务调度
- **任务分类**:
  - 数据同步任务
  - 报表生成任务
  - 清理过期数据任务
  - 库存盘点任务
- **监控告警**: 重要任务需要配置监控和告警
- **日志记录**: 详细记录任务执行情况

---

## 🛡️ 安全性和性能规范

### 安全性要求
- **输入验证**: 所有用户输入必须进行验证和过滤
- **SQL注入防护**: 使用参数化查询，避免拼接SQL
- **XSS防护**: 前端模板输出数据时进行转义
- **敏感信息**: 密码、密钥等敏感信息加密存储
- **访问控制**: 实现适当的权限控制机制

### 性能优化规范
- **数据库优化**:
  - 避免N+1查询问题
  - 使用合适的索引
  - 分页处理大数据量查询
- **缓存优化**:
  - 热点数据缓存
  - 缓存预热机制
  - 缓存穿透防护
- **代码优化**:
  - 避免在循环中进行数据库操作
  - 合理使用线程池
  - 及时释放资源

### 数据库操作规范
- **连接池**: 使用Druid连接池，配置监控和预警
- **事务管理**: 使用`@Transactional`注解，合理控制事务边界
- **批量操作**: 大数据量操作使用批处理，避免单条操作
- **索引使用**: 合理创建和使用索引，定期优化慢查询
- **数据备份**: 重要操作前进行数据备份

---

## 🌐 外部系统集成规范

### 集成方式
- **物流系统**: 极兔(jitu)、跨越(kyexpress)
- **电商平台**: Amazon、AliExpress、Shopee、Temu
- **其他系统**: ERP、PMS、TMS、FMS

### API设计
- **foreign包下的Controller**: 使用`@RestController`注解
- **错误处理**: 实现统一的异常处理和重试机制
- **数据同步**: 使用消息队列保证数据一致性

---

## 📄 文档处理规范

### Excel处理
- **优先使用**: EasyExcel 3.0.5
- **大文件处理**: 使用流式读写
- **文件上传**: 限制文件类型和大小，病毒扫描
- **文件存储**: 分类存储，定期清理临时文件

### PDF生成
- **使用工具**: iText PDF 5.5.13
- **生成方式**: 模板化生成报表

---

## 🌍 环境配置管理

### 配置分离
- **本地环境**: `application-local.yml`
- **测试环境**: `application-test.yml`
- **生产环境**: `application-prod.yml`

### 敏感配置
- **密码、密钥**: 使用环境变量或配置中心
- **配置验证**: 启动时验证必要配置项
- **配置监控**: 监控配置变更和生效情况

---

## 📊 监控和运维规范

### 健康检查
- **使用工具**: Spring Boot Actuator提供健康检查接口
- **指标监控**: 集成Prometheus收集关键指标

### 日志监控
- **重要错误**: 及时告警
- **性能监控**: 监控接口响应时间和吞吐量

---

## 🔄 版本控制规范

### 提交规范
- **提交信息**: 使用规范的提交信息格式
- **分支策略**: 功能开发使用feature分支
- **代码审查**: 重要功能需要进行代码审查
- **版本标签**: 重要版本打标签记录

---

## ⚠️ 常见问题排查

### 性能问题
1. 检查SQL执行计划，优化慢查询
2. 验证缓存配置和Key规范
3. 检查锁粒度和超时设置

### 集成问题
1. 检查消息格式和重试机制
2. 验证外部系统API调用
3. 检查数据同步状态

### 前端问题
1. 检查浏览器兼容性和组件版本
2. 验证模板数据传递
3. 检查JavaScript错误

---

## 🚨 错误管理

### 错误记录
- **要求**: 把每次的运行报错记录和最终解决方案记录到`error_analysis.md`文件
- **执行**: 如果文件不存在就创建
- **维护**: 对`error_analysis.md`文件做错误总结和错误规避

### 错误预防
- **要求**: 每次修改报错问题时需要先查看`error_analysis.md`
- **执行**: 规避这些问题减少试错成本

---

**返回主文档**: [wms-dev-rules.mdc](../wms-dev-rules.mdc) 