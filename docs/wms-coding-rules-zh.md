# WMS仓库管理系统 - 核心编码规范

## 📖 文档概述

本文档定义了WMS项目的核心编码规范，包括架构分层、代码质量控制、异常处理、日志规范等详细要求。

**相关文档**:
- [项目架构规范](wms-architecture-rules-zh.md) - 技术栈和架构设计
- [DAO层开发规范](dao-layer-coding-rules.md) - 数据访问层实现
- [AI快速参考](../.cursor/rules/wms-dev-rules-simplified.mdc) - 日常开发模板

---

## 🏗️ 架构分层规范

### 规则1：架构层次严格分离
**强制要求**: 必须严格遵循多层架构设计，保持代码结构清晰

#### 标准分层结构
```
Controller (action) → Service → DAO → Database
```

#### 分层职责定义
- **Controller层**: 处理HTTP请求，数据验证，调用Service
- **Service层**: 业务逻辑处理，事务管理，调用DAO
- **DAO层**: 数据访问，SQL执行，结果映射

#### 🔴 严重禁止事项
1. ❌ **Controller直接调用DAO** - 违反架构分层原则
2. ❌ **跨层调用** - 禁止跳跃式调用，如Controller → DAO，Service → Web层
3. ❌ **DAO层包含业务逻辑** - DAO只能进行数据访问

#### ✅ 正确示例
```java
// Controller层 - 正确
@Controller
public class OrderController {
    @Resource
    private OrderService orderService;
    
    @PostMapping("save")
    public ApiResult<String> save(@RequestBody Order order) {
        return ApiResult.newSuccess(orderService.save(order));
    }
}

// Service层 - 正确
@Service  // 禁止使用@Transactional注解，使用AOP自动管理
public class OrderServiceImpl implements OrderService {
    @Resource
    private OrderDao orderDao;
    
    public boolean save(Order order) {
        // 业务逻辑验证
        validateOrder(order);
        // 调用DAO
        orderDao.insert(order);
        return true;
    }
}
```

---

## 🎯 代码质量控制

### 规则2：代码大小控制标准
**统一标准**: 基于WMS业务复杂度和现代软件工程最佳实践

#### 📏 统一限制规范
```yaml
# WMS项目代码限制标准 (统一版本)
代码限制:
  单个方法:
    最大行数: 100行
    最大复杂度: 10
    
  类级别限制:
    Service层: 2000行      # 适应复杂业务逻辑
    Controller层: 1000行   # 控制接口数量
    DAO层: 1500行         # 平衡数据访问复杂度
    普通类: 1000行        # 通用类标准
    
  现代Java特性:
    Stream链: 最多5个操作
    Lambda表达式: 最多3行
```

#### 🎯 拆分策略指南

##### Service类拆分策略
```java
// ✅ 正确：Service类控制在2000行内，合理功能划分
@Service
public class OrderServiceImpl implements OrderService {
    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderValidationService validationService;  // 验证逻辑分离
    @Resource
    private OrderCalculationService calculationService; // 计算逻辑分离
    @Resource
    private OrderNotificationService notificationService; // 通知逻辑分离
    
    public boolean save(Order order) {
        validationService.validate(order);
        calculationService.calculateTotal(order);
        orderDao.insert(order);
        notificationService.sendConfirmation(order);
        return true;
    }
}

// 辅助服务类 - 控制在合理范围内
@Service
public class OrderValidationService {
    public void validate(Order order) {
        // 验证逻辑 - 专注单一职责
    }
}
```

##### Controller类拆分策略
```java
// ✅ 正确：Controller类控制在1000行内
@Controller
@RequestMapping("order")
public class OrderController {
    @Resource
    private OrderService orderService;
    
    // 基础CRUD操作
    @PostMapping("save")
    public ApiResult<String> save(@RequestBody Order order) {
        return ApiResult.newSuccess(orderService.save(order));
    }
}

// 复杂业务独立Controller
@Controller
@RequestMapping("order/workflow")
public class OrderWorkflowController {
    // 工作流相关操作
}
```

### 规则3：方法复杂度控制
**标准要求**: 单个方法圈复杂度不超过10，代码行数不超过100行

#### 降低复杂度方法
```java
// ✅ 正确：方法拆分，控制在100行内
public void processOrder(Order order) {
    validateOrderBasicInfo(order);    // 基础信息验证
    validateOrderItems(order);        // 商品信息验证
    calculateOrderAmounts(order);     // 金额计算
    updateOrderStatus(order);         // 状态更新
    sendOrderNotification(order);     // 发送通知
}

// 每个私有方法都控制在合理范围内
private void validateOrderBasicInfo(Order order) {
    // 验证逻辑 - 不超过100行
    if (Objects.isNull(order)) {
        throw new BusinessException("订单信息不能为空");
    }
    // ... 其他验证逻辑
}
```

#### 提前返回模式
```java
// ✅ 推荐：使用提前返回减少嵌套
public String processPayment(Payment payment) {
    if (Objects.isNull(payment)) {
        return "参数错误";
    }
    
    if (payment.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
        return "金额必须大于0";
    }
    
    if (!isValidAccount(payment.getAccount())) {
        return "账户无效";
    }
    
    // 正常处理逻辑
    return processValidPayment(payment);
}
```

### 规则4：命名规范
**标准要求**: 遵循Java命名约定

#### 命名规则
- **类名**: PascalCase (大驼峰) - `OrderService`, `UserController`
- **方法名**: camelCase (小驼峰) - `getUserById`, `saveOrder`
- **常量**: UPPER_SNAKE_CASE - `MAX_RETRY_COUNT`, `DEFAULT_TIMEOUT`
- **包名**: 全小写，用点分隔 - `com.estone.wms.service`

### 规则5：代码注释和文档规范
**强制要求**: 所有类和公共方法必须有完整的注释文档

#### 5.1 类级别注释规范

##### ✅ 强制要求：类注释格式
```java
/**
 * 类功能描述
 * 
 * <AUTHOR>
 */
@Service
public class OrderServiceImpl implements OrderService {
    // 类实现
}
```

##### 📏 类注释要求
- **@author**: 必须使用 `<AUTHOR>
- **描述**: 简洁描述类的主要功能和职责
- **位置**: 紧接在类声明之前

#### 5.2 方法级别注释规范

##### ✅ 公共方法注释格式
```java
/**
 * 方法功能描述
 * 
 * @param order 订单信息
 * @param userId 用户ID
 * @return 处理结果
 * @throws BusinessException 业务异常
 */
public ApiResult<String> processOrder(Order order, String userId) throws BusinessException {
    // 方法实现
}
```

##### 📏 方法注释要求
- **强制**：所有public方法必须有JavaDoc注释
- **参数**：@param 描述每个参数的含义和要求
- **返回值**：@return 描述返回值的含义
- **异常**：@throws 描述可能抛出的异常

#### 5.3 特殊注释规范

##### ✅ DTO类注释示例
```java
/**
 * 订单数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class OrderDTO {
    /** 订单ID */
    private String orderId;
    
    /** 订单状态 */
    private Integer status;
}
```

##### ✅ 接口注释示例
```java
/**
 * 订单服务接口
 * 
 * <AUTHOR>
 */
public interface OrderService {
    
    /**
     * 保存订单信息
     * 
     * @param order 订单对象
     * @return 保存成功返回true，否则返回false
     */
    boolean save(Order order);
}
```

#### 🔴 注释规范禁止事项
1. ❌ **遗漏类注释** - 所有新建类必须有<AUTHOR>
2. ❌ **公共方法无注释** - 所有public方法必须有完整JavaDoc
3. ❌ **注释与代码不符** - 修改代码时必须同步更新注释

### 规则6：现代Java特性使用规范
**适用版本**: 基于项目Java 8+特性，Spring Boot 2.0.8环境

#### 6.1 Stream API使用规范

##### ✅ 推荐使用场景
```java
// 集合过滤和转换
List<String> validSkus = orders.stream()
    .filter(Objects::nonNull)
    .map(Order::getSku)
    .filter(StringUtils::isNotEmpty)
    .collect(Collectors.toList());

// 分组操作
Map<Integer, List<Order>> ordersByStatus = orders.stream()
    .collect(Collectors.groupingBy(Order::getStatus));

// 聚合计算
BigDecimal totalAmount = orders.stream()
    .map(Order::getAmount)
    .filter(Objects::nonNull)
    .reduce(BigDecimal.ZERO, BigDecimal::add);

// 查找操作
Optional<Order> firstActiveOrder = orders.stream()
    .filter(order -> order.getStatus() == 1)
    .findFirst();
```

##### 🔴 禁止的Stream用法
```java
// ❌ 错误：过度复杂的Stream链（超过5个操作）
List<String> result = orders.stream()
    .filter(Objects::nonNull)
    .filter(order -> order.getStatus() == 1)
    .map(Order::getItems)
    .flatMap(Collection::stream)
    .filter(item -> item.getQuantity() > 0)
    .map(Item::getSku)
    .distinct()
    .sorted()
    .collect(Collectors.toList()); // 应拆分为多个步骤

// ❌ 错误：Stream中进行副作用操作
orders.stream()
    .filter(Objects::nonNull)
    .forEach(order -> {
        order.setUpdateTime(new Date()); // 避免在Stream中修改对象状态
        orderDao.update(order); // 避免在Stream中进行数据库操作
    });
```

##### 📏 Stream使用准则
- **长度限制**: 单个Stream链不超过5个操作
- **可读性**: 复杂操作应拆分为多个步骤或提取为私有方法
- **性能考虑**: 大数据量操作考虑使用`parallelStream()`
- **副作用**: 避免在Stream操作中进行状态修改或IO操作

#### 6.2 Lambda表达式规范

##### ✅ 推荐使用方式
```java
// 简洁的函数式接口实现
orders.forEach(this::processOrder);

// 条件判断（不超过3行）
orders.removeIf(order -> 
    order.getStatus() == 0 || 
    order.getAmount().compareTo(BigDecimal.ZERO) <= 0);

// 排序
orders.sort(Comparator.comparing(Order::getCreateTime));

// 复合排序
orders.sort(Comparator.comparing(Order::getStatus)
    .thenComparing(Order::getCreateTime));
```

##### 🔴 禁止的Lambda用法
```java
// ❌ 错误：Lambda表达式过于复杂（超过3行）
orders.forEach(order -> {
    order.setUpdateTime(new Date());
    order.setStatus(calculateNewStatus(order));
    orderDao.update(order);
    sendNotification(order);
    logOrderUpdate(order);  // 超过3行，应提取为方法
});

// ✅ 正确：提取为私有方法
orders.forEach(this::processOrderUpdate);

private void processOrderUpdate(Order order) {
    order.setUpdateTime(new Date());
    order.setStatus(calculateNewStatus(order));
    orderDao.update(order);
    sendNotification(order);
    logOrderUpdate(order);
}
```

#### 6.3 Optional使用规范

##### ✅ 推荐使用场景
```java
// 替代空值检查链
String userName = Optional.ofNullable(order)
    .map(Order::getUser)
    .map(User::getName)
    .orElse("Unknown");

// 条件执行
Optional.ofNullable(order)
    .filter(o -> o.getStatus() == 1)
    .ifPresent(this::processActiveOrder);

// 异常抛出
Order order = Optional.ofNullable(orderDao.queryById(id))
    .orElseThrow(() -> new BusinessException("订单不存在"));
```

##### 🔴 禁止的Optional用法
```java
// ❌ 错误：过度包装简单值
Optional<String> name = Optional.of("test"); // 直接使用String即可

// ❌ 错误：集合和数组的Optional包装
Optional<List<Order>> orders = Optional.of(new ArrayList<>()); // 使用空集合

// ❌ 错误：在Service方法参数中使用Optional
public void processOrder(Optional<Order> order) { // 应直接使用Order类型
```

#### 6.4 方法引用规范

##### ✅ 推荐使用方式
```java
// 静态方法引用
orders.stream().map(Objects::toString);

// 实例方法引用
orders.forEach(this::processOrder);

// 类型方法引用
names.stream().map(String::toUpperCase);

// 构造器引用
Stream.of("a", "b", "c").map(StringBuilder::new);
```

#### 6.5 函数式接口使用

##### ✅ 标准函数式接口
```java
// Predicate - 条件判断
Predicate<Order> isActive = order -> order.getStatus() == 1;

// Function - 数据转换
Function<Order, String> orderToSku = Order::getSku;

// Consumer - 消费操作
Consumer<Order> orderProcessor = this::processOrder;

// Supplier - 数据提供
Supplier<Date> currentTime = Date::new;
```

#### 6.6 代码简洁性最佳实践

##### ✅ 简洁代码示例
```java
// 传统写法 → 现代写法
// 传统空值检查
if (order != null && order.getUser() != null && order.getUser().getName() != null) {
    return order.getUser().getName();
}
return "Unknown";

// 现代写法
return Optional.ofNullable(order)
    .map(Order::getUser)
    .map(User::getName)
    .orElse("Unknown");

// 传统集合操作
List<String> activeOrderSkus = new ArrayList<>();
for (Order order : orders) {
    if (order.getStatus() == 1 && StringUtils.isNotEmpty(order.getSku())) {
        activeOrderSkus.add(order.getSku());
    }
}

// 现代写法
List<String> activeOrderSkus = orders.stream()
    .filter(order -> order.getStatus() == 1)
    .map(Order::getSku)
    .filter(StringUtils::isNotEmpty)
    .collect(Collectors.toList());
```

##### 📏 现代特性使用准则
1. **可读性优先**: 现代特性应提高而非降低代码可读性
2. **团队一致性**: 团队成员需要熟悉相关特性
3. **性能考虑**: 复杂操作时评估传统方法和函数式方法的性能
4. **错误处理**: 复杂的异常处理仍使用传统try-catch模式
5. **文档注释**: 复杂的函数式操作需要添加详细注释

---

## 🚫 核心禁止事项清单

### 🔴 架构违规（严重错误）
1. ❌ **Controller直接调用DAO** - 必须通过Service层
2. ❌ **跨层调用** - 禁止跳跃式调用
3. ❌ **DAO层包含业务逻辑** - DAO只能进行数据访问

### 🔴 代码质量违规（严重错误）
4. ❌ **单个方法超过100行** - 必须拆分为更小的方法
5. ❌ **Service类超过2000行** - 必须拆分为更小的服务类
6. ❌ **Controller类超过1000行** - 必须拆分功能模块
7. ❌ **DAO类超过1500行** - 必须按业务域拆分
8. ❌ **方法复杂度超过10** - 必须简化逻辑或拆分方法

### 🔴 技术规范违规（严重错误）
9. ❌ **使用@Transactional注解** - 必须依赖AOP自动事务管理
10. ❌ **硬编码字段名** - 必须使用 `EntityDBField` 常量
11. ❌ **手动声明Logger** - 必须使用 `@Slf4j` 注解
12. ❌ **使用@Autowired注解** - 必须使用 `@Resource` 注解
13. ❌ **字符串拼接SQL** - 必须使用参数化查询，防止SQL注入
14. ❌ **忽略空值检查** - 必须使用 `Objects.isNull()` 和 `CollectionUtils.isEmpty()`

### 🔴 现代Java特性违规（严重错误）
15. ❌ **过度复杂的Stream链** - 单个Stream操作不超过5个步骤
16. ❌ **Stream中进行副作用操作** - 避免在Stream中修改状态或执行IO操作
17. ❌ **Lambda表达式超过3行** - 复杂逻辑应提取为私有方法

### 🟠 代码规范禁止事项（警告级别）
18. ⚠️ **缺少类注释** - 所有新建类必须有<AUTHOR>
19. ⚠️ **缺少方法注释** - 所有public方法必须有完整的JavaDoc注释
20. ⚠️ **使用错误的作者标识** - 必须统一使用<AUTHOR>
21. ⚠️ **魔法数字** - 避免在代码中使用未定义的数字常量
22. ⚠️ **过长的方法** - 单个方法不应超过100行，复杂度不超过10
23. ⚠️ **不规范的命名** - 类名使用PascalCase，方法名使用camelCase

---

## ✅ 代码质量检查清单

### 基础架构检查项
- [ ] Controller不直接调用DAO
- [ ] Service层包含业务逻辑
- [ ] DAO层只进行数据访问
- [ ] 分层职责清晰分离

### 代码大小检查项
- [ ] 单个方法不超过100行
- [ ] 方法复杂度不超过10
- [ ] Service类不超过2000行
- [ ] Controller类不超过1000行
- [ ] DAO类不超过1500行
- [ ] 普通类不超过1000行

### 现代Java特性检查项
- [ ] Stream链不超过5个操作
- [ ] Lambda表达式不超过3行
- [ ] Optional正确使用，避免过度包装
- [ ] 避免在Stream中进行副作用操作
- [ ] 优先使用方法引用而非Lambda表达式
- [ ] 现代特性提升而非降低代码可读性

### 技术规范检查项
- [ ] @Repository注解
- [ ] setQueryCondition()方法
- [ ] DBField常量使用
- [ ] DataType枚举使用
- [ ] 空值检查完整
- [ ] @Slf4j注解使用
- [ ] 异常处理规范

### 代码注释检查项
- [ ] 类注释包含<AUTHOR>
- [ ] 类功能描述清晰
- [ ] 公共方法有JavaDoc注释
- [ ] 方法参数描述完整(@param)
- [ ] 返回值描述明确(@return)
- [ ] 异常说明详细(@throws)
- [ ] 注释与代码同步更新

### 事务管理检查项
- [ ] 禁止使用@Transactional注解
- [ ] 方法命名符合事务规则
- [ ] 查询方法使用get/search/query/find/list/count前缀
- [ ] 写操作方法使用save/add/insert/create/update/delete/batch/do前缀
- [ ] 依赖AOP自动事务管理

### 异常处理检查项
- [ ] 使用BusinessException
- [ ] 实现全局异常处理
- [ ] 异常信息有意义
- [ ] 不暴露系统异常

---

**项目**: WMS仓库管理系统 | **版本**: v1.0 | **作者**: Amoi 