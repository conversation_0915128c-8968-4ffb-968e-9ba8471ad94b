# GPSR真实数据测试指南

## 📋 概述

本指南说明如何使用真实的后端数据测试GPSR缓存管理器，确保在生产环境中正常工作。

## 🚀 测试准备

### 1. 环境要求

- **后端服务**：确保GPSR相关的后端接口正常运行
- **数据库**：包含真实的GPSR订单数据
- **网络连接**：能够访问后端API接口
- **浏览器**：支持现代JavaScript特性

### 2. 获取测试数据

#### 查找GPSR订单
```sql
-- 查询GPSR订单的SQL示例
SELECT apv_no, platform, buyer_checkout, buyer_country 
FROM wh_apv 
WHERE buyer_checkout LIKE '%GPSR%' 
AND created_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
LIMIT 10;
```

#### 验证发货单状态
确保测试用的发货单：
- 状态正常（未取消、未删除）
- 包含GPSR标记
- 有对应的商品信息

### 3. 配置测试环境

#### 设置正确的CONTEXT_PATH
```javascript
// 在测试页面中确保CONTEXT_PATH正确
window.CONTEXT_PATH = '/your-app-context/'; // 根据实际部署路径修改
```

#### 检查接口权限
确保当前用户有权限访问：
- `/apv/packs/printGpsrTag` 接口
- `/fba/packs/printGpsrTag` 接口

## 🧪 测试步骤

### 第一步：网络连接测试

1. **打开测试页面**
   ```
   http://your-domain/your-app/html/test/gpsr-cache-test.html
   ```

2. **点击"测试网络连接"按钮**
   - 检查是否能访问后端接口
   - 查看日志输出的连接状态

3. **预期结果**
   ```
   接口 1 可访问：/apv/packs/printGpsrTag?apvNo=TEST
   接口 2 可访问：/fba/packs/printGpsrTag?apvNo=TEST
   ```

### 第二步：后端接口测试

1. **输入真实发货单号**
   ```
   发货单号: APV202412300001 (替换为真实的GPSR发货单号)
   接口类型: 本地接口 (或FBA接口，根据发货单类型选择)
   ```

2. **点击"测试后端接口"按钮**
   - 直接调用后端API
   - 分析返回的GPSR数据

3. **预期结果**
   ```
   接口调用成功，响应长度：1234 字符
   找到 2 个GPSR标签
   标签 1：SKU=ABC123, 数量=1, PDF=/static/pdf/gpsr/xxx.pdf
   标签 2：SKU=DEF456, 数量=1, PDF=/static/pdf/gpsr/yyy.pdf
   ```

### 第三步：预加载功能测试

1. **点击"预加载GPSR"按钮**
   - 测试缓存管理器的预加载功能
   - 观察性能指标

2. **预期结果**
   ```
   开始预加载：APV202412300001 (接口类型：local)
   预加载成功：APV202412300001, 平台：SMT, PDF数量：2
   PDF URLs：/static/pdf/gpsr/xxx.pdf, /static/pdf/gpsr/yyy.pdf
   ```

### 第四步：数据验证测试

1. **点击"验证GPSR数据"按钮**
   - 验证缓存数据的完整性和正确性

2. **预期结果**
   ```
   验证GPSR数据：APV202412300001
   ✅ 找到 2 个GPSR标签
   ✅ 标签 1 验证通过：SKU=ABC123, 数量=1
   ✅ 标签 2 验证通过：SKU=DEF456, 数量=1
   验证完成：2/2 个标签有效
   ```

### 第五步：打印功能测试

1. **点击"打印GPSR"按钮**
   - 测试从缓存打印功能
   - 验证打印控件调用

2. **预期结果**
   ```
   开始打印：APV202412300001
   使用缓存打印：APV202412300001
   调用doPrintGpsrTag函数，份数：1, PDF：/static/pdf/gpsr/xxx.pdf
   调用doPrintGpsrTag函数，份数：1, PDF：/static/pdf/gpsr/yyy.pdf
   打印成功（缓存方式）：APV202412300001, 打印数量：2
   ```

### 第六步：批量预加载测试

1. **输入多个发货单号**
   ```
   发货单列表: APV001,APV002,APV003 (替换为真实的GPSR发货单号)
   ```

2. **点击"批量预加载"按钮**
   - 测试批量处理功能
   - 观察并发控制效果

3. **预期结果**
   ```
   开始批量预加载：APV001, APV002, APV003 (接口类型：local)
   批量预加载进度：3/3
   批量预加载完成：成功 3, 失败 0
   成功的发货单：
     - APV001: SMT
     - APV002: HTML
     - APV003: SMT
   ```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 接口不可访问
```
错误：接口 1 不可访问：/apv/packs/printGpsrTag?apvNo=TEST (状态码：404)
```

**解决方案：**
- 检查CONTEXT_PATH配置是否正确
- 确认后端服务是否正常运行
- 验证接口路径是否正确

#### 2. 权限不足
```
错误：HTTP状态码：403
```

**解决方案：**
- 确认当前用户是否已登录
- 检查用户是否有相应权限
- 联系管理员分配权限

#### 3. 发货单不存在
```
错误信息：发货单不存在或不是GPSR订单
```

**解决方案：**
- 确认发货单号是否正确
- 检查发货单是否为GPSR订单
- 使用数据库查询验证发货单状态

#### 4. 数据格式错误
```
❌ 验证失败：未找到GPSR标签
```

**解决方案：**
- 检查后端返回的HTML格式
- 确认CSS选择器是否正确
- 验证数据库中的GPSR标记

#### 5. PDF文件不存在
```
PDF预加载失败：/static/pdf/gpsr/xxx.pdf
```

**解决方案：**
- 检查PDF文件是否存在于服务器
- 确认文件路径是否正确
- 验证文件访问权限

## 📊 性能基准

### 预期性能指标

| 操作 | 预期时间 | 说明 |
|------|----------|------|
| **单个预加载** | 2-5秒 | 首次加载，包含PDF下载 |
| **缓存命中打印** | <100ms | 使用缓存数据 |
| **批量预加载(3个)** | 5-10秒 | 并发处理 |
| **数据验证** | <50ms | 纯前端处理 |

### 性能监控

```javascript
// 查看性能统计
var perfStats = GpsrCacheManager.getPerformanceStats();
console.log('性能统计：', perfStats);

// 查看缓存统计
var cacheStats = GpsrCacheManager.getCacheStats();
console.log('缓存统计：', cacheStats);
```

## 🎯 测试清单

### 基础功能测试
- [ ] 网络连接正常
- [ ] 后端接口可访问
- [ ] 单个预加载成功
- [ ] 数据验证通过
- [ ] 打印功能正常

### 高级功能测试
- [ ] 批量预加载成功
- [ ] 缓存管理正常
- [ ] 性能指标合理
- [ ] 错误处理正确
- [ ] 降级机制有效

### 兼容性测试
- [ ] 不同平台GPSR订单（SMT、Amazon、Fruugo等）
- [ ] 不同浏览器环境
- [ ] 不同网络条件
- [ ] 不同数据量场景

## 📞 技术支持

如果在测试过程中遇到问题：

1. **收集信息**：
   - 浏览器控制台的完整错误日志
   - 网络请求的详细信息（F12 -> Network标签）
   - 测试用的发货单号和预期结果

2. **健康检查**：
   ```javascript
   var health = GpsrCacheManager.healthCheck();
   console.log('健康检查结果：', health);
   ```

3. **联系支持**：
   - 提供完整的错误信息和环境描述
   - 说明测试步骤和预期结果
   - 附上相关的日志和截图

通过这个测试指南，您可以全面验证GPSR缓存管理器在真实环境中的工作状态，确保生产部署的成功。
