# GPSR缓存管理器集成指南

## 📋 概述

GPSR缓存管理器是一个专门为优化GPSR打印标签性能而设计的JavaScript模块，通过智能预加载和缓存机制，将GPSR标签打印等待时间从3-8秒降低到接近0秒。

## 🚀 核心特性

- **智能预加载**：在扫描成功时自动预加载GPSR标签
- **内存缓存管理**：自动管理缓存大小，防止内存溢出
- **缓存过期机制**：确保数据新鲜度，避免使用过期数据
- **降级保护**：预加载失败时自动使用原有打印方式
- **性能监控**：实时监控预加载和打印性能
- **多平台支持**：支持SMT、Amazon、Fruugo等多种GPSR平台
- **兼容性保证**：与现有代码完全兼容，无需修改现有逻辑

## 📦 安装和引入

### 1. 引入JS文件

在需要使用GPSR功能的HTML页面中引入缓存管理器：

```html
<!-- 在现有的packing.js之前引入 -->
<script type="text/javascript" src="${CONTEXT_PATH}js/gpsr-cache-manager.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/packing.js?v=${.now?datetime}"></script>
```

### 2. 确保页面有GPSR容器

确保页面中存在GPSR打印容器（如果没有会自动创建）：

```html
<div id="print_gpsr_tag" style="display: none;"></div>
```

## 🔧 API使用指南

### 基础API

#### 1. 预加载GPSR标签

```javascript
// 基础预加载
GpsrCacheManager.preloadGpsrTag('APV202506300001')
    .then(function(result) {
        console.log('预加载成功：', result);
    })
    .catch(function(error) {
        console.error('预加载失败：', error);
    });

// 带选项的预加载
GpsrCacheManager.preloadGpsrTag('APV202506300001', {
    local: true,  // 是否使用本地接口，默认true
    retryCount: 0 // 重试次数，内部使用
});
```

#### 2. 打印GPSR标签

```javascript
// 打印所有SKU的GPSR标签
GpsrCacheManager.printGpsrTag('APV202506300001')
    .then(function(result) {
        console.log('打印成功：', result);
    });

// 打印指定SKU的GPSR标签
GpsrCacheManager.printGpsrTag('APV202506300001', 'SKU123456')
    .then(function(result) {
        console.log('打印成功：', result);
    });
```

#### 3. 缓存管理

```javascript
// 检查是否已缓存
if (GpsrCacheManager.isCached('APV202506300001')) {
    console.log('已缓存，可以快速打印');
}

// 清理指定缓存
GpsrCacheManager.clearGpsrCache('APV202506300001');

// 清理所有缓存
GpsrCacheManager.clearGpsrCache();

// 获取缓存统计
var stats = GpsrCacheManager.getCacheStats();
console.log('缓存统计：', stats);
```

### 高级API

#### 1. 批量预加载

```javascript
var apvNos = ['APV001', 'APV002', 'APV003'];
GpsrCacheManager.batchPreload(apvNos, {
    concurrency: 3 // 并发数限制
}).then(function(result) {
    console.log('批量预加载完成：', result);
});
```

#### 2. 智能预加载

```javascript
// 根据当前扫描情况智能预加载
GpsrCacheManager.smartPreload('APV202506300001');
```

#### 3. 健康检查

```javascript
var health = GpsrCacheManager.healthCheck();
console.log('系统健康状态：', health);
```

## 🔗 集成到现有页面

### 方式一：自动集成（推荐）

启用兼容模式，自动重写现有的`printGpsrTag`函数：

```javascript
// 在页面加载完成后启用兼容模式
$(document).ready(function() {
    // 启用兼容模式
    enableGpsrCompatibilityMode();
    
    // 现有代码无需修改，自动使用缓存管理器
});
```

### 方式二：手动集成

在扫描成功的回调中添加预加载：

```javascript
// 在扫描成功回调中添加
if (gpsrPlatform) {
    audioPlay('gpsr');
    // 添加预加载
    GpsrCacheManager.preloadGpsrTag(apvNo).catch(function(error) {
        console.warn('GPSR预加载失败：', error);
    });
}
```

## 📄 具体页面集成示例

### 1. apv_singlemoreproduct_scan.html

```javascript
// 在inputUniqueKey函数的成功回调中添加
success: function(response) {
    $("#check_scan_datas").html(response);
    // ... 现有逻辑 ...
    
    gpsrPlatform = $('#check_scan_datas').find("input[name='gpsrPlatform']").val();
    
    // 添加GPSR预加载
    if (gpsrPlatform) {
        GpsrCacheManager.preloadGpsrTag(apvNo).catch(function(error) {
            console.warn('GPSR预加载失败：', error);
        });
    }
    
    // ... 其他逻辑 ...
}
```

### 2. ss_init.html

```javascript
// 在input_submit函数的成功回调中添加
success: function(r) {
    $('#scan_datas').html(r);
    
    if($(r).find(".scan_success_sub").length == 1) {
        // ... 现有逻辑 ...
        
        gpsrPlatform = $(r).find("input[name='gpsrPlatform']").val();
        
        // 添加GPSR预加载
        if (gpsrPlatform) {
            audioPlay('gpsr');
            GpsrCacheManager.preloadGpsrTag(apvNo).catch(function(error) {
                console.warn('GPSR预加载失败：', error);
            });
        }
        
        // ... 其他逻辑 ...
    }
}
```

## ⚙️ 配置选项

```javascript
// 修改配置
GpsrCacheManager.setConfig({
    CACHE_MAX_SIZE: 15,        // 增加缓存大小
    CACHE_EXPIRE_TIME: 10 * 60 * 1000, // 延长过期时间到10分钟
    PERFORMANCE_LOG: true,     // 启用性能日志
    DEBUG_MODE: true          // 启用调试模式
});

// 获取当前配置
var config = GpsrCacheManager.getConfig();
console.log('当前配置：', config);
```

## 📊 性能监控

```javascript
// 获取性能统计
var perfStats = GpsrCacheManager.getPerformanceStats();
console.log('性能统计：', perfStats);

// 监控特定操作
GpsrCacheManager.performance.start('custom_operation');
// ... 执行操作 ...
var duration = GpsrCacheManager.performance.end('custom_operation');
console.log('操作耗时：', duration + 'ms');
```

## 🐛 故障排除

### 常见问题

1. **预加载失败**
   ```javascript
   // 检查网络连接和后端接口
   GpsrCacheManager.healthCheck();
   ```

2. **打印不工作**
   ```javascript
   // 检查打印机配置和打印控件
   var health = GpsrCacheManager.healthCheck();
   if (!health.dependencies.lodop) {
       console.error('打印控件未安装');
   }
   ```

3. **缓存占用过多内存**
   ```javascript
   // 调整缓存大小
   GpsrCacheManager.setConfig({ CACHE_MAX_SIZE: 5 });
   
   // 手动清理缓存
   GpsrCacheManager.clearGpsrCache();
   ```

### 调试模式

```javascript
// 启用调试模式查看详细日志
GpsrCacheManager.setConfig({ DEBUG_MODE: true });
```

## 🔄 升级和维护

### 版本兼容性

- 向后兼容现有的`printGpsrTag`和`printLocalGpsrTag`函数
- 可以逐步迁移到新API，无需一次性修改所有代码

### 性能优化建议

1. **合理设置缓存大小**：根据实际使用情况调整`CACHE_MAX_SIZE`
2. **监控缓存命中率**：定期检查缓存统计信息
3. **及时清理无用缓存**：在任务完成后清理相关缓存

## 📞 技术支持

如有问题或建议，请联系开发团队或查看相关文档。
