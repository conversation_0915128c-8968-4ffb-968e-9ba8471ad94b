**接口名称:**

删除自定义标记原因

**接口描述:**

删除自定义标记原因

**请求路径:**

/exceptionMarkConfig/removeCustomReason

**请求方式:**

POST

**请求参数:**

- Header

|参数名|参数值|必填|描述|
|:-----|:-----|:-----|:-----|
|Content-Type|application/x-www-form-urlencoded|Y||


- Param

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|reasonName|String|Y|原因名称|


- Body



**请求示例:**

```Form
reasonName=商品变形
```

**返回参数:**

|参数名|类型|必选|描述|
|:-----|:-----|:-----|:-----|
|success|boolean|N|api返回状态|
|errorMsg|String|N|返回错误信息|
|result|Boolean|N|api返回值|
|exceptionCode|String|N|异常编码|


**返回示例:**

```JSON
{
    "success": true,
    "errorMsg": "",
    "result": true,
    "exceptionCode": ""
}
```

**错误码说明:**

- 原因名称不能为空
- 原因名称不存在  
- 删除失败
- 删除自定义标记原因失败

**创建时间:** 2025-06-30

**作者:** Amoi 