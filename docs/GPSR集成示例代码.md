# GPSR缓存管理器集成示例代码

## 📋 完整集成示例

### 1. apv_singlemoreproduct_scan.html 集成示例

```html
<!-- 在现有脚本引入之前添加 -->
<script type="text/javascript" src="${CONTEXT_PATH}js/gpsr-cache-manager.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/packing.js?v=${.now?datetime}"></script>

<script type="text/javascript">
    // 页面加载完成后启用兼容模式（推荐方式）
    $(document).ready(function() {
        // 启用GPSR兼容模式，自动重写printGpsrTag函数
        enableGpsrCompatibilityMode();
        
        // 现有的初始化代码保持不变
        initSkuUuIdStorageCache(uuIdCacheKey);
        initScanTaskYSTSkuKeyQtyCache();
        pageInit();
        // ... 其他初始化代码
    });

    // 修改inputUniqueKey函数，添加预加载逻辑
    function inputUniqueKey(sku, uuid, bindingApvNo) {
        var uniqueKey = $.trim(uuid);

        var r = $.ajax({
            url: CONTEXT_PATH + "single/batch/scans/check/uniqueKey",
            data: {uniqueKey: uniqueKey},
            timeout: 90 * 1000,
            success: function(response) {
                $("#check_scan_datas").html(response);
                const errorElement = $(response).find("#scan-error");
                
                if (response.length > 230 && errorElement.length === 0) {
                    // 扫描成功，获取发货单信息
                    var apvNo = $('#check_scan_datas').find("input[name='apvNo']").val();
                    shipStatus = $('#check_scan_datas').find("input[name='shipStatus']").val();
                    gpsrPlatform = $('#check_scan_datas').find("input[name='gpsrPlatform']").val();
                    
                    // ✅ 新增：GPSR预加载逻辑
                    if (gpsrPlatform && apvNo) {
                        console.log('[GPSR] 检测到GPSR订单，开始预加载：' + apvNo);
                        GpsrCacheManager.preloadGpsrTag(apvNo)
                            .then(function(result) {
                                console.log('[GPSR] 预加载成功：' + apvNo, result);
                            })
                            .catch(function(error) {
                                console.warn('[GPSR] 预加载失败：' + apvNo, error);
                            });
                    }
                    
                    // 其他现有逻辑保持不变
                    $("#apv-no-now").val(apvNo);
                    $("#sku-now").val(sku);
                    // ... 其他逻辑
                    
                    checkIn(sku, uuid, bindingApvNo);
                } else {
                    // 错误处理逻辑保持不变
                    audioPlay('error');
                    // ... 错误处理
                }
            },
            error: function() {
                layer.alert('扫描失败，请重新扫描');
            }
        });
    }

    // checkIn函数中的GPSR打印逻辑保持不变
    // 因为启用了兼容模式，printGpsrTag会自动使用缓存
    function checkIn(sku, uuid, bindingApvNo) {
        // ... 现有逻辑 ...
        
        // 这行代码无需修改，会自动使用缓存管理器
        } else if (gpsrPlatform && needQty === scanQty) {
            printGpsrTag(apvNo, sku); // 自动使用缓存
        }
        
        // ... 其他逻辑 ...
    }
</script>
```

### 2. ss_init.html 集成示例

```html
<!-- 在现有脚本引入之前添加 -->
<script type="text/javascript" src="${CONTEXT_PATH}js/gpsr-cache-manager.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/packing.js?v=${.now?datetime}"></script>

<script type="text/javascript">
    $(document).ready(function() {
        // 启用GPSR兼容模式
        enableGpsrCompatibilityMode();
        
        // 现有初始化代码保持不变
        input_init();
        // ... 其他初始化
    });

    // 修改input_submit函数
    function input_submit(obj) {
        var sku = "sku=" + obj.value;
        sku = sku + "&isJit=" + false;
        var date = new Date();
        
        var r = $.ajax({
            type: "get",
            url: CONTEXT_PATH + "apv/packs/ss/scan/sku?time=" + date,
            data: sku,
            timeout: 100000,
            async: false,
            beforeSend: function() {
                App.blockUI(null, null, 500);
                $("#sku").attr("disabled", true);
            },
            success: function(r) {
                $('#scan_datas').html(r);
                
                if ($(r).find(".scan_success_sub").length == 1) {
                    // 统计数量
                    calsf();
                    
                    // 获取发货单信息
                    apvNo = $(r).find("input[name='apvNo']").val();
                    trackingNumber = $(r).find("input[name='trackingNumber']").val();
                    shipStatus = $(r).find("input[name='shipStatus']").val();
                    gpsrPlatform = $(r).find("input[name='gpsrPlatform']").val();
                    
                    // ✅ 新增：GPSR预加载逻辑
                    if (gpsrPlatform && apvNo) {
                        console.log('[GPSR] 检测到GPSR订单，开始预加载：' + apvNo);
                        GpsrCacheManager.preloadGpsrTag(apvNo)
                            .then(function(result) {
                                console.log('[GPSR] 预加载成功：' + apvNo, result);
                                audioPlay('gpsr'); // 预加载成功后播放音效
                            })
                            .catch(function(error) {
                                console.warn('[GPSR] 预加载失败：' + apvNo, error);
                                audioPlay('gpsr'); // 即使预加载失败也播放音效
                            });
                    } else {
                        audioPlay('success');
                    }
                    
                    // 打印发货单
                    printApvNo(apvNo);
                    
                } else {
                    // 错误处理保持不变
                    App.unblockUI();
                    audioPlay('error');
                    // ... 错误处理
                }
                
                // 防止重复扫描
                setTimeout(removeDisabled, 1000);
            },
            error: function() {
                layer.alert('扫描失败，请重新扫描/' + sku);
            }
        });
    }

    // printApvNo函数中的GPSR打印保持不变
    function printApvNo(apvNo) {
        var sku = $('#sku').val();

        // 这行代码无需修改，会自动使用缓存管理器
        if (gpsrPlatform) {
            printGpsrTag(apvNo, null); // 自动使用缓存
        }
        
        // ... 其他打印逻辑保持不变
    }
</script>
```

### 3. 手动集成方式（不使用兼容模式）

```javascript
// 如果不想使用兼容模式，可以手动调用API
$(document).ready(function() {
    // 不启用兼容模式，手动管理
});

// 在扫描成功后手动预加载
function onScanSuccess(apvNo, gpsrPlatform) {
    if (gpsrPlatform) {
        // 手动预加载
        GpsrCacheManager.preloadGpsrTag(apvNo)
            .then(function(result) {
                console.log('GPSR预加载完成：', result);
            })
            .catch(function(error) {
                console.warn('GPSR预加载失败：', error);
            });
    }
}

// 在需要打印时手动调用
function onPrintGpsr(apvNo, sku) {
    GpsrCacheManager.printGpsrTag(apvNo, sku)
        .then(function(result) {
            if (result.fallback) {
                console.log('使用降级方式打印成功');
            } else {
                console.log('使用缓存打印成功，打印数量：', result.printCount);
            }
        })
        .catch(function(error) {
            console.error('GPSR打印失败：', error);
            // 可以尝试原有方式
            if (typeof printLocalGpsrTag === 'function') {
                printLocalGpsrTag(apvNo, sku, true);
            }
        });
}
```

## 🔧 高级集成示例

### 1. 批量预加载示例

```javascript
// 在任务开始时批量预加载
function onTaskStart(taskApvNos) {
    if (taskApvNos && taskApvNos.length > 0) {
        console.log('开始批量预加载GPSR标签：', taskApvNos);
        
        GpsrCacheManager.batchPreload(taskApvNos, {
            concurrency: 3 // 限制并发数
        }).then(function(result) {
            console.log('批量预加载完成：', result);
            console.log('成功：' + result.successCount + '，失败：' + result.errorCount);
        });
    }
}
```

### 2. 智能预加载示例

```javascript
// 根据扫描历史智能预加载
function smartPreloadBasedOnHistory(currentApvNo, taskNo) {
    // 预加载当前发货单
    GpsrCacheManager.smartPreload(currentApvNo);
    
    // 可以扩展：根据任务号预加载同批次的其他发货单
    // 这里可以调用后端接口获取同批次的发货单列表
    $.get(CONTEXT_PATH + 'apv/packs/getTaskApvNos?taskNo=' + taskNo)
        .then(function(apvNos) {
            if (apvNos && apvNos.length > 1) {
                // 预加载其他发货单（排除当前的）
                var otherApvNos = apvNos.filter(function(apvNo) {
                    return apvNo !== currentApvNo;
                });
                
                if (otherApvNos.length > 0) {
                    GpsrCacheManager.batchPreload(otherApvNos, {
                        concurrency: 2 // 降低并发数，避免影响当前操作
                    });
                }
            }
        });
}
```

### 3. 性能监控集成

```javascript
// 定期输出性能统计
setInterval(function() {
    var cacheStats = GpsrCacheManager.getCacheStats();
    var perfStats = GpsrCacheManager.getPerformanceStats();
    var health = GpsrCacheManager.healthCheck();
    
    console.log('[GPSR监控] 缓存统计：', cacheStats);
    console.log('[GPSR监控] 性能统计：', perfStats);
    console.log('[GPSR监控] 健康状态：', health.status);
    
    // 可以将统计数据发送到监控系统
    // sendToMonitoringSystem({ cache: cacheStats, performance: perfStats, health: health });
}, 5 * 60 * 1000); // 每5分钟输出一次
```

### 4. 错误处理和降级示例

```javascript
// 完整的错误处理和降级逻辑
function robustGpsrPrint(apvNo, sku) {
    return GpsrCacheManager.printGpsrTag(apvNo, sku)
        .catch(function(error) {
            console.warn('[GPSR] 缓存打印失败，尝试降级：', error);
            
            // 尝试原有方式
            if (typeof printLocalGpsrTag === 'function') {
                try {
                    printLocalGpsrTag(apvNo, sku, true);
                    return { success: true, fallback: true, method: 'printLocalGpsrTag' };
                } catch (fallbackError) {
                    console.error('[GPSR] 降级打印也失败：', fallbackError);
                    throw new Error('所有打印方式都失败');
                }
            } else {
                throw new Error('没有可用的降级打印方式');
            }
        });
}
```

## 📊 配置和调优示例

```javascript
// 根据实际使用情况调优配置
$(document).ready(function() {
    // 生产环境配置
    GpsrCacheManager.setConfig({
        CACHE_MAX_SIZE: 20,           // 增加缓存大小
        CACHE_EXPIRE_TIME: 10 * 60 * 1000, // 10分钟过期
        RETRY_MAX_COUNT: 3,           // 增加重试次数
        PERFORMANCE_LOG: false,       // 关闭性能日志
        DEBUG_MODE: false            // 关闭调试模式
    });
    
    // 启用兼容模式
    enableGpsrCompatibilityMode();
});

// 开发环境配置
if (window.location.hostname === 'localhost' || window.location.hostname.includes('test')) {
    GpsrCacheManager.setConfig({
        PERFORMANCE_LOG: true,
        DEBUG_MODE: true
    });
}
```

这些示例展示了如何在不同场景下集成GPSR缓存管理器，既保持了与现有代码的兼容性，又充分利用了缓存优化的优势。
