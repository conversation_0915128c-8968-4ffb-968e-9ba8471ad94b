-- ============================================================================
-- 入库异常单标记功能 - 数据库变更脚本
-- 创建时间: 2025-06-28
-- 说明: 为入库异常单表添加标记相关字段，支持异常单标记功能
-- ============================================================================

-- 1. 为入库异常单表添加标记相关字段
ALTER TABLE wh_check_in_exception 
ADD COLUMN mark_reason VARCHAR(200) NULL COMMENT '标记原因' AFTER tags,
ADD COLUMN mark_time DATETIME NULL COMMENT '标记时间' AFTER mark_reason,
ADD COLUMN mark_user_id INT NULL COMMENT '标记人员ID' AFTER mark_time;

-- 2. 为标记相关字段添加索引（可选，根据查询需求调整）
-- 标记状态索引（用于筛选已标记/未标记的异常单）
CREATE INDEX idx_wh_checkin_exception_mark_reason ON wh_check_in_exception(mark_reason);

-- 标记时间索引（用于按标记时间排序和筛选）
CREATE INDEX idx_wh_checkin_exception_mark_time ON wh_check_in_exception(mark_time);

-- 标记人员索引（用于按标记人员筛选）
CREATE INDEX idx_wh_checkin_exception_mark_user ON wh_check_in_exception(mark_user_id);

-- 复合索引：标记状态+时间（用于高效的标记数据查询）
CREATE INDEX idx_wh_checkin_exception_mark_status_time ON wh_check_in_exception(mark_reason, mark_time);

-- 3. 验证字段添加是否成功
-- 查看表结构
-- DESCRIBE wh_check_in_exception;

-- 4. 数据验证查询（可选）
-- 查询所有标记相关字段
-- SELECT id, sku, mark_reason, mark_remark, mark_time, mark_user_name 
-- FROM wh_check_in_exception 
-- WHERE mark_reason IS NOT NULL 
-- ORDER BY mark_time DESC 
-- LIMIT 10;

-- ============================================================================
-- 变更说明:
-- 1. mark_reason: 标记原因，VARCHAR(200)，可存储预设原因或自定义原因
-- 2. mark_time: 标记时间，DATETIME类型，记录标记的具体时间
-- 3. mark_user_id: 标记人员ID，INT类型，关联用户表，后续可根据ID获取人员姓名
-- 
-- 注意事项:
-- - 所有新增字段都允许NULL值，不影响现有数据
-- - mark_reason为NULL表示未标记，有值表示已标记
-- - 标记备注记录在exceptionLog中，不新增字段
-- - 索引根据实际查询需求可以调整或删除
-- ============================================================================