# GPSR缓存管理器故障排除指南

## 🚨 常见问题及解决方案

### 1. 批量预加载报错

#### 问题描述
在测试页面中点击"批量预加载"按钮时出现JavaScript错误。

#### 可能原因
1. **this上下文丢失**：在嵌套函数中`this`引用丢失
2. **Promise链错误**：Promise处理逻辑有误
3. **输入验证失败**：输入的发货单号格式不正确
4. **网络请求失败**：后端接口不可用

#### 解决方案

##### 方案1：检查控制台错误信息
```javascript
// 打开浏览器开发者工具，查看Console标签页的错误信息
// 常见错误类型：
// - TypeError: Cannot read property 'preloadGpsrTag' of undefined
// - ReferenceError: GpsrCacheManager is not defined
// - Promise rejection: Network error
```

##### 方案2：验证输入格式
```javascript
// 确保发货单号格式正确
// 正确格式：APV001,APV002,APV003
// 错误格式：APV001, APV002, APV003（多余空格）
// 错误格式：APV001;APV002;APV003（错误分隔符）

function validateApvNos(apvNosStr) {
    var apvNos = apvNosStr.split(',').map(function(s) { 
        return s.trim(); 
    }).filter(function(s) { 
        return s && s.length > 0; 
    });
    
    console.log('解析的发货单号：', apvNos);
    return apvNos;
}
```

##### 方案3：启用调试模式
```javascript
// 在测试前启用调试模式
GpsrCacheManager.setConfig({
    DEBUG_MODE: true,
    PERFORMANCE_LOG: true
});

// 查看详细日志
console.log('当前配置：', GpsrCacheManager.getConfig());
console.log('健康检查：', GpsrCacheManager.healthCheck());
```

##### 方案4：手动测试单个预加载
```javascript
// 先测试单个发货单预加载是否正常
GpsrCacheManager.preloadGpsrTag('APV001')
    .then(function(result) {
        console.log('单个预加载成功：', result);
        // 如果单个成功，再测试批量
        return GpsrCacheManager.batchPreload(['APV001', 'APV002'], {
            concurrency: 1
        });
    })
    .then(function(batchResult) {
        console.log('批量预加载成功：', batchResult);
    })
    .catch(function(error) {
        console.error('测试失败：', error);
    });
```

### 2. 预加载失败

#### 问题描述
单个或批量预加载返回失败状态。

#### 可能原因
1. **网络连接问题**
2. **后端接口错误**
3. **发货单号不存在**
4. **权限不足**

#### 解决方案

##### 检查网络连接
```javascript
// 测试网络连接
fetch(window.CONTEXT_PATH + 'apv/packs/printGpsrTag?apvNo=TEST')
    .then(response => {
        console.log('网络状态：', response.status);
        return response.text();
    })
    .then(data => {
        console.log('响应数据：', data);
    })
    .catch(error => {
        console.error('网络错误：', error);
    });
```

##### 检查后端接口
```javascript
// 直接访问后端接口
var testUrl = window.CONTEXT_PATH + 'apv/packs/printGpsrTag?apvNo=APV202506300001';
console.log('测试URL：', testUrl);

// 在浏览器中直接访问该URL，查看返回结果
```

### 3. 打印功能不工作

#### 问题描述
预加载成功但打印时没有反应或报错。

#### 可能原因
1. **打印控件未安装**
2. **打印机未配置**
3. **缓存数据格式错误**

#### 解决方案

##### 检查打印环境
```javascript
// 检查打印控件
if (typeof window.getLodop !== 'function') {
    console.error('打印控件未加载');
} else {
    var LODOP = window.getLodop();
    if (!LODOP) {
        console.error('打印控件不可用');
    } else {
        console.log('打印控件正常');
    }
}

// 检查打印机配置
if (!window.jitPrinter75Tag) {
    console.error('打印机未配置');
} else {
    console.log('打印机配置：', window.jitPrinter75Tag);
}
```

##### 检查缓存数据
```javascript
// 查看缓存的GPSR数据
var cached = GpsrCacheManager.cache.get('APV202506300001');
if (cached) {
    console.log('缓存数据：', cached);
    
    // 检查数据格式
    var $content = $(cached.content);
    var gpsrTags = $content.find('.gpsr-tag-print');
    console.log('找到GPSR标签数量：', gpsrTags.length);
    
    gpsrTags.each(function(index) {
        var $tag = $(this);
        console.log('标签' + index + '：', {
            sku: $tag.find("input[name='printSku']").val(),
            qty: $tag.find("input[name='printQty']").val(),
            url: $tag.find("input[name='printUrl']").val()
        });
    });
} else {
    console.log('未找到缓存数据');
}
```

### 4. 内存占用过高

#### 问题描述
长时间使用后浏览器变慢或崩溃。

#### 解决方案

##### 调整缓存配置
```javascript
// 减少缓存大小
GpsrCacheManager.setConfig({
    CACHE_MAX_SIZE: 5,           // 减少到5个
    CACHE_EXPIRE_TIME: 3 * 60 * 1000  // 减少到3分钟
});
```

##### 定期清理缓存
```javascript
// 定期清理缓存
setInterval(function() {
    var stats = GpsrCacheManager.getCacheStats();
    if (stats.totalCount > 8) {
        GpsrCacheManager.clearGpsrCache();
        console.log('自动清理缓存');
    }
}, 2 * 60 * 1000); // 每2分钟检查一次
```

### 5. 兼容性问题

#### 问题描述
在某些页面中启用兼容模式后出现问题。

#### 解决方案

##### 检查函数冲突
```javascript
// 检查是否有函数冲突
console.log('原始printGpsrTag：', window._originalPrintGpsrTag);
console.log('当前printGpsrTag：', window.printGpsrTag);

// 如果有冲突，手动恢复
if (window._originalPrintGpsrTag) {
    window.printGpsrTag = window._originalPrintGpsrTag;
    console.log('已恢复原始函数');
}
```

##### 使用手动集成
```javascript
// 如果兼容模式有问题，使用手动集成
// 不调用enableGpsrCompatibilityMode()
// 直接使用GpsrCacheManager的API

// 预加载
if (gpsrPlatform) {
    GpsrCacheManager.preloadGpsrTag(apvNo);
}

// 打印
GpsrCacheManager.printGpsrTag(apvNo, sku);
```

## 🔧 调试工具

### 1. 健康检查
```javascript
// 全面的健康检查
function fullHealthCheck() {
    var health = GpsrCacheManager.healthCheck();
    var cache = GpsrCacheManager.getCacheStats();
    var perf = GpsrCacheManager.getPerformanceStats();
    
    console.log('=== GPSR健康检查 ===');
    console.log('状态：', health.status);
    console.log('缓存：', cache);
    console.log('性能：', perf);
    console.log('问题：', health.issues || '无');
    
    return health.status === 'healthy';
}
```

### 2. 性能分析
```javascript
// 性能分析工具
function performanceAnalysis() {
    var stats = GpsrCacheManager.getPerformanceStats();
    
    for (var key in stats) {
        var metric = stats[key];
        if (metric.duration) {
            console.log(key + '：' + metric.duration.toFixed(2) + 'ms');
        }
    }
}
```

### 3. 缓存分析
```javascript
// 缓存分析工具
function cacheAnalysis() {
    var cache = GpsrCacheManager.cache.data;
    
    console.log('=== 缓存分析 ===');
    console.log('总数：', Object.keys(cache).length);
    
    for (var apvNo in cache) {
        var item = cache[apvNo];
        var age = Date.now() - item.timestamp;
        console.log(apvNo + '：', {
            平台: item.platform,
            年龄: Math.round(age / 1000) + '秒',
            PDF数量: item.pdfUrls.length
        });
    }
}
```

## 📞 获取帮助

如果以上解决方案都无法解决问题，请：

1. **收集错误信息**：完整的控制台错误日志
2. **记录操作步骤**：重现问题的具体步骤
3. **环境信息**：浏览器版本、操作系统等
4. **配置信息**：当前的GPSR配置和健康检查结果

联系技术支持时请提供这些信息以便快速定位问题。
