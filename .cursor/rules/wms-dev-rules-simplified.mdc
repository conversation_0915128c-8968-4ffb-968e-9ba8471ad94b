---
description: 
globs: 
alwaysApply: true
---
# WMS开发规则 - AI快速参考 (主规则文档)

> **🎯 这是WMS项目的主要开发规则文档，专为AI快速参考设计**  
> **📚 详细规范请参考**: [主导航文档](mdc:wms-dev-rules.mdc)

## 📖 文档导航 | Document Navigation

### 🏗️ 项目架构 | Project Architecture
- **中文**: [wms-architecture-rules-zh.md](mdc:../docs/wms-architecture-rules-zh.md)
- **内容**: 技术栈、项目结构、架构设计原则

### 💻 核心编码规范 | Core Coding Standards  
- **中文**: [wms-coding-rules-zh.md](mdc:../docs/wms-coding-rules-zh.md)
- **内容**: 架构分层、代码质量、异常处理、日志规范

### 🗄️ DAO层开发规范 | DAO Layer Standards
- **中文**: [dao-layer-coding-rules.md](mdc:../docs/dao-layer-coding-rules.md)
- **内容**: SQL映射框架、数据访问模式、性能优化

### 📋 扩展开发规范 | Extended Development Rules
- **扩展规范**: [wms-fx-rules.mdc](mdc:wms-fx-rules.mdc)
- **内容**: 实体bean规范、前后端分离接口、接口文档、HTTP请求

### 🎨 前端页面规范 | Frontend Page Standards
- **前端规范**: [wms-page-rules.mdc](mdc:wms-page-rules.mdc)
- **内容**: HTML结构、JavaScript规范、FreeMarker模板、命名约定

---

## 🎯 核心原则 | Core Principles

### 1. 架构分层 | Architecture Layers
```
Controller (action) → Service → DAO → Database
```

### 2. 技术栈 | Technology Stack
```
Spring Boot 2.0.8 + 自定义SQL映射 + FreeMarker + MySQL 5.1.38
```

### 3. 关键注解 | Key Annotations
```java
@Controller / @RestController  // 控制器
@Service                      // 服务层 (禁止@Transactional，使用AOP自动管理)
@Repository                   // 数据访问层
@Slf4j                       // 日志
@Resource                    // 依赖注入
```

---

## 🚫 禁止事项 | Prohibited

### 🔴 核心禁止（严重错误）
1. ❌ Controller直接调用DAO | Controller calling DAO directly
2. ❌ 硬编码字段名 | Hard-coded field names  
3. ❌ 手动声明Logger | Manual Logger declaration
4. ❌ 使用@Autowired注解 | Using @Autowired annotation
5. ❌ 单个方法超过100行 | Single method over 100 lines
6. ❌ Service类超过2000行 | Service class over 2000 lines
7. ❌ Controller类超过1000行 | Controller class over 1000 lines
8. ❌ DAO类超过1500行 | DAO class over 1500 lines
9. ❌ 忽略空值检查 | Ignoring null checks
10. ❌ 字符串拼接SQL | String concatenation for SQL

### 🔴 事务管理禁止（严重错误）
11. ❌ 使用@Transactional注解 | Using @Transactional annotation
12. ❌ 不规范的方法命名 | Non-standard method naming
13. ❌ 跳过事务前缀规则 | Ignoring transaction prefix rules

### 🔴 现代Java特性禁止（严重错误）
14. ❌ Stream链超过5个操作 | Stream chain over 5 operations
15. ❌ Lambda表达式超过3行 | Lambda expressions over 3 lines
16. ❌ Stream中副作用操作 | Side effects in Stream operations

### 🔴 扩展规范禁止（严重错误）
17. ❌ 修改实体bean类型 | Modifying entity bean types
18. ❌ 随意新增接口 | Adding interfaces arbitrarily
19. ❌ 不规范的接口文档 | Non-standard API documentation

### 🔴 代码注释禁止（严重错误）
20. ❌ 遗漏类注释 | Missing class comments with <AUTHOR> ❌ 公共方法无注释 | Public methods without JavaDoc

**详细禁止事项**: 参考 [核心编码规范](mdc:../docs/wms-coding-rules-zh.md)

---

## ✅ 必须检查 | Must Check

### 基础检查项
- [ ] @Repository注解 | @Repository annotation
- [ ] setQueryCondition()方法 | setQueryCondition() method  
- [ ] DBField常量 | DBField constants
- [ ] DataType枚举 | DataType enumeration
- [ ] 空值检查 | Null value checks
- [ ] @Slf4j注解 | @Slf4j annotation
- [ ] 异常处理 | Exception handling
- [ ] 方法注释 | Method comments

### 代码质量检查项 | Code Quality Checks
- [ ] 单个方法不超过100行 | Single method under 100 lines
- [ ] 方法复杂度不超过10 | Method complexity under 10
- [ ] Service类不超过2000行 | Service class under 2000 lines
- [ ] Controller类不超过1000行 | Controller class under 1000 lines
- [ ] DAO类不超过1500行 | DAO class under 1500 lines
- [ ] 普通类不超过1000行 | General class under 1000 lines

### 现代Java特性检查项 | Modern Java Features Checks
- [ ] Stream链不超过5个操作 | Stream chain under 5 operations
- [ ] Lambda表达式不超过3行 | Lambda expressions under 3 lines
- [ ] Optional正确使用 | Proper Optional usage
- [ ] 避免Stream副作用操作 | Avoid Stream side effects
- [ ] 函数式接口使用标准 | Standard functional interface usage
- [ ] 方法引用替代Lambda | Method references over Lambda
- [ ] 现代特性提高可读性 | Modern features improve readability

### 事务检查项 | Transaction Checks
- [ ] 禁止使用@Transactional注解 | Prohibit @Transactional annotation
- [ ] 方法命名符合事务规则 | Method naming follows transaction rules
- [ ] 查询方法使用get/search/query/find/list/count前缀 | Query methods use proper prefixes
- [ ] 写操作方法使用save/add/insert/create/update/delete/batch/do前缀 | Write methods use proper prefixes
- [ ] 依赖AOP自动事务管理 | Rely on AOP automatic transaction management

### 扩展检查项
- [ ] 实体bean类型一致性 | Entity bean type consistency
- [ ] 接口返回ApiResult | Interface returns ApiResult
- [ ] 标准接口文档格式 | Standard API documentation format
- [ ] HTTP请求规范 | HTTP request standards

### 代码注释检查项 | Code Comment Checks
- [ ] 类注释包含@author标识 | Class comments include <AUTHOR> [ ] 类功能描述清晰 | Clear class description
- [ ] 公共方法有JavaDoc注释 | Public methods have JavaDoc
- [ ] 方法参数描述完整 | Complete parameter descriptions
- [ ] 返回值描述明确 | Clear return value descriptions
- [ ] 异常说明详细 | Detailed exception descriptions

### 架构检查项 | Architecture Checks
- [ ] Controller不直接调用DAO | Controller does not directly call DAO
- [ ] Service层包含业务逻辑 | Service layer contains business logic
- [ ] DAO层只进行数据访问 | DAO layer only performs data access
- [ ] 分层职责清晰分离 | Layer responsibilities clearly separated

### 异常处理检查项 | Exception Handling Checks
- [ ] 使用BusinessException | Use BusinessException
- [ ] 实现全局异常处理 | Implement global exception handling
- [ ] 异常信息有意义 | Exception messages are meaningful
- [ ] 不暴露系统异常 | Do not expose system exceptions

---

## 📏 代码限制标准 | Code Limit Standards

### 🎯 统一限制规范
```yaml
# WMS项目代码限制标准
代码限制:
  单个方法:
    最大行数: 100行
    最大复杂度: 10
    
  类级别限制:
    Service层: 2000行
    Controller层: 1000行  
    DAO层: 1500行
    普通类: 1000行
    
  现代Java特性:
    Stream链: 最多5个操作
    Lambda表达式: 最多3行
```

### 🔧 拆分策略 | Splitting Strategies

#### 方法拆分
```java
// ✅ 正确：单个方法控制在100行内
public void processOrder(Order order) {
    validateOrder(order);      // 提取验证方法
    calculateAmounts(order);   // 提取计算方法  
    updateOrderStatus(order);  // 提取状态更新方法
    sendNotification(order);   // 提取通知方法
}
```

#### 类拆分
```java
// ✅ 正确：Service类控制在2000行内，合理拆分
@Service
public class OrderServiceImpl implements OrderService {
    @Resource
    private OrderValidationService validationService;  // 验证服务
    @Resource
    private OrderCalculationService calculationService; // 计算服务
    @Resource
    private OrderNotificationService notificationService; // 通知服务
}
```

---

## 🔧 SQL映射规范 | SQL Mapping Standards

### 查询操作
```xml
<sql id="queryEntity">
  SELECT * FROM table WHERE 1 = 1
  <[AND id = :id]>
  <[AND name = :name]>
</sql>
```

### ✅ 正确的条件更新操作
```xml
<sql id="updateEntity">
  UPDATE table SET
    <[name = :name,]>
    <[status = :status,]>
  id = id
  WHERE 1 = 1
  AND id = :id
</sql>
```

### ❌ 错误的强制更新操作
```xml
<!-- 禁止：强制覆盖所有字段 -->
<sql id="updateEntityWrong">
  UPDATE table SET
    name = :name,
    status = :status
  WHERE id = :id
</sql>
```

---

## 📚 详细文档引用 | Detailed Documentation References

- **完整架构规范**: [项目架构规范](mdc:../docs/wms-architecture-rules-zh.md)
- **详细编码规范**: [核心编码规范](mdc:../docs/wms-coding-rules-zh.md)
- **DAO层详细实现**: [DAO层开发规范](mdc:../docs/dao-layer-coding-rules.md)
- **扩展功能规范**: [扩展开发规范](mdc:wms-fx-rules.mdc)
- **前端开发规范**: [前端页面规范](mdc:wms-page-rules.mdc)
- **使用指南**: [Cursor使用指南](mdc:../docs/cursor-usage-guide.md)

---

**项目**: WMS仓库管理系统 | WMS Warehouse Management System  
**版本**: v1.0  
**作者**: Amoi


