---
description: 
globs: 
alwaysApply: true
---
# WMS仓库管理系统开发规则文档 (主导航文档)

> **💡 AI开发建议**: 日常开发请优先参考 [AI快速参考](mdc:wms-dev-rules-simplified.mdc)  
> **📖 本文档用途**: 项目架构了解和详细规范查询

## 📖 文档导航 | Document Navigation

### 🏗️ 项目架构规范 | Project Architecture
- **中文**: [docs/wms-architecture-rules-zh.md](mdc:../docs/wms-architecture-rules-zh.md)
- **内容**: 技术栈、项目结构、架构设计原则、分布式架构

### 💻 核心编码规范 | Core Coding Standards  
- **中文**: [docs/wms-coding-rules-zh.md](mdc:../docs/wms-coding-rules-zh.md)
- **内容**: 架构分层详细规则、代码质量控制、异常处理、日志规范

### 🗄️ DAO层开发规范 | DAO Layer Standards
- **中文**: [docs/dao-layer-coding-rules.md](mdc:../docs/dao-layer-coding-rules.md)
- **内容**: SQL映射框架、数据访问模式、性能优化

### 🔄 开发流程规范 | Development Process
- **中文**: [docs/wms-development-process-zh.md](mdc:../docs/wms-development-process-zh.md)
- **内容**: 开发流程、技术框架使用、错误管理

### 🚀 AI快速参考 | AI Quick Reference
- **中英双语**: [.cursor/rules/wms-dev-rules-simplified.mdc](mdc:wms-dev-rules-simplified.mdc)
- **内容**: 核心原则、快速模板、检查清单

### 📋 扩展开发规范 | Extended Development Rules
- **扩展规范**: [.cursor/rules/wms-fx-rules.mdc](mdc:wms-fx-rules.mdc)
- **内容**: 实体bean规范、前后端分离接口、接口文档、HTTP请求

### 🎨 前端页面规范 | Frontend Page Standards
- **前端规范**: [.cursor/rules/wms-page-rules.mdc](mdc:wms-page-rules.mdc)
- **内容**: HTML结构、JavaScript规范、FreeMarker模板、命名约定

### 📝 使用指南 | Usage Guide
- **使用指南**: [docs/cursor-usage-guide.md](mdc:../docs/cursor-usage-guide.md)
- **内容**: Cursor IDE中使用规则文档的完整指南

---

## 🏗️ 项目基本信息

- **项目名称**: WMS仓库管理系统 (Warehouse Management System)
- **技术栈**: Spring Boot 2.0.8 + 自定义SQL映射 + FreeMarker
- **版本**: v1.0
- **作者**: Amoi

---

## 🎯 核心原则概述 (Core Principles Overview)

### 1. 架构分层
```java
Controller (action) → Service → DAO → Database
```
**详细规范**: 参考 [核心编码规范](mdc:../docs/wms-coding-rules-zh.md)

### 2. 技术框架
- **Spring Boot**: 2.0.8 + 自定义SQL映射
- **详细信息**: 参考 [项目架构规范](mdc:../docs/wms-architecture-rules-zh.md)

### 3. 关键注解 | Key Annotations
```java
@Controller / @RestController  // 控制器
@Service                      // 服务层 (禁止@Transactional，使用AOP自动管理)
@Repository                   // 数据访问层
@Slf4j                       // 日志
@Resource                    // 依赖注入
```

### 4. 开发规范
- **架构一致性**: 严格遵循多层架构，保持代码结构清晰
- **性能优先**: 优化数据库查询，合理使用缓存和消息队列
- **安全第一**: 防范SQL注入、XSS攻击等安全威胁
- **可维护性**: 控制代码复杂度，减少重复代码

---

## 📏 统一代码限制标准 (Unified Code Limit Standards)

### 🎯 最新统一规范
```yaml
# WMS项目代码限制标准 (v2.0统一版本)
代码限制:
  单个方法:
    最大行数: 100行
    最大复杂度: 10
    
  类级别限制:
    Service层: 2000行      # 适应复杂业务逻辑
    Controller层: 1000行   # 控制接口数量  
    DAO层: 1500行         # 平衡数据访问复杂度
    普通类: 1000行        # 通用类标准
    
  现代Java特性:
    Stream链: 最多5个操作
    Lambda表达式: 最多3行
```

### 🔧 拆分策略建议
- **方法拆分**: 超过100行的方法必须拆分为多个私有方法
- **类拆分**: 超过限制的类按业务功能或职责拆分
- **功能分离**: 验证、计算、通知等逻辑分离到专门的服务类

---

## 🚫 关键禁止事项 (Key Prohibitions)

### 🔴 核心禁止（严重错误）
1. ❌ **Controller直接调用DAO** - 必须通过Service层
2. ❌ **使用@Transactional注解** - 必须依赖AOP自动事务管理
3. ❌ **硬编码字段名** - 必须使用 `EntityDBField` 常量
4. ❌ **手动声明Logger** - 必须使用 `@Slf4j` 注解
5. ❌ **使用@Autowired注解** - 必须使用 `@Resource` 注解
6. ❌ **单个方法超过100行** - 必须拆分为更小的方法
7. ❌ **Service类超过2000行** - 必须拆分为更小的服务类
8. ❌ **Controller类超过1000行** - 必须拆分功能模块
9. ❌ **DAO类超过1500行** - 必须按业务域拆分
10. ❌ **字符串拼接SQL** - 必须使用参数化查询，防止SQL注入
11. ❌ **忽略空值检查** - 必须使用 `Objects.isNull()` 和 `CollectionUtils.isEmpty()`
12. ❌ **过度复杂的Stream链** - 单个Stream操作不超过5个步骤
13. ❌ **Stream中进行副作用操作** - 避免在Stream中修改状态或执行IO操作
14. ❌ **Lambda表达式超过3行** - 复杂逻辑应提取为私有方法

**完整禁止事项和详细规范**: 参考 [AI快速参考](mdc:wms-dev-rules-simplified.mdc)

---

## ✅ 快速检查清单 (Quick Checklist)

### 基础检查项
- [ ] 遵循多层架构设计原则
- [ ] 使用自定义SQL映射框架
- [ ] 添加适当的缓存策略
- [ ] 编写规范的异常处理代码
- [ ] 添加详细的日志记录

### 代码质量检查项
- [ ] 单个方法不超过100行
- [ ] 方法复杂度不超过10
- [ ] Service类不超过2000行
- [ ] Controller类不超过1000行
- [ ] DAO类不超过1500行
- [ ] 普通类不超过1000行

### 现代Java特性检查项
- [ ] Stream链不超过5个操作
- [ ] Lambda表达式不超过3行
- [ ] Optional正确使用，避免过度包装
- [ ] 避免在Stream中进行副作用操作
- [ ] 优先使用方法引用而非Lambda表达式
- [ ] 现代特性提升而非降低代码可读性

### 代码基础检查项
- [ ] @Repository注解 | @Repository annotation
- [ ] setQueryCondition()方法 | setQueryCondition() method  
- [ ] DBField常量 | DBField constants
- [ ] DataType枚举 | DataType enumeration
- [ ] 空值检查 | Null value checks
- [ ] @Slf4j注解 | @Slf4j annotation
- [ ] 异常处理 | Exception handling
- [ ] 方法注释 | Method comments

### 事务检查项 | Transaction Checks
- [ ] 禁止使用@Transactional注解 | Prohibit @Transactional annotation
- [ ] 方法命名符合事务规则 | Method naming follows transaction rules
- [ ] 查询方法使用get/search/query/find/list/count前缀 | Query methods use proper prefixes
- [ ] 写操作方法使用save/add/insert/create/update/delete/batch/do前缀 | Write methods use proper prefixes
- [ ] 依赖AOP自动事务管理 | Rely on AOP automatic transaction management

**完整检查清单**: 参考 [AI快速参考](mdc:wms-dev-rules-simplified.mdc)

---

## 🎓 学习路径建议 (Learning Path)

### 新手开发者
1. 阅读 [项目架构规范](mdc:../docs/wms-architecture-rules-zh.md) 了解整体架构
2. 学习 [核心编码规范](mdc:../docs/wms-coding-rules-zh.md) 掌握编码标准
3. 实践 [DAO层开发规范](mdc:../docs/dao-layer-coding-rules.md) 进行数据访问开发
4. 使用 [AI快速参考](mdc:wms-dev-rules-simplified.mdc) 进行日常开发

### 经验开发者
1. 直接使用 [AI快速参考](mdc:wms-dev-rules-simplified.mdc) 进行快速开发
2. 参考 [扩展开发规范](mdc:wms-fx-rules.mdc) 处理特殊需求
3. 使用 [使用指南](mdc:../docs/cursor-usage-guide.md) 提高开发效率

### 前端开发者
1. 重点学习 [前端页面规范](mdc:wms-page-rules.mdc)
2. 配合 [扩展开发规范](mdc:wms-fx-rules.mdc) 进行前后端分离开发

---

**注意：本文档为主导航文档，具体开发规范请参考对应的专门文档！**

**项目**: WMS仓库管理系统 | **版本**: v1.0 | **作者**: Amoi









